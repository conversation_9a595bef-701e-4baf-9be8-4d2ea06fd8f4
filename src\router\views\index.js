import Layout from "@/page/index/";

export default [
  {
    path: "/wel",
    component: Layout,
    redirect: "/wel/index",
    children: [
      {
        path: "index",
        name: "首页",
        meta: {
          i18n: "dashboard",
        },
        component: () =>
          import(/* webpackChunkName: "views" */ "@/views/wel/index"),
      },
      {
        path: "dashboard",
        name: "控制台",
        meta: {
          i18n: "dashboard",
          menu: false,
        },
        component: () =>
          import(/* webpackChunkName: "views" */ "@/views/wel/dashboard"),
      },
    ],
  },
  // {
  //   path: '/bdface',
  //   name: 'face',
  //   component: () =>
  //     import( /* webpackChunkName: "page" */ '@/page/lock/face'),
  //   meta: {
  //     keepAlive: false,
  //     isTab: false,
  //     isAuth: false
  //   }
  // },
  {
    path: "/test",
    component: Layout,
    redirect: "/test/index",
    children: [
      {
        path: "index",
        name: "测试页",
        meta: {
          i18n: "test",
        },
        component: () =>
          import(/* webpackChunkName: "views" */ "@/views/util/test"),
      },
    ],
  },
  {
    path: "/dict-horizontal",
    component: Layout,
    redirect: "/dict-horizontal/index",
    children: [
      {
        path: "index",
        name: "字典管理",
        meta: {
          i18n: "dict",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/util/demo/dict-horizontal"
          ),
      },
    ],
  },
  {
    path: "/dict-vertical",
    component: Layout,
    redirect: "/dict-vertical/index",
    children: [
      {
        path: "index",
        name: "字典管理",
        meta: {
          i18n: "dict",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/util/demo/dict-vertical"
          ),
      },
    ],
  },
  {
    path: "/info",
    component: Layout,
    redirect: "/info/index",
    children: [
      {
        path: "index",
        name: "个人信息",
        meta: {
          i18n: "info",
        },
        component: () =>
          import(/* webpackChunkName: "views" */ "@/views/system/userinfo"),
      },
    ],
  },
  {
    path: "/work/process/leave",
    component: Layout,
    redirect: "/work/process/leave/form",
    children: [
      {
        path: "form/:processDefinitionId",
        name: "请假流程",
        meta: {
          i18n: "work",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/work/process/leave/form"
          ),
      },
      {
        path: "handle/:taskId/:processInstanceId/:businessId",
        name: "处理请假流程",
        meta: {
          i18n: "work",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/work/process/leave/handle"
          ),
      },
      {
        path: "detail/:processInstanceId/:businessId",
        name: "请假流程详情",
        meta: {
          i18n: "work",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/work/process/leave/detail"
          ),
      },
    ],
  },

  {
    path: "/entrustedcompany",
    component: Layout,
    redirect: "/entrustedcompany/entrustedCompany",
    children: [
      {
        path: "detail/:id/:tabindex",
        name: "企业会员管理详情",
        meta: {
          activeMenu: "/entrustedcompany/entrustedCompany",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/entrustedcompany/entrustedcompanyDetail"
          ),
      },
      {
        path: "add/",
        name: "企业会员注册",
        meta: {
          activeMenu: "/entrustedcompany/entrustedCompany",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/entrustedcompany/entrustedcompanyAdd"
          ),
      },
      {
        path: "edit/:id",
        name: "企业会员管理编辑",
        meta: {
          activeMenu: "/entrustedcompany/entrustedCompany",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/entrustedcompany/entrustedcompanyEdit"
          ),
      },
    ],
  },
  // 预览文件
  {
    path: "/preview",
    component: Layout,
    redirect: "/preview/preview",
    children: [
      {
        path: "preview",
        name: "预览",
        meta: {},
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/previewFile/previewFile"
          ),
      },
    ],
  },
  // 企业服务结算单
  {
    path: "/settlementOrderDoctor",
    component: Layout,
    redirect: "/settlementOrderDoctor/settlementOrderDoctor",
    children: [
      {
        path: "detail/:id",
        name: "企业服务结算单详情",
        meta: {
          activeMenu: "/settlementOrderDoctor/settlementOrderDoctor",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/settlementOrderDoctor/settlementOrderDoctorDetail"
          ),
      },
    ],
  },

  // 企业积分结算单
  {
    path: "/settlementOrderDoctorPoint",
    component: Layout,
    redirect: "/settlementOrderDoctorPoint/settlementOrderDoctorPoint",
    children: [
      {
        path: "detail/:id",
        name: "企业积分服务结算单详情",
        meta: {
          activeMenu: "/settlementOrderDoctorPoint/settlementOrderDoctorPoint",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/settlementOrderDoctorPoint/settlementOrderDoctorPointDetail"
            ),
      },
    ],
  },

  // 企业周期结算单
  {
    path: "/settlementCycle",
    component: Layout,
    redirect: "/settlementCycle/settlementCycle",
    children: [
      {
        path: "detail/:id",
        name: "企业周期结算单详情",
        meta: {
          activeMenu: "/settlementCycle/settlementCycle",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/settlementCycle/settlementCycleDetail"
          ),
      },
    ],
  },
  // 企业服务资金台账
  {
    path: "/servicepaymentorder",
    component: Layout,
    redirect: "/servicepaymentorder/servicePaymentOrder",
    children: [
      {
        path: "detail/:id",
        name: "企业资金支付详情",
        meta: {
          activeMenu: "/servicepaymentorder/servicePaymentOrder",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/servicepaymentorder/servicePaymentOrderDetail"
          ),
      },
    ],
  },
  {
    path: "/tenderDoctorProtocols",
    component: Layout,
    redirect: "/tenderDoctorProtocols/tenderDoctorProtocols",
    children: [
      {
        path: "detail/:id",
        name: "平台个人承包合同详情",
        meta: {
          activeMenu: "/tenderDoctorProtocols/tenderDoctorProtocols",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/tenderDoctorProtocols/tenderDoctorProtocolsDetail"
          ),
      },
    ],
  },
  {
    path: "/tenderentrustedprotocols",
    component: Layout,
    redirect: "/tenderentrustedprotocols/tenderEntrustedProtocols",
    children: [
      {
        path: "detail/:id",
        name: "企业平台委托合同详情",
        meta: {
          activeMenu: "/tenderentrustedprotocols/tenderEntrustedProtocols",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/tenderentrustedprotocols/tenderEntrustedProtocolsDetail"
          ),
      },
    ],
  },
  // 会员认证管理
  {
    path: "/customercertificationrecord",
    component: Layout,
    redirect: "/customercertificationrecord/customerCertificationRecord",
    children: [
      {
        path: "detail/:id",
        name: "企业会员认证详情",
        meta: {
          activeMenu:
            "/customercertificationrecord/customerCertificationRecord",
        },
        component: () =>
          import(
            "@/views/customercertificationrecord/customercertificationrecordDetail"
          ),
      },
      {
        path: "authenticationDoctorDetail/:id",
        name: "自然人会员认证详情",
        meta: {
          activeMenu:
            "/customercertificationrecord/customerCertificationRecord",
        },
        component: () =>
          import(
            "@/views/customercertificationrecord/customercertificationrecordDetailDoctor"
          ),
      },
    ],
  },
  //企业服务发票单
  {
    path: "/entrustedInvoice",
    component: Layout,
    redirect: "/entrustedInvoice/entrustedInvoice",
    children: [
      {
        path: "detail/:id",
        name: "企业服务发票单详情",
        meta: {
          activeMenu: "/entrustedInvoice/entrustedInvoice",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/entrustedInvoice/entrustedInvoiceDetail"
          ),
      },
      {
        path: "edit/:id",
        name: "上传企业服务发票",
        meta: {
          activeMenu: "/entrustedInvoice/entrustedInvoice",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/entrustedInvoice/entrustedInvoiceEdit"
          ),
      },
    ],
  },
  //企业积分发票单
  {
    path: "/entrustedInvoicePoint",
    component: Layout,
    redirect: "/entrustedInvoicePoint/entrustedInvoicePoint",
    children: [
      {
        path: "detail/:id",
        name: "企业积分发票单详情",
        meta: {
          activeMenu: "/entrustedInvoicePoint/entrustedInvoicePoint",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/entrustedInvoicePoint/entrustedInvoicePointDetail"
            ),
      },
      {
        path: "edit/:id",
        name: "上传企业服务发票",
        meta: {
          activeMenu: "/entrustedInvoicePoint/entrustedInvoicePoint",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/entrustedInvoicePoint/entrustedInvoicePointEdit"
            ),
      },
    ],
  },
  //个人代征申报单
  {
    path: "/summaryInvoice",
    component: Layout,
    redirect: "/summaryInvoice/summaryInvoice",
    children: [
      {
        path: "detail/:id",
        name: "自然人代征申报单详情",
        meta: {
          activeMenu: "/summaryInvoice/summaryInvoice",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/summaryInvoice/summaryInvoiceDetail"
          ),
      },
      {
        path: "edit/:id",
        name: "自然人代征申报单编辑",
        meta: {
          activeMenu: "/summaryInvoice/summaryInvoice",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/summaryInvoice/summaryInvoiceEdit"
          ),
      },
    ],
  },
  //个人完税单
  {
    path: "/summarytaxcert",
    component: Layout,
    redirect: "/summarytaxcert/summaryTaxCert",
    children: [
      {
        path: "detail/:id",
        name: "自然人代征完税单详情",
        meta: {
          activeMenu: "/summarytaxcert/summaryTaxCert",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/summarytaxcert/summaryTaxCertDetail"
          ),
      },
      {
        path: "edit/:id",
        name: "自然人代征完税单编辑",
        meta: {
          activeMenu: "/summarytaxcert/summaryTaxCert",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/summarytaxcert/summaryTaxCertEdit"
          ),
      },
    ],
  },

  // 需求方发票单开票项明细表
  {
    path: "/entrustedInvoiceItem",
    component: Layout,
    redirect: "",
    children: [
      {
        path: "entrustedInvoiceItem",
        name: "需求方发票单开票项明细表",
        meta: {
          i18n: "dict",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/entrustedInvoiceItem/entrustedInvoiceItem"
          ),
      },
    ],
  },

  {
    path: "/summaryInvoicedetail",
    component: Layout,
    redirect: "",
    children: [
      {
        path: "summaryInvoiceDetail",
        name: "自然人代开发票单明细",
        meta: {
          i18n: "dict",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/summaryInvoicedetail/summaryInvoiceDetail"
          ),
      },
    ],
  },

  {
    path: "/summarytaxcertdetail",
    component: Layout,
    redirect: "",
    children: [
      {
        path: "summaryTaxCertDetail",
        name: "自然人完税单明细",
        meta: {
          i18n: "dict",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/summarytaxcertdetail/summaryTaxCertDetail"
          ),
      },
    ],
  },
  // 意见反馈
  {
    path: "/feedback",
    component: Layout,
    redirect: "/feedback/feedback",
    children: [
      {
        path: "detail/:id",
        name: "意见反馈详情",
        meta: {},
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/feedback/feedbackDetail"
          ),
      },
    ],
  },
  // 个人会员管理
  {
    path: "/authenticationDoctor",
    component: Layout,
    redirect: "/authenticationDoctor/authenticationDoctor",
    children: [
      {
        path: "detail/:id/:tabindex",
        name: "自然人会员管理详情",
        meta: {
          activeMenu: "/authenticationDoctor/authenticationDoctor",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/authenticationDoctor/authenticationDoctorDetail"
          ),
      },
      {
        path: "edit/:id",
        name: "自然人会员管理编辑",
        meta: {
          activeMenu: "/authenticationDoctor/authenticationDoctor",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/authenticationDoctor/authenticationDoctorEdit"
          ),
      },
    ],
  },

  // 病例征集-活动数据收集
  {
    path: "/caseCollectionReport",
    component: Layout,
    redirect: "/caseCollectionReport/caseCollectionReport",
    children: [
      {
        path: "detail/:id",
        name: "病例信息征集详情",
        meta: {
          activeMenu: "/caseCollectionReport/caseCollectionReport",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/caseCollectionReport/caseCollectionReportDetail"
          ),
      },
    ],
  },
  // 演示病例征集-活动数据收集
  {
    path: "/demoCaseCollectionReport",
    component: Layout,
    redirect: "/demoCaseCollectionReport/demoCaseCollectionReport",
    children: [
      {
        path: "detail/:id",
        name: "病例信息征集详情",
        meta: {
          activeMenu: "/demoCaseCollectionReport/demoCaseCollectionReport",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/demoCaseCollectionReport/demoCaseCollectionReportDetail"
            ),
      },
    ],
  },
  // 患者教育-参与活动数据
  {
    path: "/patientEducationReport",
    component: Layout,
    redirect: "/patientEducationReport/patientEducationReport",
    children: [
      {
        path: "detail/:id",
        name: "患者教育活动详情",
        meta: {},
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/patientEducationReport/patientEducationReportDetail"
          ),
      },
    ],
  },
  // 医患服务总成果单
  {
    path: "/resultOrder",
    component: Layout,
    redirect: "/resultOrder/resultOrder",
    children: [
      {
        path: "detail/:id",
        name: "企业成果单详情",
        meta: {
          activeMenu: "/resultorder/resultOrder",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/resultorder/resultOrderDetail"
          ),
      },
      {
        path: "add",
        name: "企业成果单新增",
        meta: {
          activeMenu: "/resultorder/resultOrder",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/resultorder/resultOrderAdd"
          ),
      },
    ],
  },
  // 医患服务积分总成果单
  {
    path: "/resultOrderPoint",
    component: Layout,
    redirect: "/resultOrderPoint/resultOrderPoint",
    children: [
      {
        path: "detail/:id",
        name: "企业积分成果单详情",
        meta: {
          activeMenu: "/resultorderPoint/resultOrderPoint",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/resultorderPoint/resultOrderPointDetail"
            ),
      },
      {
        path: "add",
        name: "企业积分成果单新增",
        meta: {
          activeMenu: "/resultorderPoint/resultOrderPoint",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/resultorder/resultOrderAdd"
            ),
      },
    ],
  },
  //
  {
    path: "/resultOrderDetail",
    component: Layout,
    redirect: "/resultorderdetail/resultOrderDetail",
    children: [
      {
        path: "detail/:id",
        name: "自然人成果单详情",
        meta: {
          activeMenu: "/resultorderdetail/resultOrderDetail",
        },
        component: () =>
          import("@/views/resultorderdetail/resultOrderDetailInner"),
      },
    ],
  },
  //积分流水明细
  {
    path: "/integralDetail",
    component: Layout,
    redirect: "/integralDetail/integralDetail",
    children: [
      {
        path: "integralDetail/:id",
        name: "积分流水明细",
        meta: {},
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/integralDetail/integralDetail"
          ),
      },
    ],
  },
  // 活动计划
  {
    path: "/activityPlan",
    component: Layout,
    redirect: "/activityPlan/activityPlan",
    children: [
      {
        path: "caseCollectionAdd/",
        name: "病例征集计划任务新增",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/caseCollectionAdd"),
      },

      {
        path: "caseCollectionEdit/:id",
        name: "病例征集计划任务编辑",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/caseCollectionEdit"),
      },

      {
        path: "caseCollectionView/:id",
        name: "病例征集计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/caseCollectionDetail"),
      },

      {
        path: "medicationFeedbackAdd/",
        name: "用药反馈计划任务新增",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/medicationFeedbackAdd"),
      },
      {
        path: "medicationFeedbackEdit/:id",
        name: "用药反馈计划任务编辑",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/medicationFeedbackEdit"),
      },
      {
        path: "medicationFeedbackView/:id",
        name: "用药反馈计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () =>
          import("@/views/activityPlan/medicationFeedbackDetail"),
      },
      //临床调研
      {
        path: "clinicalResearchAdd/",
        name: "临床调研计划任务新增",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/clinicalResearchAdd"),
      },
      {
        path: "clinicalResearchEdit/:id",
        name: "临床调研计划任务编辑",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/clinicalResearchEdit"),
      },
      {
        path: "clinicalResearchView/:id",
        name: "临床调研计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/clinicalResearchDetail"),
      },
      {
        path: "clinicalResearchPointView/:id",
        name: "临床调研计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/clinicalResearchDetail"),
      },
      //学术会议
      {
        path: "meetingEdit/:id",
        name: "学术会议计划任务编辑",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/meetingEdit"),
      },
      {
        path: "meetingView/:id",
        name: "学术会议计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/meetingDetail"),
      },
      //知识创作
      {
        path: "lectureView/:id",
        name: "知识创作计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () => import("@/views/activityPlan/lectureDetail"),
      },
      //专业审批详情
      {
        path: "professionalReviewView/:id",
        name: "专业审批计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlan",
        },
        component: () =>
          import("@/views/activityPlan/professionalReviewDetail"),
      },
    ],
  },
  // 活动计划
  {
    path: "/activityPlanPoint",
    component: Layout,
    redirect: "/activityPlan/activityPlanPoint",
    children: [
      {
        path: "caseCollectionAdd/",
        name: "病例征集计划任务新增",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/caseCollectionAdd"),
      },

      {
        path: "caseCollectionEdit/:id",
        name: "病例征集计划任务编辑",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/caseCollectionEdit"),
      },

      {
        path: "caseCollectionView/:id",
        name: "病例征集计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/caseCollectionDetail"),
      },

      {
        path: "medicationFeedbackAdd/",
        name: "用药反馈计划任务新增",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/medicationFeedbackAdd"),
      },
      {
        path: "medicationFeedbackEdit/:id",
        name: "用药反馈计划任务编辑",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/medicationFeedbackEdit"),
      },
      {
        path: "medicationFeedbackView/:id",
        name: "用药反馈计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () =>
          import("@/views/activityPlan/medicationFeedbackDetail"),
      },
      //临床调研
      {
        path: "clinicalResearchAdd/",
        name: "临床调研计划任务新增",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/clinicalResearchAdd"),
      },
      {
        path: "clinicalResearchEdit/:id",
        name: "临床调研计划任务编辑",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/clinicalResearchEdit"),
      },
      {
        path: "clinicalResearchView/:id",
        name: "临床调研计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/clinicalResearchDetail"),
      },
      {
        path: "clinicalResearchPointView/:id",
        name: "临床调研计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/clinicalResearchDetail"),
      },
      //学术会议
      {
        path: "meetingEdit/:id",
        name: "学术会议计划任务编辑",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/meetingEdit"),
      },
      {
        path: "meetingView/:id",
        name: "学术会议计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/meetingDetail"),
      },
      //知识创作
      {
        path: "lectureView/:id",
        name: "知识创作计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () => import("@/views/activityPlan/lectureDetail"),
      },
      //专业审批详情
      {
        path: "professionalReviewView/:id",
        name: "专业审批计划任务详情",
        meta: {
          activeMenu: "/activityPlan/activityPlanPoint",
        },
        component: () =>
          import("@/views/activityPlan/professionalReviewDetail"),
      },
    ],
  },
    // 演示活动计划
  {
    path: "/demoActivityPlan",
    component: Layout,
    redirect: "/demoActivityPlan/demoActivityPlan",
    children: [
      {
        path: "demoCaseCollectionAdd/",
        name: "病例征集计划任务新增",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoCaseCollectionAdd"),
      },

      {
        path: "demoCaseCollectionEdit/:id",
        name: "病例征集计划任务编辑",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoCaseCollectionEdit"),
      },

      {
        path: "demoCaseCollectionView/:id",
        name: "病例征集计划任务详情",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoCaseCollectionDetail"),
      },

      {
        path: "demoMedicationFeedbackAdd/",
        name: "用药反馈计划任务新增",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoMedicationFeedbackAdd"),
      },
      {
        path: "demoMedicationFeedbackEdit/:id",
        name: "用药反馈计划任务编辑",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoMedicationFeedbackEdit"),
      },
      {
        path: "demoMedicationFeedbackView/:id",
        name: "用药反馈计划任务详情",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () =>
          import("@/views/demoActivityPlan/demoMedicationFeedbackDetail"),
      },
      //临床调研
      {
        path: "demoClinicalResearchAdd/",
        name: "临床调研计划任务新增",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoClinicalResearchAdd"),
      },
      {
        path: "demoClinicalResearchEdit/:id",
        name: "临床调研计划任务编辑",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoClinicalResearchEdit"),
      },
      {
        path: "demoClinicalResearchView/:id",
        name: "临床调研计划任务详情",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoClinicalResearchDetail"),
      },
      //学术会议
      {
        path: "demoMeetingAdd",
        name: "学术会议计划任务新增",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoMeetingAdd"),
      },
      {
        path: "demoMeetingEdit/:id",
        name: "学术会议计划任务编辑",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoMeetingEdit"),
      },
      {
        path: "demoMeetingView/:id",
        name: "学术会议计划任务详情",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoMeetingDetail"),
      },
      //知识创作
      {
        path: "demoLectureAdd",
        name: "知识创作计划任务详情",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoLectureAdd"),
      },
      {
        path: "demoLectureEdit/:id",
        name: "知识创作计划任务详情",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoLectureEdit"),
      },
      {
        path: "demoLectureView/:id",
        name: "知识创作计划任务详情",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoLectureDetail"),
      },
      // 专业评审
      {
        path: "demoProfessionalReviewAdd/",
        name: "专业审批计划任务新增",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoProfessionalReviewAdd.vue"),
      },
      {
        path: "demoProfessionalReviewEdit/:id",
        name: "专业审批计划任务编辑",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () => import("@/views/demoActivityPlan/demoProfessionalReviewEdit.vue"),
      },
      //专业审批详情
      {
        path: "demoProfessionalReviewView/:id",
        name: "专业审批计划任务详情",
        meta: {
          activeMenu: "/demoActivityPlan/demoActivityPlan",
        },
        component: () =>
          import("@/views/demoActivityPlan/demoProfessionalReviewDetail"),
      },
    ],
  },
  // 病例征集模板
  {
    path: "/casecollectiontemplate",
    component: Layout,
    redirect: "/casecollectiontemplate/caseCollectionTemplate",
    children: [
      {
        path: "add/",
        name: "病例征集模板新增",
        meta: {
          activeMenu: "/casecollectiontemplate/caseCollectionTemplate",
        },
        component: () =>
          import("@/views/casecollectiontemplate/caseCollectionTemplateAdd"),
      },
      {
        path: "edit/:id",
        name: "病例征集模板编辑",
        meta: {
          activeMenu: "/casecollectiontemplate/caseCollectionTemplate",
        },
        component: () =>
          import("@/views/casecollectiontemplate/caseCollectionTemplateEdit"),
      },
      {
        path: "detail/:id",
        name: "病例征集模板详情",
        meta: {
          activeMenu: "/casecollectiontemplate/caseCollectionTemplate",
        },
        component: () =>
          import("@/views/casecollectiontemplate/caseCollectionTemplateDetail"),
      },
    ],
  },
  // 服务方案
  {
    path: "/activityServiceProject",
    component: Layout,
    redirect: "/activityserviceproject/activityServiceProject",
    children: [
      {
        path: "add/",
        name: "服务方案新增",
        meta: {
          activeMenu: "/activityserviceproject/activityServiceProject",
        },
        component: () =>
          import("@/views/activityserviceproject/activityServiceProjectAdd"),
      },
      {
        path: "edit/:id",
        name: "服务方案编辑",
        meta: {
          activeMenu: "/activityserviceproject/activityServiceProject",
        },
        component: () =>
          import("@/views/activityserviceproject/activityServiceProjectEdit"),
      },
      {
        path: "detail/:id",
        name: "服务方案详情",
        meta: {
          activeMenu: "/activityserviceproject/activityServiceProject",
        },
        component: () =>
          import("@/views/activityserviceproject/activityServiceProjectDetail"),
      },
    ],
  },

  //医患服务预算
  {
    path: "/budgetorder",
    component: Layout,
    redirect: "/budgetorder/budgetOrder",
    children: [
      {
        path: "detail/:id",
        name: "企业服务预算详情",
        meta: {
          activeMenu: "/budgetorder/budgetOrder",
        },
        component: () => import("@/views/budgetorder/budgetOrderDetail"),
      },
    ],
  },

  //平台服务差额收入
  {
    path: "/platServiceFee",
    component: Layout,
    redirect: "/platServiceFee/platServiceFee",
    children: [
      {
        path: "detail/:id/:settlementOrderId",
        name: "平台服务净收入详情",
        meta: {
          activeMenu: "/platServiceFee/platServiceFee",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/platServiceFee/platServiceFeeDetail"
          ),
      },
    ],
  },

  //平台服务积分差额收入
  {
    path: "/platServiceFeePoint",
    component: Layout,
    redirect: "/platServiceFeePoint/platServiceFeePoint",
    children: [
      {
        path: "detail/:id/:settlementOrderId",
        name: "平台服务净收入详情",
        meta: {
          activeMenu: "/platServiceFeePoint/platServiceFeePoint",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/platServiceFeePoint/platServiceFeePointDetail"
            ),
      },
    ],
  },
  //代征代缴税款支付

  {
    path: "/entrustedTaxPaymentOrder",
    component: Layout,
    redirect: "/entrustedtaxpaymentorder/entrustedTaxPaymentOrder",
    children: [
      {
        path: "detail/:id",
        name: "代征代缴税款支付详情",
        meta: {
          activeMenu: "/entrustedtaxpaymentorder/entrustedTaxPaymentOrder",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/entrustedtaxpaymentorder/entrustedTaxPaymentOrderDetail"
          ),
      },
    ],
  },
  //资金变动明细
  {
    path: "/entrustedaccount",
    component: Layout,
    redirect: "",
    children: [
      {
        path: "entrustedAccountRecord/:id",
        name: "资金变动明细",
        meta: {
          i18n: "dict",
          activeMenu: "/entrustedaccount/entrustedAccount",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/entrustedaccountrecord/entrustedAccountRecord"
          ),
      },
    ],
  },
  //个人订单管理
  {
    path: "/orderdoctor",
    component: Layout,
    redirect: "/orderdoctor/orderDoctor",
    children: [
      {
        path: "detail/:id",
        name: "会员订单详情",
        meta: {
          activeMenu: "/orderdoctor/orderDoctor",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/orderdoctor/orderDoctorDetail"
          ),
      },
    ],
  },
  //企业总包订单
  {
    path: "/orderentrusted",
    component: Layout,
    redirect: "/orderentrusted/orderEntrusted",
    children: [
      {
        path: "detail/:id",
        name: "企业客户订单详情",
        meta: {
          activeMenu: "/orderentrusted/orderEntrusted",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/orderentrusted/orderEntrustedDetail"
          ),
      },
    ],
  },
  //临床调研问卷题库
  {
    path: "/clinicalresearchtemplate",
    component: Layout,
    redirect: "/clinicalresearchtemplate/clinicalResearchTemplate",
    children: [
      {
        path: "add",
        name: "临床调研问卷模板新增",
        meta: {
          activeMenu: "/clinicalresearchtemplate/clinicalResearchTemplate",
        },
        component: () =>
          import(
            "@/views/clinicalresearchtemplate/clinicalResearchTemplateAdd"
          ),
      },
      {
        path: "edit/:id",
        name: "临床调研问卷模板编辑",
        meta: {
          activeMenu: "/clinicalresearchtemplate/clinicalResearchTemplate",
        },
        component: () =>
          import(
            "@/views/clinicalresearchtemplate/clinicalResearchTemplateEdit"
          ),
      },
      {
        path: "detail/:id",
        name: "临床调研问卷模板详情",
        meta: {
          activeMenu: "/clinicalresearchtemplate/clinicalResearchTemplate",
        },
        component: () =>
          import(
            "@/views/clinicalresearchtemplate/clinicalResearchTemplateDetail"
          ),
      },
    ],
  },
  //用药反馈
  {
    path: "/medicationFeedbackReport",
    component: Layout,
    redirect: "/medicationFeedbackReport/medicationFeedbackReport",
    children: [
      {
        path: "detail/:id",
        name: "用药反馈详情",
        meta: {
          activeMenu: "/medicationFeedbackReport/medicationFeedbackReport",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/medicationFeedbackReport/medicationFeedbackReportDetail"
          ),
      },
    ],
  },
  //演示用药反馈
  {
    path: "/demoMedicationFeedbackReport",
    component: Layout,
    redirect: "/demoMedicationFeedbackReport/demoMedicationFeedbackReport",
    children: [
      {
        path: "detail/:id",
        name: "用药反馈详情",
        meta: {
          activeMenu: "/demoMedicationFeedbackReport/demoMedicationFeedbackReport",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/demoMedicationFeedbackReport/demoMedicationFeedbackReportDetail"
            ),
      },
    ],
  },

  //临床调研
  {
    path: "/clinicalResearchReport",
    component: Layout,
    redirect: "/clinicalresearchreport/clinicalResearchReport",
    children: [
      {
        path: "detail/:id",
        name: "临床调研详情",
        meta: {
          activeMenu: "/clinicalresearchreport/clinicalResearchReport",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/clinicalresearchreport/clinicalResearchReportDetail"
          ),
      },
    ],
  },
  //演示临床调研
  {
    path: "/demoClinicalResearchReport",
    component: Layout,
    redirect: "/demoClinicalresearchreport/demoClinicalResearchReport",
    children: [
      {
        path: "detail/:id",
        name: "临床调研详情",
        meta: {
          activeMenu: "/demoClinicalresearchreport/demoClinicalResearchReport",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/demoClinicalresearchreport/demoClinicalResearchReportDetail"
            ),
      },
    ],
  },
  //个人结算单详情
  {
    path: "/settlementOrderDoctorDetailOut",
    component: Layout,
    redirect: "/settlementOrderDoctorDetailOut/settlementOrderDoctorDetailOut",
    children: [
      {
        path: "detail/:id",
        name: "自然人结算单详情",
        meta: {
          activeMenu:
            "/settlementOrderDoctorDetailOut/settlementOrderDoctorDetailOut",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/settlementOrderDoctorDetailOut/settlementOrderDoctorDetailOutDetail"
          ),
      },
    ],
  },
  //会员订单结算单详情
  {
    path: "/orderdoctosettlement",
    component: Layout,
    redirect: "/orderdoctosettlement/orderDoctorSettlement",
    children: [
      {
        path: "detail/:id",
        name: "会员订单结算单详情",
        meta: {
          activeMenu: "/orderdoctosettlement/orderDoctorSettlement",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/orderdoctosettlement/orderDoctorSettlementDetail"
          ),
      },
      {
        path: "blackList",
        name: "黑名单列表",
        meta: {
          activeMenu: "/orderdoctosettlement/orderDoctorSettlement",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/system/userBlacklist.vue"
            ),
      },
    ],
  },
  //会员积分结算单详情
  {
    path: "/orderdoctosettlementpoint",
    component: Layout,
    redirect: "/orderdoctosettlementpoint/orderDoctorSettlementPoint",
    children: [
      {
        path: "detail/:id",
        name: "会员积分结算单详情",
        meta: {
          activeMenu: "/orderdoctosettlementpoint/orderDoctorSettlementPoint",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/orderdoctosettlementpoint/orderDoctorSettlementPointDetail"
            ),
      },
      {
        path: "blackList",
        name: "黑名单列表",
        meta: {
          activeMenu: "/orderdoctosettlementpoint/orderDoctorSettlementPoint",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/system/userBlacklist.vue"
            ),
      },
    ],
  },
  //会员订单支付单详情
  {
    path: "/orderdoctorpayment",
    component: Layout,
    redirect: "/orderdoctorpayment/orderDoctorPayment",
    children: [
      {
        path: "detail/:id",
        name: "会员订单支付单详情",
        meta: {
          activeMenu: "/orderdoctorpayment/orderDoctorPayment",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/orderdoctorpayment/orderDoctorPaymentDetail"
          ),
      },
    ],
  },
  //会员积分发放单详情
  {
    path: "/orderdoctorpaymentpoint",
    component: Layout,
    redirect: "/orderdoctorpaymentpoint/orderDoctorPaymentPoint",
    children: [
      {
        path: "detail/:id",
        name: "会员积分发放单详情",
        meta: {
          activeMenu: "/orderdoctorpaymentpoint/orderDoctorPaymentPoint",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/orderdoctorpaymentpoint/orderDoctorPaymentPointDetail"
            ),
      },
    ],
  },

  // 会议营销活动
  {
    path: "/meeting",
    component: Layout,
    redirect: "/meeting/meeting",
    children: [
      {
        path: "detail/:id",
        name: "学术会议详情",
        meta: {
          activeMenu: "/meeting/meeting",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/meeting/meetingDetail"
          ),
      },
    ],
  },
  // 演示会议营销活动
  {
    path: "/demoMeeting",
    component: Layout,
    redirect: "/demoMeeting/demoMeeting",
    children: [
      {
        path: "detail/:id",
        name: "学术会议详情",
        meta: {
          activeMenu: "/demoMeeting/demoMeeting",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/demoMeeting/demoMeetingDetail"
            ),
      },
    ],
  },
  //大讲堂
  {
    path: "/lecture_1",
    component: Layout,
    redirect: "/lecture/lecture_1",
    children: [
      {
        path: "detail/:id",
        name: "大讲堂详情",
        meta: {
          activeMenu: "/lecture/lecture_1",
        },
        component: () => import("@/views/lecture/lecture_1Detail"),
      },
    ],
  },
  //演示大讲堂
  {
    path: "/demoLecture_1",
    component: Layout,
    redirect: "/lecture/lecture_1",
    children: [
      {
        path: "detail/:id",
        name: "大讲堂详情",
        meta: {
          activeMenu: "/demoLecture/demoLecture_1",
        },
        component: () => import("@/views/demoLecture/demoLecture_1Detail"),
      },
    ],
  },
  //专业录播详情
  {
    path: "/lecture_2",
    component: Layout,
    redirect: "/lecture/lecture_2",
    children: [
      {
        path: "detail/:id",
        name: "专业录播详情",
        meta: {
          activeMenu: "/lecture/lecture_2",
        },
        component: () => import("@/views/lecture/lecture_2Detail"),
      },
    ],
  },
  //演示专业录播详情
  {
    path: "/demoLecture_2",
    component: Layout,
    redirect: "/demoLecture/demoLecture_2",
    children: [
      {
        path: "detail/:id",
        name: "专业录播详情",
        meta: {
          activeMenu: "/demoLecture/demoLecture_2",
        },
        component: () => import("@/views/demoLecture/demoLecture_2Detail"),
      },
    ],
  },
  //微学堂详情
  {
    path: "/lecture_3",
    component: Layout,
    redirect: "/lecture/lecture_3",
    children: [
      {
        path: "detail/:id",
        name: "微学堂详情",
        meta: {
          activeMenu: "/lecture/lecture_3",
        },
        component: () => import("@/views/lecture/lecture_3Detail"),
      },
    ],
  },
  //演示微学堂详情
  {
    path: "/demoLecture_3",
    component: Layout,
    redirect: "/demoLecture/demoLecture_3",
    children: [
      {
        path: "detail/:id",
        name: "微学堂详情",
        meta: {
          activeMenu: "/demoLecture/demoLecture_3",
        },
        component: () => import("@/views/demoLecture/demoLecture_3Detail"),
      },
    ],
  },
  //杏林集萃详情
  {
    path: "/lecture_4",
    component: Layout,
    redirect: "/lecture/lecture_4",
    children: [
      {
        path: "detail/:id",
        name: "杏林集萃详情",
        meta: {
          activeMenu: "/lecture/lecture_4",
        },
        component: () => import("@/views/lecture/lecture_4Detail"),
      },
    ],
  },
  //演示杏林集萃详情
  {
    path: "/demoLecture_4",
    component: Layout,
    redirect: "/demoLecture/demoLecture_4",
    children: [
      {
        path: "detail/:id",
        name: "杏林集萃详情",
        meta: {
          activeMenu: "/demoLecture/demoLecture_4",
        },
        component: () => import("@/views/demoLecture/demoLecture_4Detail"),
      },
    ],
  },
  {
    path: "/summarytaxreturns",
    component: Layout,
    redirect: "/summarytaxreturns/summarytaxreturns",
    children: [
      {
        path: "detail/:id",
        name: "月度纳税申报单详情",
        meta: {
          activeMenu: "/summarytaxreturns/summaryTaxReturns",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */
            "@/views/summarytaxreturns/summaryTaxReturnsDetail"
          ),
      },
    ],
  },
  //认证医师资料库
  {
    path: "/doctorlibrary",
    component: Layout,
    redirect: "/doctorlibrary/doctorLibrary",
    children: [
      {
        path: "detail/:id",
        name: "认证医师资料库详情",
        meta: {
          i18n: "dict",
          activeMenu: "/doctorlibrary/doctorLibrary",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/doctorlibrary/doctorLibraryDetail"
          ),
      },
    ],
  },
  // 自然人代发经办单
  {
    path: "/handleApply",
    component: Layout,
    redirect: "/handleApply/handleApply",
    children: [
      {
        path: "detail/:id",
        name: "自然人代发经办单详情",
        meta: {
          i18n: "dict",
          activeMenu: "/handleApply/handleApply",
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ "@/views/handleApply/handleApplyDetail"
          ),
      },
    ],
  },
  // 专业审批活动数据收集
  {
    path: "/professionalreviewreport",
    component: Layout,
    redirect: "/professionalreviewreport/professionalReviewReport",
    children: [
      {
        path: "detail/:id",
        name: "专业评审详情",
        meta: {
          activeMenu: "/professionalreviewreport/professionalReviewReport",
        },
        component: () =>
          import(
            "@/views/professionalreviewreport/professionalReviewReportDetail"
          ),
      },
    ],
  },
  // 演示专业审批活动数据收集
  {
    path: "/demoProfessionalreviewreport",
    component: Layout,
    redirect: "/demoProfessionalreviewreport/demoProfessionalReviewReport",
    children: [
      {
        path: "detail/:id",
        name: "专业评审详情",
        meta: {
          activeMenu: "/demoProfessionalreviewreport/demoProfessionalReviewReport",
        },
        component: () =>
          import(
            "@/views/demoProfessionalreviewreport/demoProfessionalReviewReportDetail"
          ),
      },
    ],
  },
  // 内容发布详情
  {
    path: "/contentManagement",
    component: Layout,
    redirect: "/contentManagement/contentManagement",
    children: [
      {
        path: "contentCreate",
        name: "发布内容",
        meta: {
          activeMenu: "/contentmanagement/contentManagement",
        },
        component: () => import("@/views/contentmanagement/contentCreate.vue"),
      },
      {
        path: "contentEdit/:id",
        name: "编辑内容",
        meta: {
          activeMenu: "/contentmanagement/contentManagement",
        },
        component: () => import("@/views/contentmanagement/contentCreate.vue"),
      },
      {
        path: "contentDetail/:id",
        name: "内容详情",
        meta: {
          activeMenu: "/contentmanagement/contentManagement",
        },
        component: () => import("@/views/contentmanagement/contentDetail.vue"),
      },
    ],
  },
];
