export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "康缘会议id",
      prop: "meetingId",
      type: "input",
    },
    {
      label: "腾讯会议id",
      prop: "tencentMeetingId",
      type: "input",
    },
    {
      label: "参会者用户ID，企业自建应用鉴权方式（JWT）时使用的企业唯一用户标识",
      prop: "userid",
      type: "input",
    },
    {
      label: "入会用户名",
      prop: "userName",
      type: "input",
    },
    {
      label: "参会者手机号的SHA256 hash值，基于手机号+"/"+secretid的组合",
      prop: "phone",
      type: "input",
    },
    {
      label: "参会者加入会议的时间戳（单位：秒）",
      prop: "joinTime",
      type: "input",
    },
    {
      label: "参会者离开会议的时间戳（单位：秒）",
      prop: "leftTime",
      type: "input",
    },
    {
      label: "用户的终端设备类型，具体值参考表注释",
      prop: "instanceid",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
