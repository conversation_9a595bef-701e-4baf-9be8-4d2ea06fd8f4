<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ type, size, row, index }" slot="enable">
        <div
          style="cursor: pointer"
          v-if="permission.clinicalResearchTemplate_stopTemplate"
        >
          <el-switch
            v-model="row.enable"
            @change="enableChange(row)"
            active-color="#67C23A"
            inactive-color="#cacdd4"
            active-text="启用"
            inactive-text="停用"
            :active-value="1"
            :inactive-value="0"
          >
          </el-switch>
        </div>
        <div v-else>
          {{ row.enable == 1 ? "启用" : "停用" }}
        </div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  updateStatus,
  remove,
} from "@/api/clinicalresearchtemplate/clinicalResearchTemplate";
import option from "@/const/clinicalresearchtemplate/clinicalResearchTemplate";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.clinicalResearchTemplate_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.clinicalResearchTemplate_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.clinicalResearchTemplate_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.clinicalResearchTemplate_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    enableChange(row) {
      console.log(row);
      updateStatus(row.id, row.enable).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type == "add") {
        this.$router.push({
          path: `/clinicalresearchtemplate/add/`,
        });
      } else if (type == "edit") {
        this.$router.push({
          path: `/clinicalresearchtemplate/edit/${this.form.id}`,
        });
      } else if (type == "view") {
        this.$router.push({
          path: `/clinicalresearchtemplate/detail/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records.map((e)=>{
          return {
            ...e,
            unitPrice:e.unitPrice==null?'':Number(e.unitPrice).toFixed(0)
          }
        });
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
