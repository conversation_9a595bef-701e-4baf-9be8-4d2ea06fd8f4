<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button type="primary" size="small" @click="handleSettle"
          >申请企业结算
        </el-button>
      </template>
      <template slot-scope="{ type, size, row, index }" slot="resultOrderCode">
        <div
          class="to-view"
          @click="toResultOrder(row.resultOrderId)"
          v-if="row.resultOrderCode"
        >
          <a readonly>
            {{ row.resultOrderCode }}
          </a>
        </div>
        <div v-else>无</div>
      </template>
      <!-- 推送状态 -->
      <template slot="pushStatus" slot-scope="{ row }">
        <el-tag v-if="row.pushStatus == 0" size="small">待推送</el-tag>
        <el-tag v-if="row.pushStatus == 1" type="success" size="small"
        >已推送</el-tag
        >
      </template>
      <!-- 审核状态 -->
      <template slot="approvalStatus" slot-scope="{ row }">
        <el-tag v-if="row.approvalStatus == 0" size="small">待确认</el-tag>
        <el-tag v-if="row.approvalStatus == 1" type="success" size="small"
          >已确认</el-tag
        >
        <el-tag v-if="row.approvalStatus == 2" type="danger" size="small"
        >已驳回</el-tag
        >
        <el-tag v-if="row.approvalStatus == 3" size="small">生成中</el-tag>
      </template>
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button
          :size="option.size"
          type="text"
          v-if="
            permission.point_settlementOrderDoctor_view && row.approvalStatus != 3
          "
          @click="$refs.crud.rowView(row, index)"
          >查 看</el-button
        >
        <el-button
          :size="option.size"
          type="text"
          v-if="
            permission.point_settlementOrderDoctor_delete && row.approvalStatus == 0&&row.pushStatus == 0
          "
          @click="rowDel(row)"
          >删 除</el-button
        >
        <el-button
          :size="option.size"
          type="text"
          v-if="
            permission.point_settlementOrderDoctor_push && row.approvalStatus == 0 && row.pushStatus == 0
          "
          @click="push(row)"
        >发起推送</el-button
        >
        <!--<el-button-->
          <!--:size="option.size"-->
          <!--type="text"-->
          <!--v-if="-->
            <!--permission.point_settlementOrderDoctor_confim && row.approvalStatus == 0-->
          <!--"-->
          <!--@click="confim(row)"-->
          <!--&gt;确认结算</el-button-->
        <!--&gt;-->
      </template>
    </avue-crud>
    <el-dialog
      title="申请企业结算"
      @close="$refs.dialogForm.resetForm()"
      append-to-body
      :visible.sync="dialogVisible"
      width="60%"
    >
      <avue-form
        v-if="dialogVisible"
        ref="dialogForm"
        :option="dialogOption"
        v-model="dialogForm"
        @submit="handleSubmit"
      >
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button :loading="dialogLoading" type="primary" @click="dialogSubmit"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getDetail,
  add,
  update,
  remove,
  toSettle,
  confim,
  push,
} from "@/api/settlementOrderPoint/settlementOrderPoint";
import { getList } from "@/api/settlementOrderPoint/settlementOrderPoint";
import option from "@/const/settlementOrderDoctorPoint/settlementOrderDoctorPoint";
import { mapGetters } from "vuex";
import dayjs from "dayjs";
export default {
  data() {
    return {
      //弹窗
      dialogLoading: false,
      dialogVisible: false,
      dialogForm: {},
      dialogOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: "100",
        column: [
          {
            label: "开始时间",
            prop: "startTime",
            type: "date",
            rules: [
              {
                required: true,
                message: "请选择开始时间",
                trigger: ["blur", "change"],
              },
            ],
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            change: ({ value }) => {
              if (
                dayjs(value).valueOf() >=
                dayjs(this.dialogForm.endTime).valueOf()
              ) {
                this.dialogForm.endTime = "";
              }
            },
          },
          {
            label: "结束时间",
            prop: "endTime",
            type: "date",
            rules: [
              {
                required: true,
                message: "请选择结束时间",
                trigger: ["blur", "change"],
              },
            ],
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            pickerOptions: {
              disabledDate: (time) => {
                let start = dayjs(this.dialogForm.startTime).valueOf();
                return time.getTime() < start;
              },
            },
          },
        ],
      },
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.point_settlementOrderDoctor_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.point_settlementOrderDoctor_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.point_settlementOrderDoctor_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.point_settlementOrderDoctor_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    confim(row) {
      this.$confirm("此操作后该数据将不可删除，是否继续操作?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return confim(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    push(row) {
      this.$confirm("此操作后该数据将不可删除，是否继续操作?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return push(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleSubmit(form, done) {
      this.dialogLoading = true;
      toSettle(form).then(
        (res) => {
          let data = res.data;
          this.$message({
            type: "success",
            message: data.msg,
          });
          done();
          this.$refs.dialogForm.resetForm();
          this.onLoad(this.page);
          this.dialogVisible = false;
          this.dialogLoading = false;
        },
        (error) => {
          done();
          this.dialogLoading = false;
          console.log(error);
        }
      );
    },
    dialogSubmit() {
      this.$refs.dialogForm.submit();
    },
    handleSettle() {
      this.dialogVisible = true;
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$store.commit("SET_CREATE_EVIDENCE_FILE_STATUS", 1);
        this.$router.push({
          path: `/settlementOrderDoctorPoint/detail/${this.form.id}`,
        });
      } else if ("edit" == type) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      } else {
        done();
      }
    },
    toResultOrder(id) {
      this.$router.push({
        path: `/resultorderPoint/detail/${id}`,
      });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      //结算时间
      if (!this.validatenull(params.createTime)) {
        this.query.settleStartData = params.createTime[0];
        this.query.settleEndData = params.createTime[1];
        delete this.query.createTime;
      }
      //审核时间
      if (!this.validatenull(params.approvalDate)) {
        this.query.auditStartData = params.approvalDate[0];
        this.query.auditEndData = params.approvalDate[1];
        delete this.query.approvalDate;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.businessType = 2;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
