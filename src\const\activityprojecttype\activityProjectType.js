export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  labelWidth: "140",
  index: true,
  viewBtn: true,
  selection: true,
  delBtn: false,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "服务项目编号",
      prop: "projectCode",
      type: "input",
      maxlength: 100,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输入项目编号",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "服务项目大类",
      prop: "projectCategory",
      type: "select",
      rules: [
        {
          required: true,
          message: "请选择服务项目大类",
          trigger: ["blur", "change"],
        },
      ],
      dicData: [
        {
          label: "推广交流服务",
          value: "推广交流服务",
        },
        {
          label: "演讲培训服务",
          value: "演讲培训服务",
        },
        {
          label: "专业技术服务",
          value: "专业技术服务",
        },
        {
          label: "信息调研服务",
          value: "信息调研服务",
        },
        {
          label: "健康咨询服务",
          value: "健康咨询服务",
        },
      ],
    },
    {
      label: "服务项目名称",
      prop: "projectTypeName",
      type: "input",
      maxlength: 100,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输入服务项目名称",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "关联活动类型",
      prop: "actType",
      type: "select",
      rules: [
        {
          required: true,
          message: "请选择活动类型",
          trigger: ["blur", "change"],
        },
      ],
      dicData: [
        {
          label: "学术会议",
          value: 1,
        },
        {
          label: "知识创作",
          value: 2,
        },
        {
          label: "知识传播",
          value: 3,
        },
        {
          label: "临床调研",
          value: 4,
        },
        {
          label: "用药反馈",
          value: 5,
        },
        {
          label: "专业评审",
          value: 6,
        },
        {
          label: "病例征集",
          value: 8,
        },
      ],
    },
    {
      label: "服务收入性质",
      prop: "natureOfIncome",
      type: "select",
      hide: true,
      value: 1,
      rules: [
        {
          required: true,
          message: "请选择服务收入性质",
          trigger: ["blur", "change"],
        },
      ],
      dicData: [
        {
          label: "经营所得",
          value: 1,
        },
        {
          label: "劳务报酬",
          value: 2,
        },
      ],
    },

    {
      label: "应税项目名称",
      prop: "projectTaxItemName",
      type: "input",
      maxlength: 100,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输入应税项目名称",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "规格型号",
      prop: "specification",
      type: "input",
      hide: true,
      maxlength: 100,
      showWordLimit: true,
    },
    {
      label: "单位",
      prop: "expenceUnit",
      type: "input",
      hide: true,
      maxlength: 100,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输入单位",
          trigger: ["blur", "change"],
        },
      ],
    },

    {
      label: "医健服务项目说明",
      prop: "projectNote",
      type: "textarea",
      hide: true,
      maxlength: 1000,
      showWordLimit: true,
      span: 24,
    },
    {
      label: "服务合规验收要求",
      prop: "checkRequire",
      type: "textarea",
      hide: true,
      maxlength: 1000,
      showWordLimit: true,
      span: 24,
    },
    {
      label: "医健服务成果规范",
      prop: "resultRequire",
      type: "textarea",
      hide: true,
      maxlength: 1000,
      showWordLimit: true,
      span: 24,
    },
  ],
};
