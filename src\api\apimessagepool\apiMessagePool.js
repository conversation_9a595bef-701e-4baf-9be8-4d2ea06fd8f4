import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-sys/apiMessagePool/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-sys/apiMessagePool/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const resend = (ids) => {
  return request({
    url: "/api/blade-sys/apiMessagePool/resend",
    method: "get",
    params: {
      ids,
    },
  });
};
