<template>
  <div>
    <el-row>
      <el-col :span="24">
        <third-register></third-register>
      </el-col>
    </el-row>
    <basic-container v-if="userInfo.role_id == 39 || userInfo.role_id == 6">
      <div
        class="flex-row"
        style="
          flex-direction: column;
          align-items: center;
          text-align: center;
          margin-bottom: 50px;
        "
      >
        <img style="width: 55%" src="../../../public/img/home.png" alt="" />
        <p style="margin-left: -120px; font-size: 20px">
          欢迎使用元圈健康服务云平台
        </p>
      </div>
    </basic-container>

    <div v-else>
      <!-- <HomeHeader :form="form"></HomeHeader> -->
      <el-row :gutter="10" justify="space-between">
        <el-col :span="6">
          <HomeStatistics :form="form"></HomeStatistics>
        </el-col>
        <el-col :span="18"><HomeTrend></HomeTrend> </el-col>
      </el-row>
      <div style="height: 12px"></div>
      <el-row :gutter="10" justify="space-between">
        <el-col :span="12"> <HomeMap></HomeMap> </el-col>
        <el-col :span="12">
          <HomeProportion></HomeProportion>
          <homeSummary></homeSummary
        ></el-col>
      </el-row>
    </div>
    <div style="height: 50px"></div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import HomeMap from "../../components/home-map/home-map.vue";
import HomeHeader from "../../components/home-header/home-header.vue";
import HomeTrend from "../../components/home-trend/home-trend.vue";
import HomeStatistics from "../../components/home-statistics/home-statistics.vue";
import HomeProportion from "../../components/home-proportion/home-proportion.vue";
import homeSummary from "../../components/home-summary/home-summary.vue";
import { getStatisticsSingleItem } from "@/api/home/<USER>";
export default {
  components: {
    HomeMap,
    HomeHeader,
    HomeTrend,
    HomeStatistics,
    HomeProportion,
    homeSummary,
  },
  name: "wel",
  data() {
    return {
      form: {},
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  created() {
    this.getStatisticsSingleItem();
  },
  methods: {
    getStatisticsSingleItem() {
      getStatisticsSingleItem().then((res) => {
        this.form = res.data.data;
      });
    },
  },
};
</script>

<style scoped>
.el-col {
  margin-bottom: 0 !important;
}

.el-font-size {
  font-size: 14px;
}
.center {
  display: flex;
  justify-content: space-between;
}
</style>
