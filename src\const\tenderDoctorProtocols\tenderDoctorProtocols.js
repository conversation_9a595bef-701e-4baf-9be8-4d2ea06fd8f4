export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  menuWidth: 130,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  editBtn: false,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtn: false,
  column: [
    {
      label: "编号",
      prop: "code",
      type: "input",
      search: true,
    },
    {
      label: "合同名称",
      prop: "name",
      type: "input",
      span: 24,
      search: true,
      rules: [
        {
          required: true,
          message: "请输入名称",
          trigger: "blur",
        },
      ],
    },

    {
      label: "会员姓名",
      prop: "doctorName",
      type: "input",
      search: true,
    },

    {
      label: "合同开始日期",
      prop: "serviceStartDate",
      type: "input",
    },
    {
      label: "合同结束日期",
      prop: "serviceEndtDate",
      type: "input",
    },

    {
      label: "合同模板类型",
      prop: "protocolType",
      type: "select",
      dicData: [
        {
          label: "标准合同",
          value: 1,
        },
        {
          label: "个性合同",
          value: 2,
        },
      ],
    },
    {
      label: "合作结算周期",
      prop: "settlementInterval",
      type: "radio",
      dicData: [
        {
          label: "随时",
          value: 1,
        },
        {
          label: "按月",
          value: 2,
        },
        {
          label: "按季",
          value: 3,
        },
        {
          label: "按年",
          value: 4,
        },
      ],
    },
    {
      label: "签约时间",
      prop: "doctorSignTime",
      type: "input",
    },
    {
      label: "协议状态",
      prop: "protocolStatus",
      type: "select",
      dicData: [
        {
          label: "初始化起草",
          value: 1,
        },
        {
          label: "已生效（签署完成）",
          value: 2,
        },
        {
          label: "已作废",
          value: 3,
        },
        {
          label: "已过期",
          value: 4,
        },
      ],
    },
  ],
};
