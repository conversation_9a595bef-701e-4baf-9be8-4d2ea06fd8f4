<template>
  <div>
    <basic-container>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div>
          <el-page-header
            @back="goBack"
            :content="$route.name"
          ></el-page-header>
        </div>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
        @submit="saveInvoice"
        @reset-change="handleReset"
        :upload-preview="uploadPreview"
      >
        <template slot-scope="{ size }" slot="menuForm">
          <!-- <el-button type="primary" plain @click="debounceFunc"
            >发票查验
          </el-button> -->
          <el-button
            type="primary"
            plain
            :size="size"
            @click="$refs.form.submit()"
            >提交
          </el-button>
          <el-button :size="size" @click="$refs.form.resetForm()"
            >清空
          </el-button>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
      <el-dialog
        title="附件预览"
        :visible.sync="dialogVisible"
        append-to-body
        width="50%"
      >
        <el-image
          v-if="filetType == 1"
          :src="fileUrl"
          :preview-src-list="fileUrlList"
        ></el-image>
        <iframe v-else :src="fileUrl" width="100%" height="600px"></iframe>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="dialogVisible = false"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import {
  invoiceCheck,
  uploadInvoice,
} from "@/api/entrustedInvoicePoint/entrustedInvoicePoint";
export default {
  data() {
    return {
      dialogVisible: false,
      filetType: 1,
      fileUrl: "",
      fileUrlList: [],
      id: "", //数据id
      form: {
        invoiceFileLink: "",
        type: 1,
      },
      checkDate: "",
      query: {},
      loading: false,
      times: null,
      isEfficient: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option4: {
        submitText: "完成",
        span: 24,
        column: [
          {
            label: "驳回意见",
            prop: "name",
            type: "textarea",
            rules: [
              {
                required: true,
                message: "请输入驳回意见",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      //基础信息
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 150,
        group: [
          {
            label: "发票信息",
            arrow: true,
            disabled: true,
            prop: "group2",
            column: [
              {
                label: "发票上传类型",
                prop: "type",
                type: "radio",
                control: (val) => {
                  if (val === 1) {
                    return {
                      invoiceDataCode: { display: true },
                      invoiceNumber: { display: true, label: "发票号码" },
                      checkCode: { display: true },
                      billingTime: { display: true },
                      amount: { display: true },
                      taxDiskCode: { display: true },
                      invoiceTypeName: { display: true },
                      purchaserName: { display: true },
                      taxpayerNumber: { display: true },
                      taxpayerBankAccount: { display: true },
                      taxpayerAddressOrPhone: { display: true },
                      salesName: { display: true },
                      salesTaxpayerNum: { display: true },
                      salesTaxpayerBankAccount: { display: true },
                      salesTaxpayerAddress: { display: true },
                      taxAmount: { display: true },
                      totalTaxAmount: { display: true },
                    };
                  } else {
                    return {
                      invoiceDataCode: { display: false },
                      invoiceNumber: { display: true, label: "数电票号码" },
                      checkCode: { display: false },
                      billingTime: { display: true },
                      amount: { display: false },
                      taxDiskCode: { display: false },
                      invoiceTypeName: { display: false },
                      purchaserName: { display: true },
                      taxpayerNumber: { display: true },
                      taxpayerBankAccount: { display: false },
                      taxpayerAddressOrPhone: { display: false },
                      salesName: { display: true },
                      salesTaxpayerNum: { display: true },
                      salesTaxpayerBankAccount: { display: false },
                      salesTaxpayerAddress: { display: false },
                      taxAmount: { display: false },
                      totalTaxAmount: { display: true },
                    };
                  }
                },
                value: 1,
                dicData: [
                  {
                    label: "普通发票",
                    value: 1,
                  },
                  {
                    label: "数电发票",
                    value: 2,
                  },
                ],
              },
              {
                label: "发票代码",
                prop: "invoiceDataCode",
                rules: [
                  {
                    required: true,
                    message: "请输入发票代码",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "发票号码",
                prop: "invoiceNumber",
                rules: [
                  {
                    required: true,
                    message: "请输入发票号码",
                    trigger: ["blur", "change"],
                  },
                ],
              },

              {
                label: "校验码",
                prop: "checkCode",
                rules: [
                  {
                    required: true,
                    message: "请输入效验码",
                    trigger: ["blur", "change"],
                  },
                ],
              },

              {
                label: "开票日期",
                prop: "billingTime",
                type: "date",
                format: "yyyy-MM-dd",
                valueFormat: "yyyy-MM-dd",
                rules: [
                  {
                    required: true,
                    message: "请选择开票日期",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "不含税金额",
                prop: "amount",
                append: "元",
                placeholder: "当发票种类是增值税专用发票时查验必填",
              },
              {
                label: "机器码",
                prop: "taxDiskCode",
                rules: [
                  {
                    required: true,
                    message: "机器码不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "发票类型",
                prop: "invoiceTypeName",
                rules: [
                  {
                    required: true,
                    message: "发票类型不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "购方名称",
                prop: "purchaserName",
                rules: [
                  {
                    required: true,
                    message: "购方名称不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "购方纳税人识别号",
                prop: "taxpayerNumber",
                rules: [
                  {
                    required: true,
                    message: "购方纳税人识别号不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "购方开户行及账号",
                prop: "taxpayerBankAccount",
              },
              {
                label: "购方地址、电话",
                prop: "taxpayerAddressOrPhone",
              },
              {
                label: "销方名称",
                prop: "salesName",
                rules: [
                  {
                    required: true,
                    message: "销方名称不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "销方纳税人识别号",
                prop: "salesTaxpayerNum",
                rules: [
                  {
                    required: true,
                    message: "销方纳税人识别号不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "销方开户行及账号",
                prop: "salesTaxpayerBankAccount",
                rules: [
                  {
                    required: true,
                    message: "销方开户行及账号不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "销方地址、电话",
                prop: "salesTaxpayerAddress",
                rules: [
                  {
                    required: true,
                    message: "销方地址、电话不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "税额",
                prop: "taxAmount",
                type: "number",
                append: "元",
                rules: [
                  {
                    required: true,
                    message: "税额不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "价税合计",
                prop: "totalTaxAmount",
                type: "number",
                append: "元",
                rules: [
                  {
                    required: true,
                    message: "价税合计不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              // {
              //   label: "附件",
              //   prop: "invoiceFileLink",
              // },
              {
                // disabled: true,
                span: 8,
                label: "附件",
                prop: "invoiceFileLink",
                type: "upload",
                dataType: "string",
                accept: "image/png, image/jpeg, application/pdf",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                limit: 1,
                tip: "上传图片或PDF ，上传成功后点击文件名查看附件详情",
                rules: [
                  {
                    required: true,
                    message: "附件不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
            ],
          },
        ],
      },
      data: [],
      goodsData: {}, //发票回填数据
      sponsorHandleStatus: false,
    };
  },
  created() {
    this.id = this.$route.params.id;
  },
  methods: {
    uploadPreview(file) {
      this.fileUrl = file.url;
      //获取最后一个.的位置
      var index = this.fileUrl.lastIndexOf(".");
      //获取后缀
      var ext = this.fileUrl.substring(index + 1);
      this.filetType = 1;
      if (ext == "pdf") {
        this.filetType = 2;
      } else {
        this.fileUrlList[0] = this.fileUrl;
      }
      this.dialogVisible = true;
    },
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    debounceFunc() {
      // const svcInvoiceId = this.id;
      const billTime = this.form.billingTime;
      const checkCode = this.form.checkCode;
      const invoiceCode = this.form.invoiceDataCode;
      const invoiceNumber = this.form.invoiceNumber;
      const amount = this.form.amount;
      if (billTime == "" || billTime == null) {
        return this.$message({
          type: "error",
          message: "开票日期不能为空",
        });
      }
      if (invoiceCode == "" || invoiceCode == null) {
        return this.$message({
          type: "error",
          message: "发票代码不能为空",
        });
      }
      if (invoiceNumber == "" || invoiceNumber == null) {
        return this.$message({
          type: "error",
          message: "发票号码不能为空",
        });
      }
      if (checkCode == "" || checkCode == null) {
        return this.$message({
          type: "error",
          message: "校验码不能为空",
        });
      }
      const obj = {
        invoiceCode,
        invoiceNumber,
        billTime,
        checkCode: checkCode.slice(-6),
        amount,
      };
      this.invoiceCheck(obj);
    },
    //乐税发票查验
    invoiceCheck(obj) {
      invoiceCheck(obj).then((res) => {
        let data = res.data;
        if (data.success) {
          const resultObj = data.data;
          this.goodsData = resultObj.goodsData;
          this.form.invoiceDataCode = resultObj.invoiceDataCode;
          this.form.invoiceNumber = resultObj.invoiceNumber;
          this.form.taxDiskCode = resultObj.taxDiskCode;
          this.form.checkCode = resultObj.checkCode;
          this.form.invoiceTypeName = resultObj.invoiceTypeName; //发票类型名称
          this.form.invoiceTypeCode = resultObj.invoiceTypeCode; //发票类型名称
          this.checkDate = resultObj.checkDate;
          this.form.purchaserName = resultObj.purchaserName;
          this.form.taxpayerNumber = resultObj.taxpayerNumber;
          this.form.taxpayerBankAccount = resultObj.taxpayerBankAccount;
          // let z = JSON.parse(resultObj.invoiceDetailData);

          // this.form.invoiceItem = z.length > 0 ? z.map(c => c.goodserviceName).toString() : '';
          this.form.taxpayerAddressOrPhone = resultObj.taxpayerAddressOrId; //购方地址，电话
          this.form.salesName = resultObj.salesName; //销方名称
          this.form.salesTaxpayerNum = resultObj.salesTaxpayerNum; //销方纳税人识别号
          this.form.salesTaxpayerBankAccount =
            resultObj.salesTaxpayerBankAccount; //销方银行，账号
          this.form.salesTaxpayerAddress = resultObj.salesTaxpayerAddress; //销售方地址电话
          this.form.amount = resultObj.totalAmount; //不含税价（金额）
          this.form.taxAmount = resultObj.totalTaxNum; //税额
          this.form.totalTaxAmount = resultObj.totalTaxSum; //价税合计
          this.form.invoiceFileLink = "";
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    saveInvoice(form, done) {
      if (this.sponsorHandleStatus) {
        this.$message.warning("提交中请稍后");
        return;
      }
      this.sponsorHandleStatus = true;
      // form.goodsData = this.goodsData;
      form.entrustedInvoiceId = this.id;
      uploadInvoice(form).then(
        (res) => {
          this.sponsorHandleStatus = false;
          let data = res.data;
          if (data.success) {
            this.$message({
              type: "success",
              message: "操作成功",
            });
            done();
            this.importShow = false;
            this.$refs.form.resetForm();
            this.form.type = 1;
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
            done();
          }
        },
        (error) => {
          this.sponsorHandleStatus = false;
          done();
          window.console.log(error);
        }
      );
    },
        //清空
        handleReset() {
          this.form.type=1
    },
  },
};
</script>

<style></style>
