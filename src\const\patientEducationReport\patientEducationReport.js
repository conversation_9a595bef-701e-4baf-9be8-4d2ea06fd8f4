export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  editBtn: false,
  delBtn: false,
  addBtn: false,
  selection: true,
  dialogClickModal: false,
  menuWidth: 75,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "患教活动计划主键id",
      prop: "patientEducationId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "企业名称id",
      prop: "entrustedCompanyId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "企业名称",
      prop: "entrustedCompanyName",
      type: "input",
      search: true,
    },
    {
      label: "上游企业ID",
      prop: "enterpriseId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "上游企业名称",
      prop: "enterpriseName",
      type: "input",
      hide: true,
    },
    {
      label: "业务人员ID",
      prop: "baseEmployeeId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "业务人员名称",
      prop: "baseEmployeeName",
      type: "input",
      hide: true,
    },
    {
      label: "代表部门ID",
      prop: "baseDepartmentId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "代表部门名称",
      prop: "baseDepartmentName",
      type: "input",
      hide: true,
    },
    {
      label: "分享记录Id",
      prop: "shareedId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "认证会员ID",
      prop: "doctorId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "yxt会员档案ID",
      prop: "doctorArchivesId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "会员姓名",
      prop: "doctorName",
      type: "input",
      search: true,
    },
    {
      label: "所属医院",
      prop: "hospitalName",
      type: "input",
    },
    {
      label: "所属科室",
      prop: "departmentName",
      type: "input",
    },
    {
      label: "活动主题",
      prop: "activityTheme",
      type: "input",
      span: 24,
      search: true,
    },
    {
      label: "讲者姓名",
      prop: "speakerName",
      type: "input",
    },
    {
      label: "活动类型",
      prop: "activityType",
      type: "input",
      search: true,
    },
    {
      label: "组织形式",
      prop: "activityOrganizationForm",
      type: "input",
      hide: true,
    },
    {
      label: "举办形式",
      prop: "activityForm",
      type: "input",
    },
    {
      label: "活动状态",
      prop: "activityStatus",
      type: "select",
      search: true,
      dicData: [
        {
          label: "未开始",
          value: 0,
        },
        {
          label: "进行中",
          value: 1,
        },
        {
          label: "已结束",
          value: 2,
        },
      ],
    },
    {
      label: "涉及产品名称",
      prop: "baseProductName",
      type: "input",
      hide: true,
    },
    {
      label: "患教关键内容",
      prop: "patientTeachingKeyContent",
      type: "input",
      overHidden: true,
      hide: true,
    },
    {
      label: "患教目的",
      prop: "patientEducationPurpose",
      type: "input",
      hide: true,
    },
    {
      label: "计划开始时间",
      prop: "planStartTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm",
      valueFormat: "yyyy-MM-dd HH:mm",
    },
    {
      label: "活动时长(分钟)",
      prop: "activityDuration",
      type: "input",
      hide: true,
    },
    {
      label: "计划结束时间",
      prop: "planEndTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm",
      valueFormat: "yyyy-MM-dd HH:mm",
    },
    {
      label: "会议实际开始时间",
      prop: "actuallyStartDate",
      type: "input",
      hide: true,
    },
    {
      label: "会议实际结束时间",
      prop: "actuallyEndDate",
      type: "input",
      hide: true,
    },
    {
      label: "活动省份编号",
      prop: "provinceCode",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "省份",
      prop: "provinceName",
      type: "input",
    },
    {
      label: "活动城市编号",
      prop: "cityCode",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "城市",
      prop: "cityName",
      type: "input",
    },
    {
      label: "同步状态",
      prop: "syncStatus",
      type: "select",
      dicData: [
        {
          label: "未同步",
          value: 0,
        },
        {
          label: "同步完成",
          value: 1,
        },
        {
          label: "同步失败",
          value: 2,
        },
      ],
    },
    {
      label: "活动地点",
      prop: "address",
      type: "input",
      hide: true,
    },
    {
      label: "腾讯会议号",
      prop: "tencentCode",
      type: "input",
      hide: true,
    },
    {
      label: "腾讯会议地址",
      prop: "tencentLiveUrl",
      type: "input",
      hide: true,
    },

    {
      label: "会议组织人员",
      prop: "enterpriseCollaborator",
      type: "input",
      hide: true,
    },
    {
      label: "组织部门/服务商",
      prop: "organizationalDepartment",
      type: "input",
      hide: true,
    },
    {
      label: "患教活动组织",
      prop: "educationActivitiesOrganization",
      type: "input",
      overHidden: true,
      hide: true,
    },
    {
      label: "患教内容说明",
      prop: "contentDescription",
      type: "input",
      overHidden: true,
      hide: true,
    },
    {
      label: "活动议程表",
      prop: "activityAgendafile",
      type: "input",
      overHidden: true,
      hide: true,
    },
    {
      label: "讲者课件",
      prop: "speakerCoursewareFile",
      type: "input",
      overHidden: true,
      hide: true,
    },
    {
      label: "讲者课件格式类型",
      prop: "speakerCoursewareExtension",
      type: "input",
      hide: true,
    },
    {
      label: "讲者协议模版文本",
      prop: "speakerAgreementTemplate",
      type: "input",
      overHidden: true,
      hide: true,
    },
    {
      label: "讲者协议签署文件",
      prop: "speakerAgreementFile",
      type: "input",
      overHidden: true,
      hide: true,
    },
    {
      label: "讲者协议签署签名",
      prop: "speakerAgreementSignature",
      type: "input",
      hide: true,
    },
    {
      label: "讲者协议签署时间",
      prop: "speakerAgreementDate",
      type: "input",
      hide: true,
    },
    {
      label: "讲者协议签署状态",
      prop: "speakerAgreementStatus",
      type: "select",
      hide: true,
      dicData: [
        {
          label: "待签署",
          value: 0,
        },
        {
          label: "已签署",
          value: 1,
        },
      ],
    },
    {
      label: "讲者签到状态",
      prop: "speakerSignStatus",
      type: "select",
      hide: true,
      dicData: [
        {
          label: "待签到",
          value: 0,
        },
        {
          label: "已签到",
          value: 1,
        },
      ],
    },
    {
      label: "讲者签到地址",
      prop: "speakerSignAddress",
      type: "input",
      hide: true,
    },
    {
      label: "讲者签到时间",
      prop: "speakerSignDate",
      type: "input",
      hide: true,
    },
    {
      label: "讲者签到经纬度",
      prop: "speakerSignLogLat",
      type: "input",
      hide: true,
    },
    {
      label: "讲者签到签名",
      prop: "speakerSignSignature",
      type: "input",
      hide: true,
    },
    {
      label: "评价状态",
      prop: "evaluationStatus",
      type: "input",
      hide: true,
    },
    {
      label: "评价模版",
      prop: "evaluationTemplate",
      type: "input",
      overHidden: true,
      hide: true,
    },
    {
      label: "评价信息",
      prop: "evaluationInfo",
      type: "input",
      overHidden: true,
      hide: true,
    },
    {
      label: "评价时间",
      prop: "evaluationDate",
      type: "input",
      hide: true,
    },

    {
      label: "活动列表任务显示状态",
      prop: "activityViewStatus",
      type: "select",
      hide: true,
      dicData: [
        {
          label: "待签署协议",
          value: 1,
        },
        {
          label: "已签署协议",
          value: 2,
        },
        {
          label: "待签到",
          value: 3,
        },
        {
          label: "已签到",
          value: 4,
        },
        {
          label: "待评价",
          value: 5,
        },
        {
          label: "已评价",
          value: 6,
        },
      ],
    },

    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
