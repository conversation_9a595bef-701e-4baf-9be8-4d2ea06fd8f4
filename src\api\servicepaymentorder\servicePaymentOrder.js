import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/servicePaymentOrder/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/servicePaymentOrder/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-svc/servicePaymentOrder/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-svc/servicePaymentOrder/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/servicePaymentOrder/submit",
    method: "post",
    data: row,
  });
};
//线下支付流水记录到账确认
export const confimPay = (id) => {
  return request({
    url: "/api/blade-svc/servicePaymentOrder/confimPay",
    method: "get",
    params: { id },
  });
};

//获取流水分页
export const getOrderList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/servicePaymentOrderDetail/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

//线下支付 上传凭证
export const uploadVoucherUrl = (data) => {
  return request({
    url: "/api/blade-svc/servicePaymentOrderDetail/submit",
    method: "post",
    data,
  });
};

// 确认推送
export const submitReview = (row) => {
  return request({
    url: '/api/blade-svc/servicePaymentOrder/submitReview',
    method: 'post',
    data: row
  })
}
