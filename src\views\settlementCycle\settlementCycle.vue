<template>
  <basic-container>
    <avue-crud
      :option="option"
      :search.sync="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button type="warning" size="small" plain @click="handleExport"
          >导 出
        </el-button>
      </template>
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button
          :size="option.size"
          type="text"
          v-if="permission.settlementCycle_add && row.approvalStatus == 0"
          @click="confim(row)"
          >确认开票</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove,
  confim,
} from "@/api/settlementCycle/settlementCycle";
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import dayjs from "dayjs";
export default {
  data() {
    return {
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        addBtn: true,
        addBtnIcon: " ",
        viewBtnIcon: " ",
        delBtnIcon: " ",
        editBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "code",
            prop: "code",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "结算单名称",
            prop: "name",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "需求方Id",
            prop: "entrustedCompanyId",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "服务活动结算金额",
            prop: "settlementActivityAmount",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "服务管理报告金额",
            prop: "settlementManageAmount",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "服务结算总金额",
            prop: "settlementTotalAmount",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "结算年月",
            prop: "accountPeriod",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "周期开始时间",
            prop: "actStartTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            change: ({ value }) => {
              if (
                dayjs(value).valueOf() >= dayjs(this.form.actEndTime).valueOf()
              ) {
                this.form.actEndTime = "";
              }
            },
          },
          {
            label: "周期结束时间",
            prop: "actEndTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            pickerOptions: {
              disabledDate: (time) => {
                let start = dayjs(this.form.actStartTime).valueOf();
                return (
                  time.getTime() < start ||
                  time.getTime() > dayjs().valueOf() - 60 * 60 * 1000 * 24
                );
              },
            },
          },
          {
            label: "平台服务总包方",
            prop: "servicePlatform",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "服务结算单附件url",
            prop: "settlementOrderFile",
            type: "input",
            hide: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "申请状态",
            prop: "approvalStatus",
            type: "select",
            dicData: [
              {
                label: "未开票",
                value: 0,
              },
              {
                label: "已确认",
                value: 1,
              },
            ],
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "申请时间",
            prop: "approvalDate",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "申请人员",
            prop: "approvalOfficer",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "申请备注",
            prop: "approvalRemark",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建人",
            prop: "createUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建部门",
            prop: "createDept",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建时间",
            prop: "createTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改人",
            prop: "updateUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改时间",
            prop: "updateTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "是否已删除",
            prop: "isDeleted",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "版本控制",
            prop: "version",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
        ],
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.settlementCycle_add, false),
        viewBtn: this.vaildData(this.permission.settlementCycle_view, false),
        delBtn: this.vaildData(this.permission.settlementCycle_delete, false),
        editBtn: this.vaildData(this.permission.settlementCycle_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowSave(row, done, loading) {
      console.log(row);
      let obj = {
        actStartTime: row.actStartTime + " 00:00:00",
        actEndTime: row.actEndTime + " 23:59:59",
      };
      add(obj).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    confim(row) {
      this.$confirm("此操作后该数据将不可删除，是否继续操作?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return confim(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleExport() {
      let downloadUrl = `/api/blade-settlementCycle/settlementCycle/export-settlementCycle?${
        this.website.tokenHeader
      }=${getToken()}`;
      let values = {};
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `企业周期结算单${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/settlementCycle/detail/${this.form.id}`,
        });
        return;
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
