export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "服务活动成果总清单id",
      prop: "resultOrderId",
      type: "input",
    },
    {
      label: "注册会员id",
      prop: "doctorId",
      type: "input",
    },
    {
      label: "会员姓名",
      prop: "doctorName",
      type: "input",
      span: 24,
      search: true,
      rules: [
        {
          required: true,
          message: "请输入会员姓名",
          trigger: "blur",
        },
      ],
    },
    {
      label: "专业拜访活动数量",
      prop: "professionalVisitNum",
      type: "input",
    },
    {
      label: "专业拜访活动费用",
      prop: "professionalVisitFee",
      type: "input",
    },
    {
      label: "普通走访活动数量",
      prop: "generalVisitNum",
      type: "input",
    },
    {
      label: "普通走访活动费用",
      prop: "generalVisitFee",
      type: "input",
    },
    {
      label: "知识传播活动数量",
      prop: "knowledgeDisseminateNum",
      type: "input",
    },
    {
      label: "知识传播活动费用",
      prop: "knowledgeDisseminateFee",
      type: "input",
    },
    {
      label: "临床调研活动数量",
      prop: "clinicalResearchNum",
      type: "input",
    },
    {
      label: "临床调研活动费用",
      prop: "clinicalResearchFee",
      type: "input",
    },
    {
      label: "用药反馈活动数量",
      prop: "medicationFeedbackNum",
      type: "input",
    },
    {
      label: "用药反馈活动费用",
      prop: "medicationFeedbackFee",
      type: "input",
    },
    {
      label: "病例分享活动数量",
      prop: "caseInterpretationNum",
      type: "input",
    },
    {
      label: "病例分享活动费用",
      prop: "caseInterpretationFee",
      type: "input",
    },
    {
      label: "知识创作活动数量",
      prop: "knowledgeCreationNum",
      type: "input",
    },
    {
      label: "知识创作活动费用",
      prop: "knowledgeCreationFee",
      type: "input",
    },
    {
      label: "病例征集活动数量",
      prop: "caseCollectionNum",
      type: "input",
    },
    {
      label: "病例征集活动费用",
      prop: "caseCollectionFee",
      type: "input",
    },
    {
      label: "患者教育活动数量",
      prop: "patientEducationNum",
      type: "input",
    },
    {
      label: "患者教育活动费用",
      prop: "patientEducationFee",
      type: "input",
    },
    {
      label: "预留1活动数量",
      prop: "act1Num",
      type: "input",
    },
    {
      label: "预留1活动费用",
      prop: "act1Fee",
      type: "input",
    },
    {
      label: "预留2活动数量",
      prop: "act2Num",
      type: "input",
    },
    {
      label: "预留2活动费用",
      prop: "act2Fee",
      type: "input",
    },
    {
      label: "会员该批次清单结算总费用",
      prop: "settleSumFee",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
