export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  lazy: true,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  column: [
    {
      label: "部门id 主键",
      prop: "id",
      type: "input",
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "部门名称",
      prop: "deptName",
      type: "input",
      search: true,
    },
    {
      label: "上级部门",
      prop: "parentId",
      dicData: [],
      type: "tree",
      hide: true,
      addDisabled: false,
      props: {
        label: "title",
      },
      rules: [
        {
          required: false,
          message: "请选择上级部门",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "上级部门名称",
      prop: "parentDeptName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "部门全称(祖级列表)",
      prop: "fullName",
      type: "input",
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "部门层级",
      prop: "currentLevel",
      type: "input",
    },
    {
      label: "部门编码",
      prop: "deptCode",
      type: "input",
    },
    {
      label: "部门负责人",
      prop: "responsibleName",
      type: "input",
    },
    {
      label: "部门负责人id",
      prop: "responsibleId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "负责人工号",
      prop: "jobNumber",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "负责人职位",
      prop: "jobPosition",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "上级部门全程（祖级）",
      prop: "parentFullName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "上级部门层级",
      prop: "parentLevel",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "上级部门负责人",
      prop: "parentResponsibleName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "1级部门名称",
      prop: "shortLevelOneName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "2级部门名称",
      prop: "shortLevelTwoName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "3级部门名称",
      prop: "shortLevelThreeName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "4级部门名称",
      prop: "shortLevelFourName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "5级部门名称",
      prop: "shortLevelFiveName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "6级部门名称",
      prop: "shortLevelSixName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
