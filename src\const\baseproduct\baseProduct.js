export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  delBtn: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "企业名称",
      prop: "entrustedCompanyId",
      type: "select",
      props: {
        label: "name",
        value: "id",
      },
      dicUrl: "/api/blade-csc/entrustedCompany/getList",
      rules: [
        {
          required: true,
          message: "请选择企业名称",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "产品名称",
      prop: "productName",
      type: "input",
      maxlength: 100,
      showWordLimit: true,
      search: true,
      rules: [
        {
          required: true,
          message: "请输入产品名称",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "品种编码",
      prop: "varietyCode",
      type: "input",
      maxlength: 100,
      showWordLimit: true,
      search: true,
      rules: [
        {
          required: true,
          message: "请输入品种编码",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "产品类别",
      prop: "productType",
      type: "input",
      maxlength: 100,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输入产品类别",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "标准品规",
      prop: "productFormat",
      type: "input",
      maxlength: 100,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输入标准品规",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "产品分类",
      prop: "productCategory",
      type: "input",
      maxlength: 100,
      showWordLimit: true,
      search: true,
      rules: [
        {
          required: true,
          message: "请输入产品分类",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "产品线",
      prop: "productLine",
      type: "input",
      maxlength: 100,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输入产品线",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
