export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "成果单id",
      prop: "resultOrderId",
      type: "input",
      hide: true,
    },
    {
      label: "服务预算单编号",
      prop: "code",
      type: "input",
      hide: true,
    },
    {
      label: "企业名称id",
      prop: "entrustedCompanyId",
      type: "input",
      hide: true,
    },
    {
      label: "企业名称",
      prop: "entrustedCompanyName",
      type: "input",
    },

    {
      label: "活动预算费用",
      prop: "budgetFee",
      type: "input",
      hide: true,
    },

    {
      label: "活动清单预算总费用",
      prop: "budgetTotalFee",
      type: "input",
      hide: true,
    },

    {
      label: "服务预算清单URL",
      prop: "budgetOrderUrl",
      type: "input",
      hide: true,
    },
    {
      label: "总包方",
      prop: "generalContractor",
      type: "input",
    },
    {
      label: "业务期间",
      prop: "accountPeriod",
      type: "input",
    },
    {
      label: "开始日期",
      prop: "budgetStartTime",
      type: "date",
      format: "yyyy-MM-dd",
    },
    {
      label: "结束日期",
      prop: "budgetEndTime",
      type: "date",
      format: "yyyy-MM-dd",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
