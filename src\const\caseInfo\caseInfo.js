export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  editBtn: false,
  delBtn: false,
  addBtn: false,
  selection: true,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  menuWidth: 180,
  menu: false,
  searchLabelWidth: "120",
  column: [
    {
      label: "患者化名",
      prop: "attr0000000043",
      type: "input",
    },
    {
      label: "性别",
      prop: "attr0000000044",
      type: "input",
    },
    {
      label: "年龄",
      prop: "attr0000000045",
      type: "input",
    },
    {
      label: "就诊日期",
      prop: "attr0000000046",
      type: "input",
    },
    {
      label: "患者主诉",
      prop: "attr0000000047",
      type: "input",
      width: 120,
      overHidden: true,
    },

    {
      label: "现病史",
      prop: "attr0000000048",
      type: "input",
      width: 120,
      overHidden: true,
    },
    {
      label: "既往史",
      prop: "attr0000000049",
      type: "input",
      width: 120,
      overHidden: true,
    },
    {
      label: "过敏史",
      prop: "attr0000000050",
      type: "input",
      width: 120,
      overHidden: true,
    },
    {
      label: "体格检查",
      prop: "attr0000000051",
      type: "input",
      width: 120,
      overHidden: true,
    },
    {
      label: "西医诊断",
      prop: "attr0000000055",
      type: "input",
    },
    {
      label: "中医诊断",
      prop: "attr0000000056",
      type: "input",
    },
    {
      label: "治疗方案",
      prop: "attr0000000057",
      type: "input",
      width: 120,
      overHidden: true,
    },
    {
      label: "不良反应",
      prop: "attr0000000058",
      type: "input",
    },
  ],
};
