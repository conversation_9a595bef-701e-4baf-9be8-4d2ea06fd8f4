export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "规则编号",
      prop: "code",
      type: "input",
      editDisplay: false,
    },
    {
      label: "规则名称",
      prop: "name",
      type: "input",
    },
    {
      label: "对应积分",
      prop: "number",
      type: "input",
      dataType: "number",
    },
    {
      label: "类型",
      prop: "type",
      type: "select",
      dicData: [
        {
          label: "收入",
          value: 1,
        },
        {
          label: "支出",
          value: 2,
        },
      ],
    },
    {
      label: "积分来源类型",
      prop: "source",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=source",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
    },
    {
      label: "每日上限",
      prop: "upperLimit",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
