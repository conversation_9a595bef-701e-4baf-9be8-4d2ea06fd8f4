import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/resultOrderPoint/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/resultOrderPoint/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (id) => {
  return request({
    url: "/api/blade-svc/resultOrder/remove",
    method: "post",
    params: {
      id,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-svc/resultOrder/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/resultOrder/submit",
    method: "post",
    data: row,
  });
};

export const getEntrustedProtocolsList = (id) => {
  return request({
    url: "/api/blade-ts/tenderEntrustedProtocols/getList",
    method: "get",
    params: {
      id,
    },
  });
};

// 提交创建医患服务成果单
export const submitInfo = (row) => {
  return request({
    url: "/api/blade-svc/resultOrder/create",
    method: "post",
    data: row,
  });
};

//成果单确认 保存统计汇总数据 生成管理报告
export const confirm = (resultOrderOrderId) => {
  return request({
    url: "/api/blade-svc/resultOrder/confirm",
    method: "post",
    params: {
      resultOrderOrderId,
    },
  });
};

//成果总结
export const resultOrderProjectList = (current, size, resultOrderId) => {
  return request({
    url: "/api/blade-svc/resultOrderProjectDetailPoint/list",
    method: "get",
    params: {
      current,
      size,
      resultOrderId,
    },
  });
};

// 医师明细报告
export const resultOrderDoctorList = (current, size, resultOrderId) => {
  return request({
    url: "/api/blade-svc/resultOrderDoctorDetailPoint/list",
    method: "get",
    params: {
      current,
      size,
      resultOrderId,
    },
  });
};

//会员订单详情
export const orderDoctorList = (current, size, doctorId, resultOrderId) => {
  return request({
    url: "/api/blade-svc/orderDoctor/list",
    method: "get",
    params: {
      current,
      size,
      doctorId,
      resultOrderId,
    },
  });
};

// 平台服务报告
export const resultOrderPlatformList = (resultOrderId) => {
  return request({
    url: "/api/blade-svc/resultOrderPlatformDetailPoint/list",
    method: "get",
    params: {
      resultOrderId,
    },
  });
};
