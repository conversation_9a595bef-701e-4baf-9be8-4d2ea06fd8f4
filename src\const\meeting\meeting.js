export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  viewBtnIcon: " ",
  selection: true,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  dialogClickModal: false,
  menuWidth: 160,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "活动编号",
      prop: "code",
      type: "input",
      search: true,
      fixed: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "活动名称",
      prop: "name",
      type: "input",
      width: "140",
      overHidden: true,
    },
    {
      label: "活动类型",
      prop: "type",
      type: "input",
      width: "100",
      overHidden: true,
    },
    {
      label: "支付方式",
      prop: "payType",
      type: "select",
      search: true,
      searchOrder: 1,
      dicData: [
        {
          label: "积分",
          value: 1,
        },
        {
          label: "非积分",
          value: 2,
        },
      ],
      width: "120",
      overHidden: true,
    },
    {
      label: "举办形式",
      prop: "format",
      type: "select",
      search: true,
      dicData: [
        {
          label: "线上",
          value: 1,
        },
        {
          label: "线下",
          value: 2,
        },
      ],
      width: "80",
      overHidden: true,
    },
    {
      label: "活动金额",
      prop: "budgetAmount",
      type: "input",
      width: "100",
      overHidden: true,
    },
    {
      label: "会议状态",
      prop: "meetingStatus",
      type: "select",
      search: true,
      dicData: [
        {
          label: "计划中",
          value: 1,
        },
        {
          label: "进行中",
          value: 2,
        },
        {
          label: "已结束",
          value: 3,
        },
        {
          label: "已变更",
          value: 4,
        },
        {
          label: "已取消",
          value: 5,
        },
        {
          label: "已否决",
          value: 6,
        },
      ],
      width: "80",
      overHidden: true,
    },
    {
      label: "活动状态",
      prop: "planStatus",
      type: "select",
      search: true,
      dicData: [
        {
          label: "计划中",
          value: 0,
        },
        {
          label: "进行中",
          value: 1,
        },
        {
          label: "已结束",
          value: 2,
        },
      ],
      width: "80",
      overHidden: true,
    },
    {
      label: "计划开始时间",
      prop: "invitationStartDate",
      type: "input",
      width: "120",
      overHidden: true,
    },
    {
      label: "计划结束时间",
      prop: "invitationEndDate",
      type: "input",
      width: "120",
      overHidden: true,
    },
    {
      label: "产品名",
      prop: "baseProductName",
      type: "input",
      search: true,
      width: "100",
      overHidden: true,
    },
    {
      label: "产品线",
      prop: "baseProductLine",
      type: "input",
      search: true,
      width: "80",
      overHidden: true,
    },
    {
      label: "客户名称",
      prop: "entrustedCompanyName",
      type: "input",
      width: "100",
      overHidden: true,
    },
    {
      label: "会议负责人",
      prop: "baseEmployeeName",
      type: "input",
      search: true,
      width: "100",
      overHidden: true,
    },
    {
      label: "负责人部门",
      prop: "baseDepartmentName",
      type: "input",
      width: "100",
      overHidden: true,
    },

    {
      label: "提交时间",
      prop: "submitTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: "120",
      overHidden: true,
    },
    // {
    //   fixed: "right",
    //   label: "提交状态",
    //   prop: "submitType",
    //   type: "select",
    //   search: true,
    //   searchOrder: 1,
    //   dicData: [
    //     {
    //       label: "待提交",
    //       value: 1,
    //     },
    //     {
    //       label: "已提交",
    //       value: 2,
    //     },
    //     {
    //       label: "驳回",
    //       value: 3,
    //     },
    //   ],
    //   width: "80",
    //   overHidden: true,
    // },
    // {
    //   label: "初审人",
    //   prop: "approvalOfficer",
    //   type: "input",
    //   search: true,
    //
    //   overHidden: true,
    // },
    // {
    //   fixed: "right",
    //   label: "初审状态",
    //   prop: "approvalStatus",
    //   type: "select",
    //   search: true,
    //   searchOrder: 3,
    //   dicData: [
    //     {
    //       label: "待审核",
    //       value: 0,
    //     },
    //     {
    //       label: "通过",
    //       value: 1,
    //     },
    //     {
    //       label: "驳回",
    //       value: 2,
    //     },
    //   ],
    //   width: "80",
    //   overHidden: true,
    // },
    // {
    //   label: "初审时间",
    //   prop: "approvalDate",
    //   type: "datetime",
    //   format: "yyyy-MM-dd HH:mm:ss",
    //   valueFormat: "yyyy-MM-dd HH:mm:ss",
    //   width: "120",
    //   overHidden: true,
    // },
    {
      label: "审核人",
      prop: "confirmer",
      type: "input",
      search: true,

      overHidden: true,
    },
    {
      fixed: "right",
      label: "审核状态",
      prop: "confirmStatus",
      type: "select",
      search: true,
      searchOrder: 2,
      dicData: [
        {
          label: "待验收",
          value: 0,
        },
        {
          label: "通过",
          value: 1,
        },
        {
          label: "驳回",
          value: 2,
        },
      ],
      width: "80",
      overHidden: true,
    },
    {
      label: "审核时间",
      prop: "confirmDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      width: "120",
      overHidden: true,
    },
    {
      fixed: "right",
      label: "业务部门",
      prop: "baseDepartment",
      search: true,
      type: "tree",
      hide: true,
      dicData: [],
      multiple: true,
      checkStrictly: true,
      leafOnly: false,
      addDisabled: false,
      props: {
        label: "title",
        value: "id",
        width: "120",
        overHidden: true,
      },
      width: "120",
      overHidden: true,
    },
  ],
};
