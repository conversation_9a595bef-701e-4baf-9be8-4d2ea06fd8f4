<template>
  <div style="overflow-y: auto; height: 100vh">
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <avue-form
            ref="form"
            v-if="!validatenull(form)"
            :option="option"
            v-model="form"
            @submit="submit"
          >
          </avue-form>
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>
        <el-tab-pane label="活动讲者" name="2">
          <!-- 邀请讲者 -->
          <avue-crud
            :page.sync="meetingDoctorPage"
            :option="meetingDoctorOption"
            :data="meetingDoctorList"
            @current-change="currentChangeMeetingDoctor"
            @size-change="sizeChangeMeetingDoctor"
            @refresh-change="refreshChangeMeetingDoctor"
            ref="meetingDoctor"
          >
            <template slot="doctorName" slot-scope="{ row }">
              <div class="to-view" @click="toDoctorView(row)">
                <a readonly>
                  {{ row.doctorName }}
                </a>
              </div>
            </template>
            <template slot="coursework" slot-scope="{ row }">
              <div style="cursor: pointer" v-if="row.coursework">
                <el-tag @click="previewCoursework(row.coursework)"
                  >预览课件</el-tag
                >
              </div>
              <div v-else>无</div>
            </template>
            <template slot="doctorAgreement" slot-scope="{ row }">
              <div style="cursor: pointer" v-if="row.doctorAgreement">
                <el-tag @click="previewCoursework(row.doctorAgreement)"
                  >预览合同</el-tag
                >
              </div>
              <div v-else>无</div>
            </template>
            <template slot="agreementStatus" slot-scope="{ row }">
              <el-tag v-if="row.agreementStatus == 0" size="small"
                >待签署</el-tag
              >
              <el-tag
                v-if="row.agreementStatus == 1"
                type="success"
                size="small"
                >同意</el-tag
              >
              <el-tag v-if="row.agreementStatus == 2" type="danger" size="small"
                >拒绝</el-tag
              >
            </template>
          </avue-crud>
        </el-tab-pane>
        <el-tab-pane label="参会人员记录" name="4">
          <!-- 参会人员记录 -->
          <avue-crud
            :page.sync="meetingAttendPage"
            :option="meetingAttendOption"
            :data="meetingAttendList"
            @current-change="currentChangeMeetingAttend"
            @size-change="sizeChangeMeetingAttend"
            @refresh-change="refreshChangeMeetingAttend"
            ref="meetingAttend"
          >
          </avue-crud>
        </el-tab-pane>
        <el-tab-pane label="会议凭证材料" name="6">
          <!-- 会议附件 -->
          <avue-crud
            :page.sync="meetingEvidencePage"
            :option="meetingEvidenceOption"
            :data="meetingEvidenceList"
            @current-change="currentChangeMeetingEvidence"
            @size-change="sizeChangeMeetingEvidence"
            @refresh-change="refreshChangeMeetingEvidence"
            :permission="permissionList"
            ref="meetingEvidence"
          >
            <template slot-scope="{ type, size, row, index }" slot="menu">
              <el-button
                v-if="permission.meeting_meetingEvidence_view"
                :size="size"
                type="text"
                @click="toOpen(row.attachLink)"
                >预览
              </el-button>
            </template>
          </avue-crud>
        </el-tab-pane>
        <!--        <el-tab-pane label="腾讯会议预约" name="7">-->
        <!--          &lt;!&ndash; 腾讯会议信息 &ndash;&gt;-->
        <!--          <avue-crud-->
        <!--            :page.sync="meetingTencentPage"-->
        <!--            :option="meetingTencentOption"-->
        <!--            :data="meetingTencentList"-->
        <!--            @current-change="currentChangeMeetingTencent"-->
        <!--            @size-change="sizeChangeMeetingTencent"-->
        <!--            @refresh-change="refreshChangeMeetingTencent"-->
        <!--            ref="meetingTencent"-->
        <!--          >-->
        <!--          </avue-crud>-->
        <!--        </el-tab-pane>-->
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/meeting/meeting";
//邀请讲者
import { getList as getMeetingDoctorList } from "@/api/meetingdoctor/meetingDoctor";
//参会人员记录
import { getList as getMeetingAttendList } from "@/api/meetingattend/meetingAttend";
//会议附件
import { getList as getMeetingEvidenceList } from "@/api/meetingevidence/meetingEvidence";
//腾讯会议
import { getList as getMeetingTencentList } from "@/api/meetingtencent/meetingTencent";
//议程信息
import { getList as getMeetingAgendaList } from "@/api/meetingagenda/meetingAgenda";
import { mapGetters } from "vuex";
import { Base64 } from "js-base64";
export default {
  props: {
    sfeId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      activeName: "1",
      id: "",
      form: {},
      option: {
        searchShowBtn: false,
        columnBtn: false,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        column: [
          {
            label: "会议编号",
            prop: "code",
            type: "input",
          },
          {
            label: "会议名称",
            prop: "name",
            type: "input",
          },
          {
            label: "会议类型",
            prop: "type",
            type: "input",
          },

          {
            label: "举办形式",
            prop: "format",
            type: "select",
            dicData: [
              {
                label: "线上",
                value: 1,
              },
              {
                label: "线下",
                value: 2,
              },
            ],
          },
          {
            label: "会议目的",
            prop: "purpose",
            type: "input",
          },
          {
            label: "预算金额",
            prop: "budgetAmount",
            type: "input",
          },

          {
            label: "计划开始时间",
            prop: "invitationStartDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "计划结束时间",
            prop: "invitationEndDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "活动发布时间",
            prop: "releaseTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "活动超期关闭时间",
            prop: "overdueCloseTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "会议状态",
            prop: "meetingStatus",
            type: "select",
            dicData: [
              {
                label: "未开始",
                value: 1,
              },
              {
                label: "进行中",
                value: 2,
              },
              {
                label: "已结束",
                value: 3,
              },
              {
                label: "已变更",
                value: 4,
              },
              {
                label: "已取消",
                value: 5,
              },
              {
                label: "已否决",
                value: 6,
              },
            ],
          },
          {
            label: "会议简介",
            prop: "description",
            type: "input",
          },
          {
            label: "会议主办方",
            prop: "organizerName",
            type: "input",
          },
          {
            label: "客户名称",
            prop: "entrustedCompanyName",
            type: "input",
          },
          {
            label: "活动负责人",
            prop: "baseEmployeeName",
            type: "input",
          },
          {
            label: "负责人部门",
            prop: "baseDepartmentName",
            type: "input",
          },
          {
            label: "产品名称",
            prop: "baseProductName",
            type: "input",
          },
          {
            label: "产品线",
            prop: "baseProductLine",
            type: "input",
          },

          // {
          //   label: "会议地址-省",
          //   prop: "provinceCode",
          //   type: "input",
          // },
          // {
          //   label: "会议地址-市",
          //   prop: "cityCode",
          //   type: "input",
          // },
          {
            label: "会议详细地址",
            prop: "address",
            type: "input",
          },

          {
            label: "会议总结",
            prop: "summary",
            type: "input",
          },
          // {
          //   label: "初审时间",
          //   prop: "approvalDate",
          //   type: "datetime",
          //   format: "yyyy-MM-dd HH:mm:ss",
          //   valueFormat: "yyyy-MM-dd HH:mm:ss",
          // },
          // {
          //   label: "初审状态",
          //   prop: "approvalStatus",
          //   type: "select",
          //   dicData: [
          //     {
          //       label: "待审核",
          //       value: 0,
          //     },
          //     {
          //       label: "通过",
          //       value: 1,
          //     },
          //     {
          //       label: "驳回",
          //       value: 2,
          //     },
          //   ],
          // },
          // {
          //   label: "初审说明",
          //   prop: "approvalRemark",
          //   type: "input",
          // },
          // {
          //   label: "初审人",
          //   prop: "approvalOfficer",
          //   type: "input",
          // },
          {
            label: "审核时间",
            prop: "confirmDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "审核状态",
            prop: "confirmStatus",
            type: "select",
            dicData: [
              {
                label: "待验收",
                value: 0,
              },
              {
                label: "通过",
                value: 1,
              },
              {
                label: "驳回",
                value: 2,
              },
            ],
          },
          // {
          //   label: "审核说明",
          //   prop: "confirmResult",
          //   type: "input",
          // },
          {
            label: "审核人",
            prop: "confirmer",
            type: "input",
          },
        ],
      },
      //邀请讲者信息
      meetingDoctorPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      meetingDoctorOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "部门",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职称",
            prop: "professional",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "讲者级别",
            prop: "doctorLevel",
            type: "input",
          },
          {
            label: "讲者角色",
            prop: "identityRole",
            type: "select",
            dicData: [
              {
                label: "主讲者",
                value: 1,
              },
              {
                label: "主席或主持人",
                value: 2,
              },
              {
                label: "点评人",
                value: 3,
              },
            ],
          },
          {
            label: "合同费用",
            prop: "doctorFee",
            type: "input",
          },
          {
            label: "讲者课件",
            prop: "coursework",
            type: "input",
          },
          {
            label: "签署状态",
            prop: "agreementStatus",
            type: "select",
            dicData: [
              {
                label: "同意",
                value: 1,
              },
              {
                label: "拒绝",
                value: 2,
              },
              {
                label: "作废",
                value: 3,
              },
            ],
          },
          {
            label: "签署时间",
            prop: "agreementTime",
            type: "input",
          },
          {
            label: "合同协议",
            prop: "doctorAgreement",
            type: "input",
          },
        ],
      },
      meetingDoctorList: [],
      //参会人员记录
      meetingAttendPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      meetingAttendOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "参会角色",
            prop: "roleType",
            type: "select",
            dicData: [
              {
                label: "讲者",
                value: 0,
              },
              {
                label: "外部参会客户",
                value: 1,
              },
              {
                label: "内部参会人员",
                value: 2,
              },
            ],
          },
          {
            label: "姓名",
            prop: "name",
            type: "input",
          },
          {
            label: "单位",
            prop: "organization",
            type: "input",
          },
          {
            label: "部门",
            prop: "department",
            type: "input",
          },
          {
            label: "联系电话",
            prop: "phone",
            type: "input",
          },
          {
            label: "签到状态",
            prop: "signInStatus",
            type: "select",
            dicData: [
              {
                label: "未签到",
                value: 1,
              },
              {
                label: "已签到",
                value: 2,
              },
            ],
          },
          {
            label: "签到时间",
            prop: "signInTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "签到位置",
            prop: "signInLocation",
            type: "input",
          },

          {
            label: "首次入会时间",
            prop: "firstJoinTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "最后离开时间",
            prop: "leaveTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "参会时长(分钟)",
            prop: "duration",
            type: "input",
          },
        ],
      },
      meetingAttendList: [],

      //会议附件
      meetingEvidencePage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },

      meetingEvidenceOption: {
        grid: true,
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        addBtnIcon: " ",
        refreshBtn: true,
        viewBtn: false,
        delBtn: false,
        menu: true,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "凭证名称",
            prop: "name",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入凭证名称",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "凭证材料",
            prop: "attachLink",
            listType: "picture-card",
            // accept: "image/png, image/jpeg, audio/mp4, video/mp4",
            type: "upload",
            action: "/api/blade-resource/oss/endpoint/put-file",
            propsHttp: {
              res: "data",
              url: "link",
            },
            rules: [
              {
                required: true,
                message: "请上传文件",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "凭证形式",
            prop: "attachType",
            type: "select",
            dicData: [
              {
                label: "图片",
                value: 1,
              },
              {
                label: "视频",
                value: 2,
              },
              // {
              //   label: "文档",
              //   value: 3,
              // },
            ],
            addDisplay: false,
            rules: [
              {
                required: true,
                message: "请选择凭证形式",
                trigger: ["blur", "change"],
              },
            ],
            hide: true,
          },
        ],
      },
      meetingEvidenceList: [],
      //腾讯会议
      meetingTencentPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      meetingTencentOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: false,
        delBtn: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "会议主题",
            prop: "meetingName",
            type: "input",
          },

          // {
          //   label: "是否开启录制",
          //   prop: "attachType",
          //   type: "select",
          //   dicData: [
          //     {
          //       label: "不开启",
          //       value: 0,
          //     },
          //     {
          //       label: "开启",
          //       value: 1,
          //     },
          //   ],
          // },
          {
            label: "预约开始时间",
            prop: "startTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "预约结束时间",
            prop: "endTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          // {
          //   label: "是否允许成员在主持人进会前加入会议",
          //   prop: "settingAllowInBeforeHost",
          //   type: "select",
          //   dicData: [
          //     {
          //       label: "不允许",
          //       value: 0,
          //     },
          //     {
          //       label: "允许",
          //       value: 1,
          //     },
          //   ],
          // },
          {
            label: "会议主持人",
            prop: "name",
            type: "input",
          },
          {
            label: "预约状态",
            prop: "meetingStatus",
            type: "input",
            dicData: [
              {
                label: "生效中",
                value: 1,
              },
              {
                label: "已取消",
                value: 2,
              },
              {
                label: "已作废",
                value: 3,
              },
              {
                label: "已结束",
                value: 4,
              },
            ],
          },
        ],
      },
      meetingTencentList: [],

      //议程信息
      meetingAgendaPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      meetingAgendaOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "议程主题",
            prop: "title",
            type: "input",
          },
          // {
          //   label: "议程级别",
          //   prop: "level",
          //   type: "input",
          // },
          {
            label: "议程开始时间",
            prop: "startTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "议程结束时间",
            prop: "endTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "议程时长",
            prop: "duration",
            type: "input",
          },
          {
            label: "讲者姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "议程地点",
            prop: "address",
            type: "input",
          },
          {
            label: "议程顺序",
            prop: "agendaOrder",
            type: "input",
          },
        ],
      },
      meetingAgendaList: [],
    };
  },
  created() {
    this.id = this.sfeId;
    this.getDetail();
  },

  computed: {
    ...mapGetters(["permission"]),
    meetingCostPermissionList() {
      return {
        addBtn: this.vaildData(this.permission.meeting_meetingCost_add, false),
        viewBtn: this.vaildData(
          this.permission.meeting_meetingCost_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.meeting_meetingCost_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.meeting_meetingCost_edit,
          false
        ),
      };
    },
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.meeting_meetingEvidence_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.meeting_meetingEvidence_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.meeting_meetingEvidence_delete,
          false
        ),
        editBtn: false,
      };
    },
  },
  methods: {
    //去医师详情
    toDoctorView(row) {
      this.$router.push({
        path: `/detailSfe/${row.doctorId ? row.doctorId : 0}/1`,
      });
    },
    previewCoursework(i) {
      if (i) {
        window.open(
          "http://1.94.42.192:8886/onlinePreview?url=" +
            encodeURIComponent(Base64.encode(i))
        );
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    handleClick(tabs) {
      if (tabs.name == 2) {
        //邀请讲者
        this.getMeetingDoctorList(this.meetingDoctorPage);
      } else if (tabs.name == 3) {
        //议程信息
        this.getMeetingAgendaList(this.meetingAgendaPage);
      } else if (tabs.name == 4) {
        //参会人员记录
        this.getMeetingAttendList(this.meetingAttendPage);
      } else if (tabs.name == 6) {
        //会议附件
        this.getMeetingEvidenceList(this.meetingEvidencePage);
      } else if (tabs.name == 7) {
        //腾讯会议
        this.getMeetingTencentList(this.meetingTencentPage);
      }
    },
    // 邀请讲者
    getMeetingDoctorList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingDoctorList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingDoctorPage.total = data.total;
        this.meetingDoctorList = data.records;
        this.loading = false;
        this.$nextTick(() => {
          if (!this.$refs.meetingDoctor.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.meetingDoctor.doLayout();
          }
        });
      });
    },
    refreshChangeMeetingDoctor() {
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    //分页
    currentChangeMeetingDoctor(currentPage) {
      this.meetingDoctorPage.currentPage = currentPage;
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    sizeChangeMeetingDoctor(pageSize) {
      this.meetingDoctorPage.pageSize = pageSize;
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    //end
    //参会人员记录
    getMeetingAttendList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingAttendList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingAttendPage.total = data.total;
        this.meetingAttendList = data.records;
        this.loading = false;
        this.$nextTick(() => {
          if (!this.$refs.meetingAttend.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.meetingAttend.doLayout();
          }
        });
      });
    },
    refreshChangeMeetingAttend() {
      this.getMeetingAttendList(this.meetingAttendPage);
    },
    //分页
    currentChangeMeetingAttend(currentPage) {
      this.meetingAttendPage.currentPage = currentPage;
      this.getMeetingAttendList(this.meetingAttendPage);
    },
    sizeChangeMeetingAttend(pageSize) {
      this.meetingAttendPage.pageSize = pageSize;
      this.getMeetingAttendList(this.meetingAttendPage);
    },
    //end
    //会议附件

    // 打开新页面
    toOpen(i) {
      window.open(
        "http://1.94.42.192:8886/onlinePreview?url=" +
          encodeURIComponent(Base64.encode(i))
      );
    },
    getMeetingEvidenceList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingEvidenceList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingEvidencePage.total = data.total;
        this.meetingEvidenceList = data.records;
        this.loading = false;
        this.$nextTick(() => {
          if (!this.$refs.meetingEvidence.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.meetingEvidence.doLayout();
          }
        });
      });
    },
    refreshChangeMeetingEvidence() {
      this.getMeetingEvidenceList(this.meetingEvidencePage);
    },
    //分页
    currentChangeMeetingEvidence(currentPage) {
      this.meetingEvidencePage.currentPage = currentPage;
      this.getMeetingEvidenceList(this.meetingEvidencePage);
    },
    sizeChangeMeetingEvidence(pageSize) {
      this.meetingEvidencePage.pageSize = pageSize;
      this.getMeetingEvidenceList(this.meetingEvidencePage);
    },
    //end
    //腾讯会议
    getMeetingTencentList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingTencentList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingTencentPage.total = data.total;
        this.meetingTencentList = data.records;
        this.meetingTencentList.map((item) => {
          item.startTime = item.startTime * 1000;
          item.endTime = item.endTime * 1000;
          return item;
        });
        this.loading = false;
        this.$nextTick(() => {
          if (!this.$refs.meetingTencent.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.meetingTencent.doLayout();
          }
        });
      });
    },
    //刷新
    refreshChangeMeetingTencent() {
      this.getMeetingTencentList(this.meetingTencentPage);
    },
    //分页
    currentChangeMeetingTencent(currentPage) {
      this.meetingTencentPage.currentPage = currentPage;
      this.getMeetingTencentList(this.meetingTencentPage);
    },
    sizeChangeMeetingTencent(pageSize) {
      this.meetingTencentPage.pageSize = pageSize;
      this.getMeetingTencentList(this.meetingTencentPage);
    },
    //end
    //议程信息
    getMeetingAgendaList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingAgendaList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingAgendaPage.total = data.total;
        this.meetingAgendaList = data.records;
        this.loading = false;
        this.$nextTick(() => {
          if (!this.$refs.meetingAgenda.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.meetingAgenda.doLayout();
          }
        });
      });
    },
    //刷新
    refreshChangeMeetingAgenda() {
      this.getMeetingAgendaList(this.meetingAgendaPage);
    },
    //分页
    currentChangeMeetingAgenda(currentPage) {
      this.meetingAgendaPage.currentPage = currentPage;
      this.getMeetingAgendaList(this.meetingAgendaPage);
    },
    sizeChangeMeetingAgenda(pageSize) {
      this.meetingAgendaPage.pageSize = pageSize;
      this.getMeetingAgendaList(this.meetingAgendaPage);
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },

    submit() {},
  },
};
</script>

<style scoped lang="scss">
/* 预约 */
.date-title {
  display: flex;
  align-items: center;
  font-size: 16px;
}
.date-title :nth-child(2) {
  height: 25px;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100% !important;
}

.button_wrap {
  margin: 0 auto;
  border-radius: 6px;
  border: 1px solid #1d90a2;
  width: 95%;
  display: flex;
  flex-wrap: wrap;
  /* 允许子项换行 */
  align-content: flex-start;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.preinstallItem {
  border-radius: 6px;
  font-size: 13px;
  min-width: 50px;
  line-height: 26px;
  text-align: center;
  border: 1px solid #e5e6eb;
  margin-left: 15px;
  margin-top: 15px;
  padding: 5px 14px;
}

.selectPreinstallItem0 {
  background-color: #fab6b6;
  border-color: #fab6b6;
  cursor: not-allowed;
}

.selectPreinstallItem1 {
  cursor: pointer;
}
.selectPreinstallItem1:hover {
  color: #1d90a2;
  border-color: #bbdee3;
  background-color: #e8f4f6;
}
.selectPreinstallItem2 {
  background-color: #c8c9cc;
  border-color: #c8c9cc;
  cursor: not-allowed;
}
.selectPreinstallItem3 {
  cursor: pointer;
  border: 1px solid transparent;
  background-color: #1d90a2;
  color: white;
}

.selectPreinstallItem3:hover {
  background-color: #1d90a2;
  color: white;
}

.qr-img {
  width: 250px;
  margin: 0 auto;
  img {
    width: 100%;
  }
}
</style>
