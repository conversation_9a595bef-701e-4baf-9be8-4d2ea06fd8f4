<template>
  <div>
    <basic-container>
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        ref="form"
        :option="option"
        v-model="form"
        @submit="submit"
        @reset-change="handleReset"
      >
        <template slot-scope="{ type, disabled }" slot="templateProductName">
          <el-select
            v-model="form.templateProductName"
            @change="templateProductNameChange"
            @focus="templateProductNameFocus"
            filterable
            remote
            reserve-keyword
            placeholder="请选择关联产品"
            :remote-method="remoteMethod"
            :loading="selectLoading"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.productName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </template>
        <template slot-scope="{ type, disabled }" slot="issue">
          <div class="preinstall">
            <div class="title">可用病例征集属性信息库</div>
            <div class="preinstallList">
              <div
                @click="selectPreinstall(item)"
                class="preinstallItem"
                :class="idList.includes(item.id) ? 'selectPreinstallItem' : ''"
                v-for="(item, index) in preinstallList"
                :key="index"
              >
                <div>
                  {{ item.attrName }}
                </div>
              </div>
            </div>
          </div>
        </template>
        <template slot-scope="{ type, disabled }" slot="issueList">
          <div style="height: 600px">
            <div class="title">
              已关联的病例征集属性
              <el-tooltip
                class="item"
                effect="dark"
                content="请点选已关联的行属性对征集的属性进行排序显示，确定后保存"
                placement="right"
              >
                <i class="el-icon-info"></i>
              </el-tooltip>
            </div>
            <div style="width: 95%; margin: 0 auto">
              <avue-crud
                :option="topicOption"
                :data="selectPreinstallList"
                @row-del="rowDel"
              >
                <template slot="attrType" slot-scope="{ row }">
                  <el-tag v-if="row.attrType == 1" type="warning" size="small"
                    >单选</el-tag
                  >
                  <el-tag v-if="row.attrType == 2" type="success" size="small"
                    >多选</el-tag
                  >
                  <el-tag v-if="row.attrType == 3" size="small">文本</el-tag>
                </template>
                <template
                  slot-scope="{ type, size, row, index }"
                  slot="requiredStatus"
                >
                  <div style="cursor: pointer">
                    <el-switch
                      v-model="row.requiredStatus"
                      active-color="#67C23A"
                      inactive-color="#cacdd4"
                      active-text="是"
                      inactive-text="否"
                      :active-value="1"
                      :inactive-value="0"
                    >
                    </el-switch>
                  </div>
                </template>
              </avue-crud>
            </div>
          </div>
        </template>
      </avue-form>
    </basic-container>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { add } from "@/api/casecollectiontemplate/caseCollectionTemplate";
import { getIssueList } from "@/api/casecollectiontemplate/caseCollectionTemplate";
import { getList as getbaseProductList } from "@/api/baseproduct/baseProduct";
export default {
  data() {
    return {
      //关联产品名称
      selectOption: {}, //选中的对象
      options: [],
      selectLoading: false,
      //选中的问题列表
      selectPreinstallList: [],
      idList: [],
      //预设问题
      preinstallList: [],
      id: "",
      //基础信息
      option: {
        labelWidth: "150",
        submitIcon: " ",
        emptyIcon: " ",
        column: [
          {
            label: "客户名称",
            prop: "entrustedCompanyId",
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            dicUrl: "/api/blade-csc/entrustedCompany/getList",
            rules: [
              {
                required: true,
                message: "请选择客户名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "对应产品名称",
            prop: "templateProductName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请选择对应产品名称",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "模版名称/标题",
            prop: "templateName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入模版名称/标题",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "服务项目名称",
            prop: "projectTypeId",
            type: "select",
            dicUrl: "/api/blade-act/activityProjectType/getClinicalCooperation",
            props: {
              label: "projectTypeName",
              value: "id",
            },
            rules: [
              {
                required: true,
                message: "请选择服务项目名称",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            span: 24,
            labelPosition: "top",
            label: "",
            prop: "issue",
            type: "input",
          },
          {
            span: 24,
            labelPosition: "top",
            label: "",
            prop: "issueList",
            type: "input",
          },
        ],
      },
      form: {},
      data: [],
      topicOption: {
        height: "400",
        border: true,
        index: true,
        rowSort: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: false,
        columnBtn: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "病例征集属性名称",
            prop: "attrName",
          },
          {
            label: "属性类型",
            prop: "attrType",
            type: "select",
            dicData: [
              {
                label: "单选",
                value: 1,
              },
              {
                label: "多选",
                value: 2,
              },
              {
                label: "文本",
                value: 3,
              },
            ],
          },
          {
            label: "属性值选项",
            prop: "attrValue",
            type: "input",
          },
          {
            label: "是否必填",
            prop: "requiredStatus",
            type: "switch",
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  created() {
    this.getIssueList();
  },
  methods: {
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    getIssueList() {
      getIssueList().then((res) => {
        let data = res.data;
        data.data.map((item) => {
          return (item.requiredStatus = 0);
        });
        this.preinstallList = data.data;
      });
    },
    //获取关联产品名称下拉项
    initOptions() {
      this.selectLoading = true;
      getbaseProductList(1, 100).then((res) => {
        const data = res.data;
        this.options = data.data.records;
        this.selectLoading = false;
      });
    },
    //关联产品名称
    remoteMethod(query) {
      if (query !== "") {
        let params = {
          productName: query,
        };
        this.selectLoading = true;
        getbaseProductList(1, 100, params).then((res) => {
          const data = res.data;
          this.options = data.data.records;
          this.selectLoading = false;
        });
      } else {
        this.initOptions();
      }
    },
    //获取焦点
    templateProductNameFocus() {
      if (this.options.length == 0) {
        this.initOptions();
      }
    },
    //关联产品名称更改
    templateProductNameChange(value) {
      let obj = this.options.filter((item) => {
        return item.id == value;
      });
      this.selectOption = obj[0];
    },
    //关联产品名称end
    //选择
    selectPreinstall(data) {
      let existStatu = true;
      for (let item of this.selectPreinstallList) {
        if (item.id == data.id) {
          existStatu = false;
          break;
        }
      }
      if (existStatu) {
        this.idList.push(data.id);
        this.selectPreinstallList.push(data);
      }
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.idList = this.idList.filter((item) => {
          return item != row.id;
        });
        this.selectPreinstallList = this.selectPreinstallList.filter((item) => {
          return item.id != row.id;
        });
      });
    },

    submit(form, done) {
      if (this.selectPreinstallList.length == 0) {
        this.$message({
          type: "error",
          message: "请选择预设问题!",
        });
        done();
        return;
      }

      this.$refs.form.validate((valid) => {
        if (valid) {
          form.entrustedCompanyName = this.form.$entrustedCompanyId; //客户名称
          form.projectTypeName = this.form.$projectTypeId; //服务项目名称
          form.templateProductId = this.selectOption.id; //产品id
          form.templateProductName = this.selectOption.productName; //产品名称
          let relEntities = [];
          this.selectPreinstallList.map((item, index) => {
            let obj = {
              attrSort: index + 1,
              caseTemplateAttrId: item.id,
              requiredStatus: item.requiredStatus,
            };
            relEntities.push(obj);
          });
          form.relEntities = relEntities;
          console.log();
          add(form).then(
            () => {
              this.goBack();
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              done();
            },
            (error) => {
              console.log(error);
            }
          );
        }
      });
    },
    handleReset() {
      this.selectPreinstallList = [];
      this.idList = [];
    },
  },
};
</script>

<style scoped lang="scss">
.title {
  margin: 15px 30px;
  font-size: 18px;
  font-weight: 600;
}

.preinstallList {
  margin: 0 auto;
  border-radius: 6px;
  border: 1px solid #1d90a2;
  width: 95%;
  // height: 400px;
  display: flex;
  flex-wrap: wrap;
  /* 允许子项换行 */
  align-content: flex-start;
  padding-bottom: 15px;
}

.preinstallItem {
  cursor: pointer;
  border-radius: 6px;
  font-size: 13px;
  min-width: 50px;
  line-height: 26px;
  text-align: center;
  border: 1px solid #e5e6eb;
  margin-left: 15px;
  margin-top: 15px;
  padding: 5px 14px;
}

.preinstallItem:hover {
  background-color: #f2f3f5;
}

.selectPreinstallItem {
  cursor: not-allowed;
  border: 1px solid transparent;
  background-color: #1d90a2;
  color: white;
}

.selectPreinstallItem:hover {
  background-color: #1d90a2;
  color: white;
}
</style>
