// 手机号验证
const validateLinkTel = (rule, value, callback) => {
  let reg = /^(1\d{10})$/;
  if (value === "") {
    callback(new Error("请输入手机号"));
  } else {
    if (!reg.test(value)) {
      callback(new Error("请输入合法的手机号"));
    } else {
      callback();
    }
  }
};
export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "编号",
      prop: "code",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "姓名",
      prop: "name",
      type: "input",
      search: true,
      maxlength: 20,
      rules: [
        {
          required: true,
          message: "请输入姓名",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "手机号码",
      prop: "phone",
      type: "input",
      search: true,
      rules: [
        // {
        //   required: true,
        //   message: "请输入手机号码",
        //   trigger: ["blur", "change"],
        // },
        {
          validator: validateLinkTel,
          trigger: ["blur", "change"],
        },
      ],
    },

    {
      label: "性别",
      prop: "sex",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=sex",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      rules: [
        {
          required: true,
          message: "请选择性别",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "所在部门",
      prop: "dept",
      type: "input",
      maxlength: 200,
    },
    {
      label: "部门职务",
      prop: "deptDuty",
      type: "input",
      maxlength: 200,
    },
    {
      label: "助理服务项目",
      prop: "serviceProject",
      type: "input",
      maxlength: 200,
    },
    {
      label: "助理服务区域",
      prop: "serviceArea",
      type: "input",
      maxlength: 200,
    },
    {
      label: "启用标识",
      prop: "enable",
      type: "select",
      rules: [
        {
          required: true,
          message: "请选择启用标识",
          trigger: ["blur", "change"],
        },
      ],
      dicData: [
        {
          label: "否",
          value: 0,
        },
        {
          label: "是",
          value: 1,
        },
      ],
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
