import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/summaryTaxCert/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/summaryTaxCert/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-svc/summaryTaxCert/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-svc/summaryTaxCert/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/summaryTaxCert/submit",
    method: "post",
    data: row,
  });
};
export const summaryTaxCertUpload = (row) => {
  return request({
    url: "/api/blade-svc/summaryTaxCert/summaryTaxCertUpload",
    method: "post",
    data: row,
  });
};
export const exportSummaryTaxCert = (summaryTaxCertId) => {
  return request({
    url: "/api/blade-summaryTaxCertDetailDoctor/summaryTaxCertDetailDoctor/export-summaryTaxCertDoctor",
    method: "get",
    params:{
      summaryTaxCertId,
    } 
  });
};
