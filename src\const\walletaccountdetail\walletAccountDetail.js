export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  labelWidth: 150,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  addBtnIcon: " ",
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "时间戳流水号",
      prop: "code",
      addDisplay: false,
      editDisabled: true,
      type: "input",
    },
    {
      label: "客户类型",
      span: 18,
      prop: "customerType",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=customer_type",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      search: true,
      rules: [
        {
          required: true,
          message: "请选择客户类型",
          trigger: "blur",
        },
      ],
    },
    {
      label: "客户姓名",
      prop: "customerName",
      type: "input",
      span: 18,
      rules: [
        {
          required: true,
          message: "请输入客户姓名",
          trigger: "blur",
        },
      ],
    },

    {
      label: "客户身份证号",
      prop: "idCode",
      type: "input",
      span: 18,
      hide: true,
      editDisplay: false, //编辑时是否显示
      viewDisplay: true, //详情时是否显示
      rules: [
        {
          required: true,
          message: "请输入客户身份证号",
          trigger: "blur",
        },
      ],
    },

    {
      label: "记账类型",
      prop: "chargeType",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=charge_type",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      rules: [
        {
          required: true,
          message: "请选择记账类型",
          trigger: "blur",
        },
      ],
    },
    {
      label: "业务类型",
      prop: "businessType",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=business_type",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      rules: [
        {
          required: true,
          message: "请选择业务类型",
          trigger: "blur",
        },
      ],
    },

    // {
    //   label: "流水方式",
    //   prop: "waterType",
    //   type: "select",
    //   addDisplay: false,
    //   dicUrl: "/api/blade-system/dict-biz/dictionary?code=water_type",
    //   props: {
    //     label: "dictValue",
    //     value: "dictKey",
    //   },
    //   dataType: "number",
    // },
    {
      label: "记账金额",
      prop: "balanceAccount",
      type: "input",
    },
    {
      label: "记账时间",
      prop: "accountUpdateTime",
      type: "date",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入退款时间",
          trigger: "blur",
        },
      ],
    },
    {
      label: "凭证",
      prop: "voucherUrl",
      type: "upload",
      listType: "picture-img",
      dataType: "string",
      action: "/api/blade-resource/oss/endpoint/put-file",
      propsHttp: {
        res: "data",
        url: "link",
      },
      rules: [
        {
          required: true,
          message: "请上传凭证",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "对应业务单据编号",
      prop: "businessCode",
      addDisplay: false,
      hide: true,
      type: "input",
    },

    {
      label: "业务描述",
      prop: "remark",
      span: 24,
      minRows: 3,
      hide: true,
      type: "textarea",
    },

    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
