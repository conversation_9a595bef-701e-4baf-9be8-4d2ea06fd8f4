export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: true,
  viewBtn: true,
  viewBtnIcon: " ",
  searchLabelWidth: "130",
  dialogClickModal: false,
  column: [
    {
      label: "自然人结算单编号",
      prop: "code",
      type: "input",
      search: true,
    },
    {
      label: "企业结算单编号",
      prop: "settlementOrderCode",
      type: "input",
      search: true,
    },
    {
      label: "自然人成果单编号",
      prop: "resultOrderDoctorCode",
      type: "input",
    },
    {
      label: "自然人结算单名称",
      prop: "settlementDetailName",
      type: "input",
    },
    {
      label: "会员姓名",
      prop: "doctorName",
      type: "input",
      search: true,
    },
    {
      label: "会员手机号",
      prop: "phone",
      type: "input",
      search: true,
    },
    {
      label: "证件号码",
      prop: "idCardNumber",
      type: "input",
      search: true,
    },
    {
      label: "结算金额",
      prop: "settlementAmount",
      type: "input",
    },

    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      viewDisplay: false,
      hide: true,
    },
  ],
};
