<template>
  <basic-container>
    <avue-crud
      :option="option"
      :search.sync="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
   >
      <!-- 会议状态 -->
      <template slot="meetingStatus" slot-scope="{ row }">
          <el-tag v-if="row.meetingStatus == 1" size="small">计划中</el-tag>
          <el-tag v-if="row.meetingStatus == 2" type="success" size="small"
          >进行中</el-tag
          >
          <el-tag v-if="row.meetingStatus == 3" type="danger" size="small"
          >已结束</el-tag
          >
          <el-tag v-if="row.meetingStatus == 4" type="success" size="small"
          >已变更</el-tag
          >
          <el-tag v-if="row.meetingStatus == 5" type="danger" size="small"
          >已取消</el-tag
          >
          <el-tag v-if="row.meetingStatus == 6" type="danger" size="small"
          >已否决</el-tag
          >
      </template>
      <!-- 活动状态 -->
      <template slot="planStatus" slot-scope="{ row }">
        <el-tag v-if="row.planStatus == 0" size="small">计划中</el-tag>
        <el-tag v-if="row.planStatus == 1" type="success" size="small"
          >进行中</el-tag
        >
        <el-tooltip
          class="item"
          effect="dark"
          :content="
                  row.planEndedMark == 1
                  ? '活动执行完成'
                  : row.planEndedMark == 2
                  ? '活动超期关闭'
                  : row.planEndedMark == 3
                  ? '客户拒签合同'
                  : row.planEndedMark == 4
                  ? '平台主动关闭活动'
                  : row.planEndedMark == 5 || row.planEndedMark == 6
                  ? row.confirmResult
                  : '平台主动关闭活动'
              "
          placement="top"
        >
          <el-tag v-if="row.planStatus == 2" type="danger" size="small"
          >已结束</el-tag
          >
        </el-tooltip>
      </template>
      <!-- 提交状态 -->
      <template slot="submitType" slot-scope="{ row }">
        <el-tag v-if="row.submitType == 1" size="small">待提交</el-tag>
        <el-tag v-if="row.submitType == 2" type="success" size="small"
          >已提交</el-tag
        >
        <el-tag v-if="row.submitType == 3" type="danger" size="small"
          >驳回</el-tag
        >
        <el-tag v-if="row.submitType == 4" type="danger" size="small"
          >平台作废</el-tag
        >
      </template>
      <!-- 初审状态 -->
<!--      <template slot="approvalStatus" slot-scope="{ row }">-->
<!--        <el-tag v-if="row.approvalStatus == 0" size="small">待审核</el-tag>-->
<!--        <el-tag v-if="row.approvalStatus == 1" type="success" size="small"-->
<!--          >通过</el-tag-->
<!--        >-->
<!--        <el-tag v-if="row.approvalStatus == 2" type="danger" size="small"-->
<!--          >驳回</el-tag-->
<!--        >-->
<!--      </template>-->
      <!-- 验收状态 -->
<!--      <template slot="confirmStatus" slot-scope="{ row }">-->
<!--        <el-tag v-if="row.confirmStatus == 0" size="small">待验收</el-tag>-->
<!--        <el-tag v-if="row.confirmStatus == 1" type="success" size="small"-->
<!--          >通过</el-tag-->
<!--        >-->
<!--        <el-tag v-if="row.confirmStatus == 2" type="danger" size="small"-->
<!--          >驳回</el-tag-->
<!--        >-->
<!--      </template>-->
      <!-- <template slot="menuLeft">
        <el-button
          type="warning"
          size="small"
          plain
          icon="el-icon-download"
          @click="handleExport"
          >导 出
        </el-button>
      </template> -->
      <template slot-scope="{ row }" slot="menu">
        <el-button
          v-if="
            permission.meeting_createMeetingQRCode &&
            row.planStatus == 1 &&
            row.agreementStatus == 1 &&
            !(row.meetingStatus == 1 || row.meetingStatus == 4)
          "
          type="text"
          @click="createMeetingQRCode(row)"
          >查看签到码</el-button
        >
        <el-button
          type="text"
          @click="viewApproveRecord(row)"
        >审批记录</el-button>
      </template>
    </avue-crud>
    <el-dialog
      title="审批记录"
      append-to-body
      :visible.sync="showDialog"
      width="75%"
    >
      <avue-crud
        :table-loading="dialogLoading"
        :data="dialogData"
        :option="dialogOption"
        ref="dialogCrud"
      >
        <template slot="approvalType" slot-scope="{ row }">
          <el-tag v-if="row.approvalType == 1" size="small">初审</el-tag>
          <el-tag v-if="row.approvalType == 2" size="small" type="warning">复审</el-tag>
        </template>
        <template slot="approvalStatus" slot-scope="{ row }">
          <el-tag v-if="row.approvalStatus == 1" type="success" size="small"
          >通过</el-tag
          >
          <el-tag v-if="row.approvalStatus == 2" type="danger" size="small"
          >驳回</el-tag
          >
        </template>
      </avue-crud>
      <span slot="footer" class="dialog-footer">
          <el-button @click="showDialog = false">关 闭</el-button>

        </span>
    </el-dialog>
    <el-dialog
      title="查看签到码"
      append-to-body
      :visible.sync="viewDialogVisible"
      width="40%"
    >
      <div class="qr-img">
        <img :src="imageUrl" alt="" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="downloadImage(imageUrl)"
          >下载</el-button
        >
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getPage,
  getDetail,
  add,
  update,
  remove,
  createMeetingQRCode,
} from "@/api/meeting/meeting";
import {getApprovalHistoryList} from "@/api/approvalHistory/approvalHistory";
import option from "@/const/meeting/meeting";
import { mapGetters } from "vuex";
import { tree } from "@/api/entrusteddept/entrustedDept";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { downloadFileBlob } from "@/util/util";

export default {
  data() {
    return {
      //查看
      viewDialogVisible: false,
      imageUrl: "",
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      dialogData: [],
      dialogOption: {
        height: "37vh",
        rowKey: "id",
        border: true,
        index: true,
        menu: false,
        viewBtn: false,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        dialogClickModal: false,
        refreshBtn: false,
        column: [
          {
            label: "审批类型",
            prop: "approvalType",
            type: "input",
          },
          {
            label: "审批状态",
            prop: "approvalStatus",
            type: "input",
          },
          {
            label: "备注",
            prop: "approvalRemark",
            type: "input",
          },
          {
            label: "审批人",
            prop: "createUserName",
            type: "input",
          },
          {
            label: "审批时间",
            prop: "createTime",
            type: "input",
          },
        ],
      },
      showDialog: false,
      dialogLoading: false,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.meeting_add, false),
        viewBtn: this.vaildData(this.permission.meeting_view, false),
        delBtn: this.vaildData(this.permission.meeting_delete, false),
        editBtn: this.vaildData(this.permission.meeting_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.initData();
    if (!this.validatenull(window.sessionStorage.getItem("meetingSearch"))) {
      this.search = JSON.parse(window.sessionStorage.getItem("meetingSearch"));
      this.query = JSON.parse(window.sessionStorage.getItem("meetingSearch"));
      window.sessionStorage.removeItem("meetingSearch");
    }
  },
  //组件销毁
  beforeDestroy() {
    if (!this.validatenull(this.query)) {
      window.sessionStorage.setItem(
        "meetingSearch",
        JSON.stringify(this.query)
      );
    }
  },
  methods: {
    viewApproveRecord(row) {
      this.dialogLoading = true
      this.showDialog = true
      getApprovalHistoryList({ activityId: row.id, businessType: 1  }).then(res => {
        this.dialogData = res.data.data
      }).finally(() => this.dialogLoading = false)
    },
    initData() {
      tree().then((res) => {
        var prop = this.findObject(option.column, "baseDepartment");
        prop.dicData = res.data.data;
        this.orgData = res.data.data;
      });
    },
    getAllChildIdsById(id, data) {
      let _this = this;
      let childIds = [];
      // 递归辅助函数，用于遍历数据并获取子节点ID
      function traverseChildren(nodes) {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].id === id) {
            // 如果当前节点匹配到给定ID，将其子节点ID添加到结果数组中
            if (nodes[i].children && nodes[i].children.length > 0) {
              childIds = childIds.concat(
                _this.getAllChildIdsByIdHelper(nodes[i].children)
              );
            }
          } else if (nodes[i].children && nodes[i].children.length > 0) {
            // 如果当前节点不匹配给定ID，继续向下遍历子节点
            traverseChildren(nodes[i].children);
          }
        }
      }

      traverseChildren([data]); // 调用辅助函数从根节点开始遍历

      return childIds;
    },

    getAllChildIdsByIdHelper(nodes) {
      let childIds = [];
      let _this = this;
      for (let i = 0; i < nodes.length; i++) {
        childIds.push(nodes[i].id);

        if (nodes[i].children && nodes[i].children.length > 0) {
          childIds = childIds.concat(
            _this.getAllChildIdsByIdHelper(nodes[i].children)
          );
        }
      }

      return childIds;
    },
    //生成签到二维码
    createMeetingQRCode(row) {
      this.$message({
        message: "正在生成中,请稍后!",
        duration: 0,
      });
      createMeetingQRCode(row.id, 1).then(
        (res) => {
          this.$message.closeAll();
          if (res.data.success) {
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.imageUrl = res.data.data;
            this.viewDialogVisible = true;
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (err) => {
          console.log(err);
          this.$message.closeAll();
          this.$message({
            type: "error",
            message: "生成失败,请稍后再试或联系管理员",
          });
        }
      );
    },
    // 下载签到码
    downloadImage(imageUrl) {
      downloadFileBlob(imageUrl, "签到码");
    },

    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    handleExport() {
      let downloadUrl = `/api/blade-act-meeting/meeting/export-meeting?${
        this.website.tokenHeader
      }=${getToken()}`;

      let values = {};
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `会议营销活动${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/meeting/detail/${this.form.id}`,
        });
      } else if (["edit"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      //审核时间
      if (!this.validatenull(params.approvalDate)) {
        this.query.approvalDateEndStart = params.approvalDate[0];
        this.query.approvalDateEndEnd = params.approvalDate[1];
        delete this.query.approvalDate;
      }
      //提交时间
      if (!this.validatenull(params.submitTime)) {
        this.query.submitTimeStart = params.submitTime[0];
        this.query.submitTimeEnd = params.submitTime[1];
        delete this.query.submitTime;
      }
      if (params.baseDepartment) {
        let baseDepartmentId = params.baseDepartment.join(",") + ",";
        params.baseDepartment.map((item) => {
          this.orgData.map((item2) => {
            let itemList = this.getAllChildIdsById(item, item2);
            baseDepartmentId += itemList.join(",");
          });
        });
        // 调用方法，传入给定ID和组织架构数据
        params.baseDepartmentIds = baseDepartmentId;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getPage(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.qr-img {
  width: 250px;
  margin: 0 auto;
  img {
    width: 100%;
  }
}
</style>
