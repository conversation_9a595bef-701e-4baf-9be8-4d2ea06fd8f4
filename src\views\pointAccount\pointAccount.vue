<template>
  <div>
    <basic-container>
      <avue-crud :option="option"
                 :search.sync="search"
                 :table-loading="loading"
                 :data="data"
                 :page.sync="page"
                 :before-open="beforeOpen"
                 v-model="form"
                 ref="crud"
                 @row-update="rowUpdate"
                 @row-save="rowSave"
                 @row-del="rowDel"
                 @search-change="searchChange"
                 @search-reset="searchReset"
                 @selection-change="selectionChange"
                 @current-change="currentChange"
                 @size-change="sizeChange"
                 @refresh-change="refreshChange"
                 @on-load="onLoad">
        <template slot-scope="{ row }" slot="menu">
          <el-button
            v-if="permission.customerCertificationRecord_view"
            type="text"
            @click="toView(row)"
          >明细</el-button>
        </template>
      </avue-crud>
    </basic-container>
    <el-dialog
      title="学识明细"
      append-to-body
      @close="closeDialog"
      :visible.sync="showDialog"
      width="75%"
    >
      <avue-crud
        :row-style="rowStyle"
        :table-loading="dialogLoading"
        :data="dialogData"
        :option="dialogOption"
        :page.sync="dialogPage"
        ref="dialogCrud"
        @search-change="dialogSearchChange"
        @search-reset="dialogSearchReset"
        @current-change="dialogCurrentChange"
        @size-change="dialogSizeChange"
        @refresh-change="dialogRefreshChange"
        @selection-change="dialogSelectionChange"
      >
        <template slot-scope="{ row }" slot="type">
          {{ typeMapping[row.type] }}
        </template>
        <template slot-scope="{ row }" slot="pointNum">
          {{ row.type == 7 ? '-' : '+'  }}{{ row.pointNum }}
        </template>
      </avue-crud>
      <span slot="footer" class="dialog-footer">
          <el-button @click="showDialog = false">关 闭</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import {getDetailList, getList} from "@/api/pointAccount/pointAccount";
import option from "@/option/pointAccount/pointAccount";
import {mapGetters} from "vuex";
import 'nprogress/nprogress.css';

const typeMapping = {
  0: '开通账号',
  1: '账号认证',
  2: '发布内容',
  3: '发布评论',
  4: '浏览内容',
  5: '分享内容',
  6: '点赞内容',
  7: '同步商城'
}

export default {
    data() {
      return {
        typeMapping,
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        //弹窗
        dialogLoading: false,
        dialogSelectionList: [],
        doctorIds: [],
        showDialog: false,
        dialogData: [],
        dialogPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0,
        },
        userId: null,
        dialogQuery: {},
        dialogOption: {
          height: "37vh",
          rowKey: "doctorId",
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          reserveSelection: true,
          border: true,
          index: true,
          menu: false,
          viewBtn: false,
          addBtn: false,
          delBtn: false,
          editBtn: false,
          dialogClickModal: false,
          column: [
            {
              label: "类型",
              prop: "type",
            },
            {
              label: "学识",
              prop: "pointNum",
            },
            {
              label: "创建时间",
              prop: "createTime",
            },
          ],
        },
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      goBack() {
        this.$router.$avueRouter.closeTag();
        this.$router.go(-1);
      },
      toView(row) {
        this.userId = row.userId
        this.openDialog()
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        params.name = params.userName
        getList(page.currentPage,
          page.pageSize,
          Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      rowStyle({ row }) {
        if (this.doctorIds.includes(row.id)) {
          return {
            backgroundColor: "#eee",
          };
        }
      },
      //打开弹窗
      openDialog() {
        this.dialogPage.currentPage = 1;
        this.getDialogList()
      },
      //清空搜索
      dialogSearchReset() {
        this.dialogQueryquery = {};
        this.$refs.dialogCrud.toggleSelection();
        this.getDialogList();
      },
      //搜索条件更改
      dialogSearchChange(params, done) {
        this.dialogQueryquery = params;
        this.dialogPage.currentPage = 1;
        this.getDialogList(params);
        done();
      },
      dialogCurrentChange(currentPage) {
        this.dialogPage.currentPage = currentPage;
        this.getDialogList();
      },
      dialogSizeChange(pageSize) {
        this.dialogPage.pageSize = pageSize;
        this.getDialogList();
      },
      //刷新
      dialogRefreshChange() {
        this.getDialogList();
      },
      //选中
      dialogSelectionChange(list) {
        this.dialogSelectionList = list;
      },
      closeDialog() {
        this.$refs.dialogCrud.toggleSelection();
      },
      //获取会员列表
      getDialogList() {
        this.dialogLoading = true;
        getDetailList(this.dialogPage.currentPage,this.dialogPage.pageSize, {  userId: this.userId }).then((res) => {
          const data = res.data.data;
          this.dialogPage.total = data.total;
          this.dialogData = data.records;
          this.showDialog = true;
          this.dialogLoading = false;
          this.$nextTick(() => {
            if (!this.$refs.dialogCrud.gridShow) {
              // myTable是表格的ref属性值
              this.$refs.dialogCrud.doLayout();
            }
          });
        });
      },
    }
  };
</script>

<style>
</style>
