<template>
  <div>
    <basic-container>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div>
          <el-page-header
            @back="goBack"
            :content="$route.name"
          ></el-page-header>
        </div>
      </div>
    </basic-container>
    <basic-container
      ><avue-form
        v-if="form.code != ''"
        ref="form"
        :option="option"
        v-model="form"
        @submit="submit"
      ></avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
  </div>
</template>

<script>
import { getDetail, update } from "@/api/summaryInvoice/summaryInvoice";
import { validatenum } from "@/util/validate.js";
export default {
  data() {
    var checkNum = (rule, value, callback) => {
      if (validatenum(value, 2)) {
        callback(new Error("请输入数字值"));
      } else {
        callback();
      }
    };
    return {
      id: "", //数据id
      form: {
        code: "",
        invoiceFileLink: "",
      },
      checkDate: "",
      query: {},
      loading: false,
      times: null,
      isEfficient: false,
      //基础信息
      option: {
        submitIcon: " ",
        emptyIcon: " ",
        height: "auto",
        calcHeight: 30,
        // submitBtn: false,
        emptyBtn: false,
        tip: false,
        labelWidth: "180",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "详情信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "编码",
                prop: "code",
                type: "input",
                disabled: true,
              },
              {
                label: "名称",
                prop: "name",
                type: "input",
                disabled: true,
              },
              {
                label: "实付总额",
                prop: "invoiceAmount",
                type: "input",
                disabled: true,
                rules: [
                  {
                    required: true,
                    message: "请输入实付总额",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "纳税总额",
                prop: "taxAmount",
                type: "input",
                disabled: true,
                rules: [
                  {
                    required: true,
                    message: "请输入纳税总额",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "代征发票金额",
                prop: "taxInvoiceTotalAmount",
                type: "input",
                disabled: true,
                rules: [
                  {
                    required: true,
                    message: "请输入代征发票金额",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "开票状态",
                prop: "invoiceStatus",
                type: "select",
                disabled: true,
                search: true,
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=summary_invoice_status",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
                rules: [
                  {
                    required: true,
                    message: "请选择开票状态",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "发票号码",
                prop: "invoiceCode",
                type: "input",
                rules: [
                  {
                    required: true,
                    message: "请输入发票号码",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "开票日期",
                prop: "invoiceDate",
                type: "date",
                format: "yyyy-MM-dd",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
                rules: [
                  {
                    required: true,
                    message: "请选择开票日期",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "代征人员数量",
                prop: "collectingNum",
                type: "input",
                rules: [
                  {
                    required: true,
                    message: "请输入代征人员数量",
                    trigger: ["blur", "change"],
                  },
                  {
                    validator: checkNum,
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "发票附件",
                prop: "invoiceUrl",
                type: "upload",
                listType: "picture-card",
                accept: "image/png, image/jpeg",
                dataType: "string",
                limit: 3,
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                rules: [
                  {
                    required: true,
                    message: "请上传发票附件",
                    trigger: ["blur", "change"],
                  },
                ],
              },
            ],
          },
        ],
      },
      data: [],
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail(); //获取详情
  },
  methods: {
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    // 获取数据详情
    getDetail() {
      getDetail(this.id).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            this.form = data.data;
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },

    submit(form, done) {
      update(form).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            this.$message({
              type: "success",
              message: "操作成功",
            });
            done();
            this.goBack();
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
            done();
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
  },
};
</script>

<style></style>
