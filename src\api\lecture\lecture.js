import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/lecture/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getPointList = (current, size, params) => {
  return request({
    url: "/api/blade-act/lecture/pointList",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
//个人订单管理
export const orderDoctorList = (current, size, params) => {
  return request({
    url: "/api/blade-act/lecture/orderDoctorList",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getListPage = (current, size, params) => {
  return request({
    url: "/api/blade-act/lecture/page",
    method: "post",
    data: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/lecture/detail",
    method: "get",
    params: {
      id,
    },
  });
};
export const stopLecture = (id, status) => {
  return request({
    url: "/api/blade-act/lecture/stopLecture",
    method: "get",
    params: {
      id,
      status,
    },
  });
};

export const getFileList = (current, size, params) => {
  return request({
    url: "/api/blade-act/lectureContent/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const remove = (id) => {
  return request({
    url: "/api/blade-act/lecture/remove",
    method: "post",
    params: {
      id,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/lecture/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/lecture/submit",
    method: "post",
    data: row,
  });
};

export const audit = (row) => {
  return request({
    url: "/api/blade-act/lecture/audit",
    method: "post",
    data: row,
  });
};
//重新开始
export const startPlanAgain = (id) => {
  return request({
    url: "/api/blade-act/lecture/startPlanAgain",
    method: "get",
    params: {
      id,
    },
  });
};
