export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  searchLabelWidth: 150,
  border: true,
  index: true,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  delBtnIcon: " ",
  addBtn: false,
  menu: false,
  menuWidth: 180,
  refreshBtn: true,
  dialogClickModal: false,
  column: [
    {
      label: "成果单编号",
      prop: "code",
      type: "input",
      search: true,
    },
    {
      label: "企业名称",
      prop: "entrustedCompanyName",
      type: "input",
    },
    {
      label: "总包方",
      prop: "generalContractor",
      type: "input",
      span: 24,
    },

    {
      label: "开始日期",
      prop: "serviceStartTime",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
    },
    {
      label: "结束日期",
      prop: "serviceEndTime",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
    },
    {
      label: "服务费用金额",
      prop: "actManageFee",
      type: "input",
    },

    {
      label: "报告制作人",
      prop: "serviceManagerName",
      type: "input",
    },
    {
      label: "报告制作日期",
      prop: "createTime",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
    },
    {
      label: "平台服务报告",
      prop: "serviceManagerReport",
      type: "input",
    },
  ],
};
