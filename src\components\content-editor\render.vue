<template>
  <div class="content-render-container">
    <div class="content-detail" v-if="readonly">{{ content }}</div>
    <div class="content-render" v-if="images.length > 0">
      <div class="render-image-wrapper" v-for="(item, index) in images" style="width: 130px; height: 130px">
        <el-image
          class="content-radius"
          :key="item.link"
          :src="item.link"
          :preview-src-list="[`${item.link}`]"
          style="width: 130px; height: 130px"
          fit="cover"
        />
        <i v-if="!readonly" class="el-icon-close remove" @click="remove('image', index)"></i>
      </div>
    </div>
    <div class="content-render" v-if="videos.length > 0">
      <div  v-for="(item, index) in videos" class="render-video-wrapper">
        <video
          class="content-radius"
          :key="item.link"
          :src="item.link"
           controls
      />
      <i v-if="!readonly"  class="el-icon-close remove" @click="remove('video', index)"></i>
      </div>
    </div>
      <div class="content-render" v-if="audios.length > 0">
      <div class="render-audio-wrapper" v-for="(item, index) in audios" :key="item.link">
        <audio
          :src="item.link"
          controls
        />
        <i v-if="!readonly"  class="el-icon-close remove remove-inline" @click="remove('audio', index)"></i>
      </div>
    </div>
    <div class="content-render" v-if="attachments.length > 0">
      <div class="render-a-wrapper" v-for="(item, index) in attachments" :key="item.link">
      <a
        :href="item.link"
        target="_blank"
      >
        {{ item.originalName }}
      </a>
        <i v-if="!readonly"  class="el-icon-close remove remove-inline" @click="remove('attachment', index)"></i>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContentRender',
  data() {
    return {
      fileType: 'image',
    }
  },
  props: {
    value: {
    },
    readonly: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    content() {
      if(!this.value) {
        return null
      }
      return this.value.content || ''
    },
    images() {
      if(!this.value) {
        return []
      }
      return this.getContent('image')
    },
    videos() {
      if(!this.value) {
        return []
      }
      return this.getContent('video')
    },
    audios() {
      if(!this.value) {
        return []
      }
      return this.getContent('audio')
    },
    attachments() {
      return this.getContent('attachment')
    }
  },
  methods: {
    remove(type, index) {
      this.$emit('remove', type, index)
    },
    getContent(type) {
      if(!this.value) {
        return []
      }
      return this.value[type] ? this.value[type] : []
    },
    emitChange(newValue) {
      if(!newValue.content) {
        delete newValue.content
      }
      if(!newValue.image || newValue.image.length === 0) {
        delete newValue.image
      }
      if(!newValue.videl || newValue.videl.length === 0) {
        delete newValue.videl
      }
      if(!newValue.audio || newValue.audio.length === 0) {
        delete newValue.audio
      }
      if(!newValue.attachment || newValue.attachment.length === 0) {
        delete newValue.attachment
      }

      newValue = Object.keys(newValue).length > 0 ? newValue : null
      console.log(newValue)
      this.$emit('input', newValue)
      this.$emit('change', newValue)
    }
  }
}
</script>

<style lang="scss" scoped>
.content-render-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.content-render {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  .render-image-wrapper {
    position: relative;
  }
  .render-video-wrapper {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    video {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%
    }
  }
  .render-audio-wrapper {
    position: relative;
  }
  .render-a-wrapper {
    position: relative;
  }
  .remove {
    position: absolute;
    right: 8px;
    top: 8px;
    cursor: pointer;
  }
  .remove-inline {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: -20px;
  }
  .content-radius {
    border-radius: 8px;
    overflow: hidden;
  }
}
.content-detail {
  line-height: 24px;
}
</style>
