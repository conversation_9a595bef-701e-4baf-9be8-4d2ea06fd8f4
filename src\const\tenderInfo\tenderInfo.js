export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  labelWidth: 150,
  index: true,
  editBtn: false,
  viewBtn: false,
  delBtn: false,
  selection: true,
  addBtn: false,
  dialogClickModal: false,
  column: [
    {
      label: "编号",
      prop: "code",
      type: "input",
      editDisabled: true,
      addDisplay: false, //新增时是否显示
    },
    {
      label: "名称",
      prop: "name",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "企业名称",
      prop: "entrustedCompanyName",
      type: "input",
      search: true,
    },
    {
      label: "招募开始日期",
      prop: "startDate",
      span: 12,
      type: "datetime",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "招募结束日期",
      prop: "endDate",
      span: 12,
      type: "datetime",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "服务开始日期",
      prop: "serviceStartDate",
      span: 12,
      type: "datetime",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "服务结束日期",
      prop: "serviceEndtDate",
      span: 12,
      type: "datetime",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "招募状态",
      prop: "status",
      type: "select",
      search: true,
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=tender_info_status",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      rules: [
        {
          required: true,
          message: "招募状态",
          trigger: "blur",
        },
      ],
    },
    {
      label: "发布日期",
      prop: "publishDate",
      span: 12,
      type: "datetime",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "审核状态",
      prop: "auditStatus",
      type: "select",
      search: true,
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=audit_status",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      rules: [
        {
          required: true,
          message: "审核状态",
          trigger: "blur",
        },
      ],
    },
    {
      label: "审核时间",
      prop: "auditTime",
      span: 12,
      type: "datetime",
      format: "yyyy-MM-dd HH:mm",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
  ],
};
