<template>
  <div>
    <basic-container>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div>
          <el-page-header
            @back="goBack"
            :content="$route.name"
          ></el-page-header>
        </div>
      </div>
    </basic-container>

    <basic-container>
      <avue-crud
        :option="option"
        :table-loading="loading"
        :search.sync="search"
        :data="data"
        :page.sync="page"
        :permission="permissionList"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-del="rowDel"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
      >
        <!-- 支付方式 -->
        <template slot="paymentType" slot-scope="{ row }">
          <el-tag v-if="row.paymentType == 1" size="small">线下</el-tag>
          <el-tag v-if="row.paymentType == 2" type="success" size="small"
            >线上</el-tag
          >
        </template>
        <!-- 记账类型 -->
        <template slot="chargeType" slot-scope="{ row }">
          <el-tag v-if="row.chargeType == 1" type="success" size="small"
            >收入</el-tag
          >
          <el-tag v-if="row.chargeType == 2" type="danger" size="small"
            >支出</el-tag
          >
        </template>
        <template slot="search" slot-scope="{ row, size }">
          <avue-form ref="form" :option="searchOption" v-model="query">
            <template slot="menuForm">
              <el-button
                icon="el-icon-search"
                type="primary"
                @click="searchChange2"
                >搜 索</el-button
              >
              <el-button icon="el-icon-delete" @click="searchReset"
                >清 空</el-button
              >
              <el-button type="primary" @click="handleExportFinance"
                         v-if="permission.entrustedAccountRecord_export"
                >导 出
              </el-button>
            </template>
          </avue-form>
        </template>
        <template slot="menuLeft">
          <el-button
            type="danger"
            size="small"
            icon="el-icon-delete"
            plain
            v-if="permission.entrustedAccountRecord_delete"
            @click="handleDelete"
            >删 除
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from "@/api/entrustedaccountrecord/entrustedAccountRecord";
import option from "@/const/entrustedaccountrecord/entrustedAccountRecord";
import { mapGetters } from "vuex";
import dayjs from "dayjs";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
export default {
  data() {
    return {
      searchOption: {
        labelWidth: 140,
        menuSpan: 6,
        submitText: "搜索",
        submitIcon: "",
        emptyBtn: false,
        submitBtn: false,
        column: [
          {
            label: "支付方式",
            prop: "paymentType",
            type: "select",
            span: 6,
            dicData: [
              {
                label: "线下",
                value: 1,
              },
              {
                label: "线上",
                value: 2,
              },
            ],
          },
          {
            label: "记账类型",
            prop: "chargeType",
            type: "select",
            span: 6,
            dicData: [
              {
                label: "收入",
                value: 1,
              },
              {
                label: "支出",
                value: 2,
              },
            ],
          },
          {
            label: "记账开始时间",
            prop: "startRecordTime",
            type: "date",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            change: ({ value }) => {
              if (
                dayjs(value).valueOf() >
                dayjs(this.query.endRecordTime).valueOf()
              ) {
                this.query.endRecordTime = "";
              }
            },
            span: 6,
          },
          {
            label: "记账结束时间",
            prop: "endRecordTime",
            type: "date",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            span: 6,
            pickerOptions: {
              disabledDate: (time) => {
                let start = dayjs(this.query.startRecordTime).valueOf();
                return time.getTime() < start;
              },
            },
          },
          {
            label: "记账凭证/经办单号",
            prop: "recordVoucher",
            type: "input",
            span: 6,
          },
          {
            label: "记账业务描述",
            prop: "recordDesc",
            type: "input",
            span: 6,
          },
        ],
      },
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      id: "",
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.entrustedAccountRecord_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.entrustedAccountRecord_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.entrustedAccountRecord_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.entrustedAccountRecord_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.id = this.$route.params.id;
  },
  methods: {
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },

    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.$refs.form.resetForm();
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      if (!this.validatenull(params.recordTime)) {
        params.startRecordTime = params.recordTime[0];
        params.endRecordTime = params.recordTime[1];
        delete params.recordTime;
      }
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    searchChange2() {
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.entrustedAccountId = this.id;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleExportFinance() {
      // if (!this.validatenull(this.query.recordTime)) {
      //   startRecordTime = this.query.recordTime[0];
      //   endRecordTime = this.query.recordTime[1];
      //   delete params.recordTime;
      // }
      let values = {
        paymentType: this.query.paymentType,
        chargeType: this.query.chargeType,
        startRecordTime: this.query.startRecordTime,
        endRecordTime: this.query.endRecordTime,
      };
      let downloadUrl = `/api/blade-pay/entrustedAccountRecord/export-entrustedAccountRecord?${
        this.website.tokenHeader
      }=${getToken()}`;
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `资金变动明细数据${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
  },
};
</script>

<style></style>
