<template>
  <div>
    <basic-container>
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        ref="form"
        :option="option"
        v-model="form"
        @submit="submit"
        @reset-change="handleReset"
      >
        <!--<template slot-scope="{ type, disabled }" slot="templateProductName">-->
        <!--<el-select-->
        <!--v-model="form.templateProductName"-->
        <!--@change="templateProductNameChange"-->
        <!--@focus="templateProductNameFocus"-->
        <!--filterable-->
        <!--remote-->
        <!--reserve-keyword-->
        <!--placeholder="请选择关联产品"-->
        <!--:remote-method="remoteMethod"-->
        <!--:loading="selectLoading"-->
        <!--disabled-->
        <!--&gt;-->
        <!--<el-option-->
        <!--v-for="item in options"-->
        <!--:key="item.value"-->
        <!--:label="item.productName"-->
        <!--:value="item.id"-->
        <!--&gt;-->
        <!--</el-option>-->
        <!--</el-select>-->
        <!--</template>-->

        <template slot-scope="{ type, disabled }" slot="issueList">
          <div style="height: 600px">
            <div class="title">
              已关联的临床调研问卷题目
              <el-tooltip
                class="item"
                effect="dark"
                content="请点选已关联的问卷题目对调研的问卷题目进行排序显示，确定后保存"
                placement="right"
              >
                <i class="el-icon-info"></i>
              </el-tooltip>
            </div>
            <div style="width: 95%; margin: 0 auto">
              <avue-crud
                :option="topicOption"
                :data="selectPreinstallList"
                @row-del="rowDel"
                ref="topicCrud"
                :before-open="beforeOpen"
              >
                <template slot="attrType" slot-scope="{ row }">
                  <el-tag v-if="row.attrType == 1" type="warning" size="small"
                    >单选</el-tag
                  >
                  <el-tag v-if="row.attrType == 2" type="success" size="small"
                    >多选</el-tag
                  >
                  <el-tag v-if="row.attrType == 3" size="small">文本</el-tag>
                  <el-tag v-if="row.attrType == 4" size="small">图片</el-tag>
                </template>
                <template
                  slot-scope="{ type, size, row, index }"
                  slot="requiredStatus"
                >
                  <div style="cursor: pointer">
                    <el-switch
                      active-color="#67C23A"
                      inactive-color="#cacdd4"
                      v-model="row.requiredStatus"
                      active-text="是"
                      inactive-text="否"
                      :active-value="1"
                      :inactive-value="0"
                    >
                    </el-switch>
                  </div>
                </template>
              </avue-crud>
            </div>
          </div>
        </template>
      </avue-form>
      <el-dialog
        title="请选择问卷题目类型"
        append-to-body
        @close="closeDialog"
        :visible.sync="showDialog"
        width="75%"
      >
        <avue-crud
          :row-style="rowStyle"
          :table-loading="dialogLoading"
          :data="dialogData"
          :option="dialogOption"
          :page.sync="dialogPage"
          ref="dialogCrud"
          @search-change="dialogSearchChange"
          @search-reset="dialogSearchReset"
          @current-change="dialogCurrentChange"
          @size-change="dialogSizeChange"
          @refresh-change="dialogRefreshChange"
          @selection-change="dialogSelectionChange"
        >
        </avue-crud>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showDialog = false">取 消</el-button>
          <el-button type="primary" @click="saveList()">确 定</el-button>
        </span>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  add,
  getDetail,
} from "@/api/clinicalresearchtemplate/clinicalResearchTemplate";
import { getList } from "@/api/clinicalresearchtemplateattr/clinicalResearchTemplateAttr";
import { getList as getbaseProductList } from "@/api/baseproduct/baseProduct";
export default {
  data() {
    return {
      //关联产品名称
      selectOption: {}, //选中的对象
      options: [],
      selectLoading: false,

      idList: [],
      //预设问题
      preinstallList: [],
      id: "",
      //基础信息
      option: {
        submitIcon: " ",
        emptyIcon: " ",
        labelWidth: "150",
        column: [
          {
            label: "企业名称",
            prop: "entrustedCompanyId",
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            dicUrl: "/api/blade-csc/entrustedCompany/getList",
            rules: [
              {
                required: true,
                message: "请选择企业名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          // {
          //   label: "对应产品名称",
          //   prop: "templateProductName",
          //   type: "input",
          //   rules: [
          //     {
          //       required: true,
          //       message: "请选择对应产品名称",
          //       trigger: ["blur", "change"],
          //     },
          //   ],
          // },

          {
            label: "模版名称/标题",
            prop: "templateName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入模版名称/标题",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "服务项目名称",
            prop: "projectTypeName",
            disabled: true,
            rules: [
              {
                required: true,
                message: "请选择服务项目名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "价格",
            prop: "unitPrice",
            type: "number",
            rules: [
              {
                required: true,
                message: "请输入价格",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            span: 24,
            labelPosition: "top",
            label: "",
            prop: "issueList",
            type: "input",
          },
        ],
      },
      form: {},
      data: [],
      topicOption: {
        height: "400",
        border: true,
        index: true,
        rowSort: true,
        addBtn: true,
        editBtn: false,
        refreshBtn: false,
        columnBtn: false,
        addBtnIcon: " ",
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "问卷题目",
            prop: "attrName",
          },
          {
            label: "题目分类",
            prop: "attrClassifyKey",
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=attr_classify",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            rules: [
              {
                required: true,
                message: "请选择题目分类",
                trigger: "blur",
              },
            ],
          },
          {
            label: "题目类型",
            prop: "attrType",
            type: "select",
            dicData: [
              {
                label: "单选",
                value: 1,
              },
              {
                label: "多选",
                value: 2,
              },
              {
                label: "文本",
                value: 3,
              },
            ],
          },
          {
            label: "作答选项",
            prop: "attrValue",
            type: "input",
          },
          {
            label: "是否必填",
            prop: "requiredStatus",
            type: "switch",
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
          },
        ],
      },
      //选中的问题列表
      dialogQueryquery: {},
      selectPreinstallList: [],
      dialogLoading: false,
      dialogSelectionList: [],
      attrCodes: [],
      showDialog: false,
      dialogData: [],
      dialogPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      dialogQuery: {},
      dialogOption: {
        selectable: (row) => {
          return !this.attrCodes.includes(row.attrCode);
        },
        height: "37vh",
        rowKey: "attrCode",
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        reserveSelection: true,
        border: true,
        index: true,
        menu: false,
        viewBtn: false,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        selection: true,
        dialogClickModal: false,
        searchLabelWidth: 120,
        column: [
          {
            label: "问卷题目编码",
            prop: "attrCode",
            type: "input",
          },
          {
            label: "问卷题目分类",
            prop: "attrClassifyKey",

            type: "select",
            search: true,
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=attr_classify",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            rules: [
              {
                required: true,
                message: "请选择题目分类",
                trigger: "blur",
              },
            ],
          },
          {
            label: "问卷题目名称",
            prop: "attrName",
            type: "input",
            search: true,
          },
          {
            label: "问卷题目类型",
            prop: "attrType",
            type: "select",

            dicData: [
              {
                label: "单选",
                value: 1,
              },
              {
                label: "多选",
                value: 2,
              },
              {
                label: "文本",
                value: 3,
              },
            ],
          },
          {
            label: "问卷题目选项",
            prop: "attrValue",
            type: "input",
          },
        ],
      },
      //弹窗End
    };
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  created() {
    this.id = this.$route.params.id;
    this.initOptions();
    this.getDetail();
  },
  methods: {
    //弹窗
    rowStyle({ row }) {
      if (this.attrCodes.includes(row.attrCode)) {
        return {
          backgroundColor: "#eee",
        };
      }
    },
    //清空搜索
    dialogSearchReset() {
      this.dialogQueryquery = {};
      this.getDialogList();
    },
    //搜索条件更改
    dialogSearchChange(params, done) {
      this.dialogQueryquery = params;
      this.dialogPage.currentPage = 1;
      this.getDialogList(params);
      done();
    },
    dialogCurrentChange(currentPage) {
      this.dialogPage.currentPage = currentPage;
      this.getDialogList();
    },
    dialogSizeChange(pageSize) {
      this.dialogPage.pageSize = pageSize;
      this.getDialogList();
    },
    //刷新
    dialogRefreshChange() {
      this.getDialogList();
    },
    //选中
    dialogSelectionChange(list) {
      this.dialogSelectionList = list;
    },
    closeDialog() {
      this.$refs.dialogCrud.toggleSelection();
    },
    //保存题目列表
    saveList() {
      let list = [];
      if (this.dialogSelectionList.length > 0) {
        this.dialogSelectionList.map((item) => {
          item.requiredStatus = 1;
          list.push(item);
        });
        this.selectPreinstallList = this.selectPreinstallList.concat(
          JSON.parse(JSON.stringify(list))
        );
      }
      this.selectionClear();
      this.$nextTick(() => {
        if (!this.$refs.topicCrud.gridShow) {
          // myTable是表格的ref属性值
          this.$refs.topicCrud.refreshTable();
          this.$refs.topicCrud.doLayout();
        }
      });
      this.showDialog = false;
    },
    //取消选中
    selectionClear() {
      this.$refs.topicCrud.toggleSelection();
    },
    beforeOpen(done, type) {
      if (type == "add") {
        this.openDialog();
      } else {
        done();
      }
    },
    //打开会员弹窗
    openDialog() {
      this.dialogPage.currentPage = 1;
      this.getSelected();
    },
    //获取已经选中题目id
    getSelected() {
      this.attrCodes = [];
      this.selectPreinstallList.map((item) => {
        this.attrCodes.push(item.attrCode);
      });
      this.getDialogList();
    },

    //获取题库列表
    getDialogList(params = {}) {
      this.dialogLoading = true;
      getList(
        this.dialogPage.currentPage,
        this.dialogPage.pageSize,
        Object.assign(params, this.dialogQueryquery)
      ).then((res) => {
        const data = res.data.data;
        this.dialogPage.total = data.total;
        this.dialogData = data.records;
        this.showDialog = true;
        this.dialogLoading = false;
        this.$nextTick(() => {
          if (!this.$refs.dialogCrud.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.dialogCrud.doLayout();
          }
        });
      });
    },

    //弹窗end
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          _this.form.templateProductName = _this.form.templateProductId;
          _this.form.projectTypeName = "临床调研";
          this.selectPreinstallList = JSON.parse(
            JSON.stringify(res.data.data.attrVOList)
          );
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    //获取关联产品名称下拉项
    initOptions() {
      this.selectLoading = true;
      getbaseProductList(1, 100).then((res) => {
        const data = res.data;
        this.options = data.data.records;
        this.selectLoading = false;
      });
    },
    //关联产品名称
    remoteMethod(query) {
      if (query !== "") {
        let params = {
          productName: query,
        };
        this.selectLoading = true;
        getbaseProductList(1, 100, params).then((res) => {
          const data = res.data;
          this.options = data.data.records;
          this.selectLoading = false;
        });
      } else {
        this.initOptions();
      }
    },
    //获取焦点
    templateProductNameFocus() {
      if (this.options.length == 0) {
        this.initOptions();
      }
    },
    //关联产品名称更改
    templateProductNameChange(value) {
      let obj = this.options.filter((item) => {
        return item.id == value;
      });
      this.selectOption = obj[0];
    },
    //关联产品名称end
    //选择
    selectPreinstall(data) {
      let existStatu = true;
      for (let item of this.selectPreinstallList) {
        if (item.id == data.id) {
          existStatu = false;
          break;
        }
      }
      if (existStatu) {
        this.idList.push(data.id);
        this.selectPreinstallList.push(data);
      }
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.idList = this.idList.filter((item) => {
          return item != row.id;
        });
        this.selectPreinstallList = this.selectPreinstallList.filter((item) => {
          return item.id != row.id;
        });
      });
    },

    submit(form, done) {
      if (this.selectPreinstallList.length == 0) {
        this.$message({
          type: "error",
          message: "请选择预设问题!",
        });
        done();
        return;
      }

      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.selectOption) {
            form.templateProductId = this.selectOption.id; //产品id
            form.templateProductName = this.selectOption.productName; //产品名称
          }
          form.entrustedCompanyName = this.form.$entrustedCompanyId; //企业名称
          form.projectTypeName = this.form.$projectTypeId; //服务项目名称
          let relEntities = [];
          this.selectPreinstallList.map((item, index) => {
            let obj = {
              attrSort: index + 1,
              researchTemplateAttrId: item.id,
              requiredStatus: item.requiredStatus,
            };
            relEntities.push(obj);
          });
          form.relEntities = relEntities;
          add(form).then(
            () => {
              this.goBack();
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              done();
            },
            (error) => {
              console.log(error);
            }
          );
        }
      });
    },
    handleReset() {
      this.selectPreinstallList = [];
      this.idList = [];
    },
  },
};
</script>

<style scoped lang="scss">
.title {
  margin: 15px 30px;
  font-size: 18px;
  font-weight: 600;
}
</style>
