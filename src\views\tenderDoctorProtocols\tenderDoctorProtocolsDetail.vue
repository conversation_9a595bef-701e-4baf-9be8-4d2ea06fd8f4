<template>
  <div>
    <basic-container>
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container
      ><avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
      >
        <template slot-scope="{}" slot="doctorName">
          <div class="to-view" @click="toAuthenticationDoctorView()">
            <a readonly>
              {{ form.doctorName }}
            </a>
          </div>
        </template>
        <template slot-scope="{}" slot="tenderInfoName">
          <div class="to-view" @click="toDoctorTenderView()">
            <a readonly>
              {{ form.tenderInfoName }}
            </a>
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
    <basic-container v-if="form.protocolFile">
      <iframe
        :src="form.protocolFile"
        width="100%"
        :height="iframeHeight"
        title="协议"
        frameBorder="no"
        border="0"
        marginWidth="0"
        marginHeight="0"
        scrolling="no"
        allowTransparency="yes"
      ></iframe>
    </basic-container>
    <div style="height: 5vh"></div>
  </div>
</template>

<script>
import { getDetail } from "@/api/tenderDoctorProtocols/tenderDoctorProtocols";
export default {
  data() {
    return {
      iframeHeight: window.innerHeight - 180,
      option: {
        labelWidth: "150",
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        editBtn: false,
        column: [
          {
            label: "编号",
            prop: "code",
            type: "input",
            editDisabled: true,
            addDisplay: false, //新增时是否显示
          },
          {
            label: "合同名称",
            prop: "name",
            type: "input",
          },
          {
            label: "招募名称",
            search: true,
            prop: "tenderInfoName",
            type: "input",
          },

          {
            label: "会员名称",
            prop: "doctorName",
            type: "input",
            search: true,
          },
          {
            label: "合同模板类型",
            prop: "protocolType",
            type: "select",
            dicData: [
              {
                label: "标准合同",
                value: 1,
              },
              {
                label: "个性合同",
                value: 2,
              },
            ],
          },
          {
            label: "合作结算周期",
            prop: "settlementInterval",
            type: "select",
            dicData: [
              {
                label: "随时",
                value: 1,
              },
              {
                label: "按月",
                value: 2,
              },
              {
                label: "按季",
                value: 3,
              },
              {
                label: "按年",
                value: 4,
              },
            ],
          },
          {
            label: "合同起草日期",
            prop: "protocolTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
          },
          {
            label: "合同开始日期",
            prop: "serviceStartDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
          },
          {
            label: "合同结束日期",
            prop: "serviceEndtDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
          },
          {
            label: "个人签约人",
            prop: "doctorSignName",
            type: "input",
          },
          {
            label: "个人签约时间",
            prop: "doctorSignTime",
            type: "date",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
          },
          {
            label: "平台签约人",
            prop: "platformSignName",
            type: "input",
          },
          {
            label: "平台签约时间",
            prop: "platformSignTime",
            type: "date",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
          },
          {
            label: "签约状态",
            prop: "status",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=protocols_sign_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },

            dataType: "number",
          },
        ],
      },
      form: {},
    };
  },
  computed: {},
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  methods: {
    //去火影详情
    toAuthenticationDoctorView() {
      this.$router.push({
        path: `/authenticationDoctor/detail/${this.form.doctorId}/1`,
      });
    },
    //
    toDoctorTenderView() {
      this.$router.push({
        path: `/tenderInfoDoctor/detail/${this.form.doctorTenderId}`,
      });
    },
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },

    // 获取详情
    getDetail() {
      getDetail(this.id).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            this.form = data.data;
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
  },
};
</script>

<style></style>
