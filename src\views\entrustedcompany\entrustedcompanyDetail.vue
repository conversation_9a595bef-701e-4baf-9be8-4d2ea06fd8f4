<template>
  <div>
    <basic-container
      ><el-page-header @back="goBack" :content="$route.name"></el-page-header
    ></basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="企业信息" name="1">
          <!--企业信息-->
          <avue-form
            ref="form1"
            :option="option1"
            v-model="form1"
            v-if="!validatenull(form1.name)"
            @submit="submit"
          />
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>
        <el-tab-pane label="认证信息" name="2">
          <el-row>
            <el-col :span="8">
              <span class="first-title">身份证信息</span>
              <el-row style="margin-top: 20px" :gutter="20">
                <el-col :span="11">
                  <div>
                    <el-image
                      :src="form1.atta1"
                      :preview-src-list="[form1.atta1]"
                      style="width: 148px; height: 148px"
                      fit="cover"
                    >
                      <template #error>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                          <div>暂无图片</div>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="prompt">身份证正面</div>
                </el-col>
                <el-col :span="11">
                  <div>
                    <el-image
                      style="width: 148px; height: 148px"
                      :src="form1.atta2"
                      fit="cover"
                      :preview-src-list="[form1.atta2]"
                    >
                      <template #error>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                          <div>暂无图片</div>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="prompt">身份证反面</div>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="16">
              <span class="first-title">企业信息</span>
              <el-row :gutter="20" style="margin-top: 20px">
                <el-col :span="5">
                  <div>
                    <el-image
                      style="width: 148px; height: 148px"
                      :src="form1.atta3"
                      :preview-src-list="[form1.atta3]"
                      fit="cover"
                    >
                      <template #error>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                          <div class>暂无图片</div>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="prompt">营业执照</div>
                </el-col>
                <el-col :span="5">
                  <div>
                    <el-image
                      style="width: 148px; height: 148px"
                      :src="form1.atta4"
                      :preview-src-list="[form1.atta4]"
                      fit="cover"
                    >
                      <template #error>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                          <div>暂无图片</div>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="prompt">开户许可证</div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <avue-crud
            :data="data2"
            :option="optionCurd2"
            :page.sync="page"
          ></avue-crud>
        </el-tab-pane>
        <el-tab-pane label="电子签章" name="3" v-if="roleId !== '2'">
          <div v-if="form1.esignStatus == 1">
            <el-row>
              <el-col
                :span="8"
                class="card-header-right"
                style="padding-right: 100px"
                ><el-empty
                  description=" "
                  image="/img/weikaitong.png"
                  :image-size="300"
                ></el-empty
              ></el-col>
              <el-col :span="12">
                <div class="first-title" style="margin-top: 70px">
                  什么是电子签章？
                </div>
                <el-row style="margin-top: 20px; font-size: 14px">
                  <el-col :span="18">
                    <span>
                      电子签章是电子合同上的印章展示效果，使用电子签章，委托双方之间通过电子信息的形式达成的一种协议，元圈平台电子签章所签署的电子合同具有和纸质合同同等的法律效力。
                    </span>
                  </el-col>
                </el-row>
                <el-row style="margin-top: 20px"
                  ><span style="font-weight: bold">电子签章的优势</span></el-row
                >
                <el-row style="margin-top: 10px"
                  ><span style="color: #f8b71f; font-size: 14px"
                    >节约成本、提高效率、便于存档、安全可靠</span
                  ></el-row
                >
                <el-row style="margin-top: 20px">
                  <el-button
                    type="primary"
                    @click="openSignature"
                    v-if="permission.entrustedCompany_signature"
                    >立即开通账户</el-button
                  >
                  <span style="font-weight: bold" v-else
                    >请联系客服进行开通！ 客服电话：00000000</span
                  >
                </el-row>
              </el-col>
            </el-row>
          </div>
          <div style="margin-top: 20px" v-else>
            <div style="height: 50px; margin-top: 20px">
              <el-row type="flex" class="row-bg2" justify="space-around">
                <el-col :span="24">
                  电子签章账户状态：
                  <span class="first-title" v-show="form1.esignStatus == 1"
                    >未开通</span
                  >
                  <span class="first-title" v-show="form1.esignStatus == 2"
                    >已开通</span
                  >
                  <span class="first-title" v-show="form1.esignStatus == 3"
                    >已过期</span
                  >
                  <span class="first-title" v-show="form1.esignStatus == 4"
                    >已停用</span
                  >
                </el-col>
              </el-row>
            </div>
            <span class="first-title" style="margin-top: 30px"
              >电子签章展示</span
            >
            <div>
              <el-image
                style="width: 200px; height: 200px; margin: 20px 0"
                :src="
                  form1.esignUrl
                    ? 'data:image/png;base64,' + form1.esignUrl
                    : ''
                "
                :preview-src-list="[
                  form1.esignUrl
                    ? 'data:image/png;base64,' + form1.esignUrl
                    : '',
                ]"
                fit="cover"
              >
                <template #error>
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                    <div class>公章</div>
                  </div>
                </template>
              </el-image>
            </div>
            <div
              class="prompt"
              v-show="form1.esignUrl == null || form1.esignUrl == ''"
            >
              <el-button
                v-if="permission.entrustedCompany_signature"
                style="text-align: center"
                type="primary"
                @click="applyNow()"
                >创建印章</el-button
              >
            </div>
            <div>
              <el-button
                type="primary"
                v-if="permission.entrustedCompany_open"
                @click="stopEsignAccount(2)"
                >启用电子签章</el-button
              >
              <el-button
                type="danger"
                v-if="permission.entrustedCompany_open"
                @click="stopEsignAccount(4)"
                >停用电子签章</el-button
              >
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="人员管理" name="4">
          <avue-crud
            :data="data4"
            :option="optionCurd4"
            :page.sync="page4"
            v-model="form4"
            :before-open="beforeOpen"
            :permission="permissionPeopleList"
            @row-update="memberRowUpdate"
            @row-save="memberRowSave"
            @row-del="memberRowDel"
            @on-load="getEntrustedCompanyMemberList"
          >
            <!-- 启用停用 -->
            <template slot="enable" slot-scope="{ row }">
              <el-switch
                v-if="permission.entrustedCompany_personnel_edit"
                :value="row.enable == 1 ? true : false"
                active-color="#67C23A"
                inactive-color="#cacdd4"
                @change="enable(row)"
              >
              </el-switch>
              <span v-else>{{ row.enable == 1 ? "启用" : "停用" }}</span>
            </template>
          </avue-crud>
        </el-tab-pane>

        <el-tab-pane
          label="企业会员协议"
          name="6"
          v-if="form1.memberAgreementUrl"
        >
          <el-button
            type="primary"
            icon="el-icon-refresh"
            v-if="permission.entrustedCompany_agreement_refresh"
            @click="toGenerateProtocol()"
            >重新生成会员协议</el-button
          >
          <!-- <template>
            <div>
              <pdf ref="pdf" :src="form1.memberAgreementUrl" v-for="i in numPages" :key="i" :page="i"></pdf>
            </div>
          </template> -->
          <div v-if="form1.memberAgreementUrl" style="margin-top: 15px">
            <iframe
              :src="form1.memberAgreementUrl"
              width="100%"
              :height="iframeHeight"
              title="企业会员协议"
              frameBorder="no"
              border="0"
              marginWidth="0"
              marginHeight="0"
              scrolling="no"
              allowTransparency="yes"
            ></iframe>
          </div>
        </el-tab-pane>
        <el-tab-pane label="交易管家记账子单元" name="7">
          <avue-crud
            :option="subelementOption"
            :table-loading="loading"
            :data="subelementData"
            :permission="permissionList"
            :before-open="beforeOpenTradingManager"
            v-model="subelementForm"
            ref="subelementCrud"
            @row-update="rowUpdate"
            @row-save="rowSave"
            @selection-change="selectionChange"
            @current-change="currentChange"
            @size-change="sizeChange"
            @refresh-change="refreshChange"
            @on-load="onLoad"
          >
            <template slot="menuLeft"> </template>
            <template slot-scope="{ type, size, row, index }" slot="menu">
              <el-button
                type="text"
                v-if="permission.tradingManager_refresh"
                @click="tradingManagerList(row)"
                >刷新状态
              </el-button>
              <el-button
                type="text"
                v-if="permission.tradingManager_delete"
                @click="handleDelete(row)"
                >关闭并删除账户
              </el-button>
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>

      <el-dialog
        append-to-body
        title="会员协议"
        width="60%"
        :visible.sync="dialogVisible"
      >
        <template>
          <div style="height: 600px; overflow: scroll">
            <pdf
              ref="pdf"
              :src="memberAgreementUrl"
              v-for="i in numPages"
              :key="i"
              :page="i"
            ></pdf>
          </div>
        </template>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import pdf from "vue-pdf";
import {
  getDetail,
  regEsignAccount,
  regEsignSeal,
  getEntrustedRelList,
  stopEsignAccount,
  generateProtocol,
} from "@/api/entrustedcompany/entrustedCompany";
import { getList } from "@/api/customercertificationrecord/customerCertificationRecord";
import {
  getEntrustedCompanyMemberList,
  enable,
  addMember,
  editMember,
  delMember,
} from "@/api/entrustedcompanymember/entrustedCompanyMember";
//交易管家记账子单元
import {
  getSubelementList,
  getSubelementDetail,
  add,
  update,
  close,
  tradingManagerList,
  getByCustomerId,
} from "@/api/tradingManager/tradingManager";
import subelementOption from "@/const/tradingManager/tradingManager";
import { mapGetters } from "vuex";
export default {
  components: {
    pdf,
  },
  data() {
    // 手机号验证
    const validateLinkTel = (rule, value, callback) => {
      let reg = /^(1\d{10})$/;
      if (value === "") {
        callback(new Error("请输入手机号"));
      } else {
        if (!reg.test(value)) {
          callback(new Error("请输入合法的手机号"));
        } else {
          callback();
        }
      }
    };
    return {
      //交易管家记账子单元
      openStatus: true,
      subelementOption: subelementOption,
      subelementForm: {},
      subelementQuery: {},
      subelementPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      subelementData: [],
      // 交易管家记账子单元 end
      iframeHeight: window.innerHeight - 180,
      dialogVisible: false,
      memberAgreementUrl: "",
      numPages: 1,
      id: "",
      activeName: "1",
      query: {},
      loading: false,
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      roleId: null,
      EntrustedRelList: [], //合作关系租户
      option2: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
        selection: true,
        dialogClickModal: false,
        addBtn: false,
        column: [
          {
            label: "业务平台租户名称",
            prop: "thirdTenantName",
            type: "input",
          },
          {
            label: "平台名称",
            prop: "appName",
            type: "input",
          },
          {
            label: "合作状态",
            prop: "auditStatus",
            type: "select",
            dicUrl:
              "/api/blade-system/dict-biz/dictionary?code=cooperate_audit_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
          },
        ],
      },
      form1: {
        atta1:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta2:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta3:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta4:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta5:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta6:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta7:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
      },
      option1: {
        disabled: true,
        labelWidth: "170",
        submitBtn: false,
        emptyBtn: false,
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",

            column: [
              {
                label: "全称",
                prop: "name",
              },
              {
                label: "简称",
                prop: "shortName",
              },
              {
                label: "企业类型",
                prop: "regType",
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=reg_type",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
              },
              {
                label: "统一社会信用代码",
                prop: "socialCreditCode",
              },
              {
                label: "法定代表人",
                prop: "lawPerson",
              },
              {
                label: "法人身份证号",
                prop: "lawPersonIdNum",
                maxlength: 18,
              },
              {
                label: "成立时间",
                prop: "regDate",
                type: "date",
                format: "yyyy年MM月dd日",
                valueFormat: "yyyy-MM-dd",
              },
              {
                label: "注册资本/万元",
                prop: "regCapital",
              },

              {
                label: "企业邮箱",
                prop: "mail",
              },

              {
                label: "注册地址",
                prop: "regAddress",
              },
              {
                label: "企业电话",
                prop: "telphone",
                maxlength: 20,
              },
            ],
          },
          {
            label: "业务信息",
            prop: "detailInfo",
            column: [
              {
                label: "经营状态",
                prop: "operatStatus",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=operat_status",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
              },
              {
                label: "行业性质",
                prop: "industryNature",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=industry_nature",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "string",
              },
              {
                label: "纳税规模",
                prop: "taxScale",
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=tax_scale",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
              },
              {
                label: "经营范围",
                prop: "bizScope",
                type: "textarea",
              },
            ],
          },
          {
            label: "开票信息",
            prop: "dutyInfo",
            column: [
              {
                label: "开户行",
                prop: "bankName",
              },
              {
                label: "银行账号",
                prop: "bankAccount",
              },
            ],
          },
          {
            label: "联系信息",
            prop: "dutyInfoss",
            column: [
              {
                label: "省份",
                prop: "province",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["city"],
                dicUrl: "/api/blade-system/region/select",
                span: 6,
              },
              {
                label: "城市",
                prop: "city",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["district"],
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 6,
              },
              {
                label: "地区",
                prop: "district",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 6,
              },
              {
                label: "详细地址",
                prop: "address",
                type: "textarea",
              },
            ],
          },
          {
            label: "联系人",
            prop: "comInfo",
            column: [
              {
                label: "联系人/授权代表",
                prop: "linkMan",
              },
              {
                label: "联系电话",
                prop: "linkTel",
              },
            ],
          },
        ],
      },
      atta1: "",
      atta2: "",
      atta4: "",
      atta5: "",
      data2: [],
      optionCurd2: {
        index: true,
        title: "认证记录信息",
        titleSize: "h4",
        stripe: true,
        addBtn: false,
        refreshBtn: false,
        columnBtn: false,
        editBtn: false,
        menu: false,
        border: true,
        column: [
          {
            label: "提交时间",
            prop: "submitTime",
          },
          {
            label: "提交人",
            prop: "customerName",
          },
          {
            label: "审核状态",
            prop: "auditStatus",
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=audit_status",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
          },
          {
            label: "审核内容",
            prop: "auditContext",
          },
        ],
      },
      form4: {},
      data4: [],
      page4: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      optionCurd4: {
        index: true,
        stripe: true,
        refreshBtn: false,
        columnBtn: false,
        border: true,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        addBtnIcon: " ",
        cancelBtnIcon: " ",
        saveBtnIcon: " ",
        updateBtnIcon: " ",
        column: [
          {
            label: "名称",
            prop: "name",
            rules: [
              {
                required: true,
                message: "请输入名称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "登录手机号",
            prop: "phone",
            disabled: false,
            rules: [
              {
                required: true,
                message: "请输入手机号",
                trigger: "blur",
              },
              {
                validator: validateLinkTel,
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "职能角色",
            prop: "roleId",
            type: "select",
            dicUrl: "/api/blade-system/role/getEntRoleList",
            props: {
              label: "roleName",
              value: "id",
            },
            rules: [
              {
                required: true,
                message: "请选择职能角色类型",
                trigger: "blur",
              },
            ],
          },
          {
            label: "部门名称",
            prop: "deptName",
          },
          {
            label: "创建时间",
            prop: "createTime",
            display: false,
          },
          {
            label: "启用/停用",
            prop: "enable",
            display: false,
            align: "center",
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    (this.roleId = localStorage.getItem("roleId")),
      (this.activeName = this.$route.params.tabindex);

    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionPeopleList() {
      return {
        addBtn: this.vaildData(
          this.permission.entrustedCompany_personnel_add,
          false
        ),
        delBtn: this.vaildData(
          this.permission.entrustedCompany_personnel_del,
          false
        ),
        editBtn: this.vaildData(
          this.permission.entrustedCompany_personnel_edit,
          false
        ),
      };
    },
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.tradingManager_add, false),
        viewBtn: this.vaildData(this.permission.tradingManager_view, false),
        delBtn: this.vaildData(this.permission.tradingManager_delete, false),
        editBtn: this.vaildData(this.permission.tradingManager_edit, false),
      };
    },
  },
  methods: {
    //交易管家记账子单元
    //新增记账子单元
    rowSave(row, done, loading) {
      row.customerId = this.id;
      add(row).then(
        () => {
          this.onLoad(this.subelementPage);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    //修改记账子单元
    rowUpdate(row, index, done, loading) {
      update(row.id, row.dmanam).then(
        () => {
          this.onLoad(this.subelementPage);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    //刷新记账子单元
    tradingManagerList(row) {
      tradingManagerList(row.dmanbr).then(({ data: res }) => {
        if (res.success) {
          this.$message({
            type: "success",
            message: "刷新成功!",
          });
          this.onLoad(this.subelementPage);
        }
      });
    },
    // 关闭
    handleDelete(row) {
      this.$confirm("确定将选择数据关闭?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return close(row.id);
        })
        .then(() => {
          this.onLoad(this.subelementPage);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    onLoad(page, params = {}) {
      params.customerId = this.id;
      this.loading = true;
      getSubelementList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then(
        (res) => {
          const data = res.data.data;
          this.subelementPage.total = data.total;
          this.subelementData = data;
          this.loading = false;
          this.selectionClear();
        },
        () => {
          this.loading = false;
        }
      );
    },

    currentChange(currentPage) {
      this.subelementPage.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.subelementPage.pageSize = pageSize;
    },

    selectionClear() {
      this.selectionList = [];
      this.$refs.subelementCrud.toggleSelection();
    },
    //选择项改变
    selectionChange(list) {
      this.selectionList = list;
    },
    //刷新
    refreshChange() {
      this.onLoad(this.subelementPage, this.query);
    },
    //打开子单元前
    beforeOpenTradingManager(done, type) {
      if (type == "add") {
        getByCustomerId(this.id).then(({ data: res }) => {
          if (res.data > 0) {
            this.$message({
              type: "error",
              message: "记账子单元已存在!",
            });
          } else {
            this.subelementForm.dmanam = this.form1.name;
            done();
          }
        });
      }
      if (type == "edit") {
        this.subelementOption.column.forEach((item) => {
          if (item.label != "记账子单元名称") item.disabled = true;
        });
      } else {
        this.subelementOption.column.forEach((item) => {
          item.disabled = false;
        });
      }
      if (["edit", "view"].includes(type)) {
        getSubelementDetail(this.subelementForm.id).then((res) => {
          this.subelementForm = res.data.data;
        });
      }
      if (type != "add") {
        done();
      }
    },
    //交易管家记账子单元end
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    handleClick(tab) {
      this.page.pageSize = 10;
      this.page.currentPage = 1;
      this.page.total = 0;
      //认证信息
      if (tab.name == "2") {
        this.getCustomercertificationRecord();
      }
      //人员管理
      if (tab.name == "4") {
        this.page4.pageSize = 10;
        this.page4.currentPage = 1;
        this.page4.total = 0;
        this.getEntrustedCompanyMemberList();
      }
      if (tab.name == "5") {
        this.getEntrustedRelList();
      } else if (tab.name == "6") {
        this.getNumPages2();
      }
    },
    getNumPages2() {
      let loadingTask = pdf.createLoadingTask(this.form1.memberAgreementUrl);
      loadingTask.promise
        .then((pdf) => {
          this.numPages = pdf.numPages;
        })
        .catch((err) => {
          console.error("pdf 加载失败", err);
        });
    },

    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form1 = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    // 获取企业合作关系数据
    getEntrustedRelList() {
      getEntrustedRelList(this.id).then((res) => {
        if (res.data.success) {
          this.EntrustedRelList = res.data.data;
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },

    getNumPages(url) {
      this.memberAgreementUrl = url;
      let loadingTask = pdf.createLoadingTask(this.memberAgreementUrl);
      loadingTask.promise
        .then((pdf) => {
          this.numPages = pdf.numPages;
          this.dialogVisible = true;
        })
        .catch((err) => {
          console.error("pdf 加载失败", err);
        });
    },
    submit() {},
    //开通电子签章
    openSignature() {
      // console.log(this.id);
      regEsignAccount(this.id).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getDetail();
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    //申请电子签章
    applyNow() {
      let hText = " ";
      regEsignSeal(this.id, hText).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getDetail();
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    // 停用或启用印章
    stopEsignAccount(status) {
      stopEsignAccount(this.id, status).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getDetail();
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    // 重新生成会员协议
    toGenerateProtocol() {
      generateProtocol(this.id).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getDetail();
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    //认证记录
    getCustomercertificationRecord() {
      let _this = this;
      _this.loading = true;
      getList(
        _this.page.currentPage,
        _this.page.pageSize,
        Object.assign(
          {
            customerType: 2,
            customerId: _this.id,
          },
          _this.query
        )
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data2 = data.records;
        this.loading = false;
      });
    },
    //人员管理
    getEntrustedCompanyMemberList() {
      let _this = this;
      _this.loading = true;
      getEntrustedCompanyMemberList(
        _this.page4.currentPage,
        _this.page4.pageSize,
        Object.assign(
          {
            entrustedCompanyId: _this.id,
          },
          _this.query
        )
      ).then((res) => {
        const data = res.data.data;
        this.page4.total = data.total;
        this.data4 = data.records;
        this.loading = false;
      });
    },
    // 人员新增
    memberRowSave(row, done, loading) {
      row.isAdmin = 0;
      row.entrustedCompanyId = this.id;
      row.roleName = this.form4.$roleId;
      addMember(row).then(
        () => {
          this.getEntrustedCompanyMemberList();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    beforeOpen(done, type) {
      if ("edit" == type) {
        this.optionCurd4.column[0].disabled = true;
        this.optionCurd4.column[1].disabled = true;
      } else {
        this.optionCurd4.column[0].disabled = false;
        this.optionCurd4.column[1].disabled = false;
      }
      done();
    },
    // 人员编辑
    memberRowUpdate(row, index, done, loading) {
      editMember(row).then(
        () => {
          this.getEntrustedCompanyMemberList();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    // 人员删除
    memberRowDel(row) {
      this.$confirm("确定删除该用户将不能在登录平台!", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return delMember(row.id);
        })
        .then(() => {
          this.getEntrustedCompanyMemberList();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    // 启用或停用
    enable(row) {
      enable(row.id).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getEntrustedCompanyMemberList();
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
  },
};
</script>

<style>
.avue-crud__menu {
  min-height: 0 !important;
}

h4 {
  margin: 5px 0 !important;
}

.first-title {
  font-size: 16px !important;
  font-weight: 600;
  color: #333;
}

.prompt {
  width: 148px;
  margin: 5px 0;
  font-size: 14px;
  text-align: center;
}

.image-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fbfdff;
  border: 1px dashed #c0ccda;
  color: #666;
  border-radius: 6px;
  box-sizing: border-box;
  width: 148px;
  height: 148px;
  cursor: pointer;
  line-height: 146px;
  vertical-align: top;
}

.image-slot div {
  font-size: 14px;
  margin: 4px 0;
}
</style>
