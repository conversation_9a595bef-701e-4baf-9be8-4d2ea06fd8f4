<template>
  <div>
    <basic-container class="el-card__body">
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(baseForm)"
        ref="baseForm"
        :option="baseOption"
        v-model="baseForm"
        @submit="submit"
      >
        <template slot-scope="{ disabled, size }" slot="protocolFile">
          <div style="cursor: pointer">
            <el-tag @click="preview(baseForm.protocolFile)"
              >预览《服务委托合同》</el-tag
            >
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>

      <!--接单订单信息-->
      <avue-crud
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :permission="permissionList"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
      >
        <template slot-scope="{ type, size, row, index }" slot="code">
          <div
            class="to-view"
            @click="toOrderdoctorDetail(row.orderDoctorId)"
            v-if="row.code"
          >
            <a readonly>
              {{ row.code }}
            </a>
          </div>
          <div v-else>无</div>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {
  getDetail as getBaseDetail,
  getAssociationList,
} from "@/api/orderentrusted/orderEntrusted";

import { mapGetters } from "vuex";

export default {
  data() {
    return {
      activeName: "1",
      id: "",

      //基础信息
      baseOption: {
        disabled: true,
        labelWidth: "150",
        submitBtn: false,
        emptyBtn: false,
        group: [
          {
            label: "基本信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "任务编号",
                prop: "code",
                type: "input",
                span: "24",
              },

              {
                label: "客户名称",
                prop: "entrustedCompanyName",
                type: "input",
              },
              {
                label: "平台名称",
                prop: "taskPlateName",
                type: "input",
              },
              {
                label: "任务开始时间",
                prop: "orderStartDate",
                type: "datetime",
                format: "yyyy-MM-dd  HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },

              {
                label: "任务结束时间",
                prop: "orderEndDate",
                type: "datetime",
                format: "yyyy-MM-dd  HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "任务数量",
                prop: "planNum",
                type: "input",
              },
              {
                label: "任务计划金额",
                prop: "planAmount",
                type: "input",
              },
              {
                label: "任务状态",
                prop: "orderStatus",
                type: "input",
              },
              {
                label: "服务委托合同",
                prop: "protocolFile",
                type: "input",
              },
            ],
          },
        ],
      },
      baseForm: {},
      //平台净收入记账单
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: "auto",
        calcHeight: 65,
        title: "接单订单明细",
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        delBtn: false,
        addBtn: false,
        selection: true,
        dialogClickModal: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        addBtnIcon: " ",
        cancelBtnIcon: " ",
        saveBtnIcon: " ",
        updateBtnIcon: " ",
        menu: false,
        column: [
          {
            label: "订单编号",
            prop: "code",
            type: "input",
          },
          {
            label: "订单开始时间",
            prop: "invitationStartDate",
            type: "date",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd",
          },

          {
            label: "订单结束时间",
            prop: "invitationEndDate",
            type: "date",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "订单发布时间",
            prop: "realityStartDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "订单接单时间",
            prop: "receivingOrderData",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "接单人员姓名",
            prop: "doctorName",
            type: "input",
          },

          {
            label: "接单人员单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "接单人员部门",
            prop: "departmentName",
            type: "input",
          },

          {
            label: "订单计划数量",
            prop: "planNum",
            type: "input",
          },
          {
            label: "订单完成数量",
            prop: "finishNum",
            type: "input",
          },
        ],
      },
      data: [],
    };
  },
  computed: {
    //平台净收入记账单
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.dmanInternalTransfer_add, false),
        viewBtn: this.vaildData(
          this.permission.dmanInternalTransfer_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.dmanInternalTransfer_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.dmanInternalTransfer_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionDoctorList.forEach((ele) => {
        ids.push(ele.doctorId);
      });
      return ids.join(",");
    },
  },
  created() {
    this.id = this.$route.params.id;

    this.init();
  },
  methods: {
    toOrderdoctorDetail(id) {
      this.$router.push({
        path: `/orderdoctor/detail/${id}`,
      });
    },
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    init() {
      this.getBaseDetail();
    },
    getBaseDetail() {
      this.receivingOrdersLoading = true;
      getBaseDetail(this.id).then((res) => {
        let data = res.data;
        data.data.orderStatus =
          data.data.orderStatus == 1
            ? "已接单"
            : data.data.orderStatus == 2
            ? "待验收"
            : data.data.orderStatus == 3
            ? "待结算"
            : data.data.orderStatus == 4
            ? "已完成"
            : "已作废";
        this.baseForm = data.data;
      });
    },
    submit() {},

    beforeOpen(done) {
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.orderEntrustedId = this.id;
      getAssociationList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        console.log(data);
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        if (!this.$refs.crud.gridShow) {
          // myTable是表格的ref属性值
          this.$refs.crud.doLayout();
        }
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}
.sub-button {
  display: flex;
  justify-content: center;
}
.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
