export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  labelWidth: 150,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  delBtn: false,
  editBtn: false,
  selection: true,
  dialogClickModal: false,
  addBtn: false,
  column: [
    {
      label: "编号",
      prop: "code",
      type: "input",
      editDisabled: true,
      width: 130,
      addDisplay: false, //新增时是否显示
    },
    {
      label: "企业名称",
      prop: "name",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入名称",
          trigger: "blur",
        },
      ],
    },
    {
      label: "认证状态",
      prop: "auditStatus",
      type: "select",
      dicUrl:
        "/api/blade-system/dict-biz/dictionary?code=customer_audit_status",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      search: true,
      width: 80,
      rules: [
        {
          required: true,
          message: "认证状态",
          trigger: "blur",
        },
      ],
    },
    {
      label: "电子签章",
      prop: "esignStatus",
      span: 12,
      type: "radio",
      slot: true,
      value: 0,
      width: 80,
      dicData: [
        {
          label: "未开通",
          value: 1,
        },
        {
          label: "已开通",
          value: 2,
        },
        {
          label: "已过期",
          value: 3,
        },
        {
          label: "已停用",
          value: 4,
        },
      ],
    },
    {
      label: "法定代表人",
      prop: "lawPerson",
      type: "input",
      width: 100,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "统一社会信用代码",
      prop: "socialCreditCode",
      search: true,
      searchLabelWidth: 135,
      width: 165,
      rules: [
        {
          required: true,
          message: "请输入统一信用代码",
          trigger: "blur",
        },
      ],
    },
    {
      label: "开户银行",
      prop: "bankName",
      rules: [
        {
          required: true,
          message: "请输入开户银行",
          trigger: "blur",
        },
      ],
    },
    {
      label: "银行账号",
      prop: "bankAccount",
      rules: [
        {
          required: true,
          message: "请输入银行账号",
          trigger: "blur",
        },
      ],
    },
    {
      label: "注册时间",
      prop: "createTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: 100,
    },
    {
      label: "审核时间",
      prop: "auditTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: 100,
    },
    {
      label: "启用/禁用",
      prop: "enable",
      align: "center",
      width: 90,
    },
  ],
};
