import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-rule/rule/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-rule/rule/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-rule/rule/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const addOrUpdate = (row) => {
  return request({
    url: '/api/blade-rule/rule/submit',
    method: 'post',
    data: row
  })
}


