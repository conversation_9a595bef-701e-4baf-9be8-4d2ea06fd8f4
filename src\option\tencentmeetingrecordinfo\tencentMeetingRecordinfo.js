export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "康缘会议id",
      prop: "meetingId",
      type: "input",
    },
    {
      label: "腾讯会议id",
      prop: "tencentMeetingId",
      type: "input",
    },
    {
      label: "会议code",
      prop: "meetingCode",
      type: "input",
    },
    {
      label: "会议录制id",
      prop: "recordFileId",
      type: "input",
    },
    {
      label: "播放地址",
      prop: "viewAddress",
      type: "input",
    },
    {
      label: "录制文件下载地址",
      prop: "downloadAddress",
      type: "input",
    },
    {
      label: "下载视频文件格式，例如：mp4",
      prop: "downloadAddressFileType",
      type: "input",
    },
    {
      label: "音频下载地址",
      prop: "audioAddress",
      type: "input",
    },
    {
      label: "下载音频文件格式，例如：m4a",
      prop: "audioAddressFileType",
      type: "input",
    },
    {
      label: "会议纪要PDF",
      prop: "meetingSummary",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
