<template>
  <div>
    <basic-container>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div>
          <el-page-header
            @back="goBack"
            :content="$route.name"
          ></el-page-header>
        </div>
      </div>
    </basic-container>
    <basic-container
      ><avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
      ></avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated /> </template
    ></basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/summarytaxcert/summaryTaxCert";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      id: "", //数据id
      form: {},
      query: {},
      loading: false,
      //基础信息
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "150",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "详情信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "编号",
                prop: "code",
                type: "input",
              },
              {
                label: "名称",
                prop: "name",
                search: true,
                type: "input",
              },
              {
                label: "金额合计大写",
                prop: "totalAmountUppercase",
                type: "input",
              },
              {
                label: "金额合计小写",
                prop: "totalAmountLowercase",
                type: "input",
              },
              {
                label: "状态",
                prop: "taxCertStatus",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=tax_cert_status",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                disabled: true,
                dataType: "number",
                slot: true,
                width: 140,
              },
              {
                label: "完税证明附件",
                prop: "taxCertFile",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
            ],
          },
        ],
      },
      data: [],
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail(); //获取详情
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    // 获取数据详情
    getDetail() {
      getDetail(this.id).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            this.form = data.data;
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
  },
};
</script>

<style></style>
