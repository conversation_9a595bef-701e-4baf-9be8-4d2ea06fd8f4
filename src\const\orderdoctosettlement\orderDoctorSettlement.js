export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  selection: true,
  viewBtnIcon: " ",
  dialogClickModal: false,
  column: [
    {
      label: "结算单编号",
      prop: "code",
      type: "input",
      search: true,
      searchLabelWidth: "120",
      width: "130"
    },
    {
      label: "会员订单编号",
      prop: "orderDoctorCode",
      search: true,
      searchLabelWidth: "120",
      type: "input",
    },
    {
      label: "会员订单id",
      prop: "orderDoctorId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "服务收入性质",
      prop: "natureOfIncome",
      type: "select",
      search: true,
      searchLabelWidth: "120",
      dicData: [
        {
          label: "经营所得",
          value: 1,
        },
        {
          label: "劳务报酬",
          value: 2,
        },
      ],
    },
    {
      label: "服务活动类型",
      prop: "activityPlanType",
      type: "select",
      search: true,
      searchLabelWidth: "120",
      dicData: [
        {
          label: "学术会议",
          value: 1,
        },
        {
          label: "知识创作",
          value: 2,
        },
        {
          label: "知识传播",
          value: 3,
        },
        {
          label: "临床调研",
          value: 4,
        },
        {
          label: "用药反馈",
          value: 5,
        },
        {
          label: "专业评审",
          value: 6,
        },
        {
          label: "病例征集",
          value: 8,
        },
      ],
    },
    {
      label: "服务活动id",
      prop: "activityId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "注册医师Id",
      prop: "doctorId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "会员姓名",
      prop: "doctorName",
      type: "input",
      search: true,
      width: "90",
    },
    {
      label: "会员手机号",
      prop: "doctorPhone",
      type: "input",
      search: true,
      searchLabelWidth: "120",
    },
    {
      label: "身份证号",
      prop: "doctorIdCard",
      type: "input",
      width: "90",
    },
    {
      label: "结算金额",
      prop: "paymentAmount",
      type: "input",
      width: "80",
    },
    {
      label: "是否申请打款",
      prop: "isApplyPayment",
      type: "select",
      search: true,
      searchLabelWidth: "150",
      dicData: [
        {
          label: "未申请",
          value: 0,
        },
        {
          label: "已申请待经办",
          value: 1,
        },
        {
          label: "已支付",
          value: 2,
        },
        {
          label: "支付失败",
          value: 3,
        },
        {
          label: "已作废",
          value: 4,
        },
      ],
    },
    {
      label: "是否纳税申报",
      prop: "isTaxDeclaration",
      type: "select",
      search: true,
      searchLabelWidth: "130",
      dicData: [
        {
          label: "未申报",
          value: 0,
        },
        {
          label: "已申报",
          value: 1,
        },
      ],
    },
    {
      label: "支付时间",
      prop: "paymentTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: "100",
    },
    {
      label: "纳税申报单编号",
      prop: "taxReturnCode",
      type: "input",
      width: "125",
    },
  ],
};
