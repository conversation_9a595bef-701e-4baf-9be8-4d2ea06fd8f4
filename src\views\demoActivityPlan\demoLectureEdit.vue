<template>
  <div>
    <basic-container>
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <!--病例征集活动计划-->
          <avue-form
            ref="form"
            :option="option"
            v-model="form"
            @submit="submit"
            @reset-change="handleReset"
          >
            <!-- 涉及产品名称 -->
            <template slot-scope="{ disabled, size }" slot="baseProductId">
              <el-select
                v-model="form.baseProductId"
                @change="productNameChange"
                @focus="productNameFocus"
                filterable
                remote
                reserve-keyword
                placeholder="请选择产品名"
                :remote-method="remoteMethodProduct"
                :loading="productSelectLoading"
              >
                <el-option
                  v-for="item in productOptions"
                  :key="item.value"
                  :label="item.productName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </template>
            <!-- 业务人员  -->
            <template slot-scope="{ disabled, size }" slot="baseEmployeeId">
              <el-select
                v-model="form.baseEmployeeId"
                @change="baseEmployeeChange"
                @focus="baseEmployeeFocus"
                filterable
                remote
                reserve-keyword
                placeholder="请选择业务人员"
                :remote-method="remoteMethodBaseEmployee"
                :loading="selectLoading"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </template>
          </avue-form>
        </el-tab-pane>
        <el-tab-pane label="邀请会员" name="2">
          <avue-crud
            :option="optionDoctor"
            :table-loading="loading"
            :data="data"
            :page.sync="page"
            :permission="permissionList"
            :before-open="beforeOpen"
            v-model="formDoctor"
            ref="crudDoctor"
            @row-update="rowUpdate"
            @row-del="rowDel"
            @search-change="searchChange"
            @search-reset="searchReset"
            @selection-change="selectionChange"
            @current-change="currentChange"
            @size-change="sizeChange"
            @refresh-change="refreshChange"
          >
            <template slot-scope="scope" slot="menuLeft">
              <el-button
                v-if="permission.inviteDoctor_add && form.planStatus == 0"
                type="primary"
                @click="$refs.crudDoctor.rowAdd()"
                >新增</el-button
              >
            </template>
            <template slot="planSearchNumHeader" slot-scope="{ column }">
              <span style="color: #f56c6c; margin-right: 4px">*</span
              >计划收集份数
            </template>
            <template slot-scope="{ row }" slot="menu">
              <el-button
                v-if="permission.customerCertificationRecord_view"
                type="text"
                @click="toView(row)"
                >查看会员信息</el-button
              >
              <el-button
                v-if="permission.inviteDoctor_edit && form.planStatus == 0"
                type="text"
                @click="rowCell(row, index)"
                >{{ row.$cellEdit ? "保存" : "修改数量" }}</el-button
              >
              <el-button
                v-if="row.$cellEdit"
                type="text"
                @click="rowCancel(row, index)"
                >取消</el-button
              >
              <el-button
                type="text"
                v-if="permission.inviteDoctor_delete && form.planStatus == 0"
                @click="$refs.crudDoctor.rowDel(row, index)"
                >删除</el-button
              >
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
      <el-dialog
        title="请选择会员"
        append-to-body
        @close="closeDialog"
        :visible.sync="showDialog"
        width="75%"
      >
        <avue-crud
          :row-style="rowStyle"
          :table-loading="dialogLoading"
          :data="dialogData"
          :option="dialogOption"
          :page.sync="dialogPage"
          ref="dialogCrud"
          @search-change="dialogSearchChange"
          @search-reset="dialogSearchReset"
          @current-change="dialogCurrentChange"
          @size-change="dialogSizeChange"
          @refresh-change="dialogRefreshChange"
          @selection-change="dialogSelectionChange"
        >
        </avue-crud>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showDialog = false">取 消</el-button>
          <el-button type="primary" @click="saveList()">确 定</el-button>
        </span>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import {
  getByEntrustedCompanyId,
  getPlanDoctor,
  listByProductId,
  saveOrUpdate,
} from "@/api/demoActivityPlan/demoActivityPlan";
import {
  deleteDoctor,
  getDetail,
  getDoctorList,
  getExistDoctorIds,
  saveList,
  savePlanSearchNum,
} from "@/api/demoLecture/demoLecture";
import {getProductList} from "@/api/demoActivityserviceproject/demoActivityServiceProject";
import {mapGetters} from "vuex";
import {validatenum} from "@/util/validate.js";
import dayjs from "dayjs";

export default {
  data() {
    return {
      //涉及产品名称
      productSelectOption: {}, //选中的对象
      productOptions: [],
      productSelectLoading: false,
      //发起代表
      selectOption: {}, //选中的对象
      options: [],
      selectLoading: false,
      activeName: "1",
      id: "",
      //基础信息
      option: {
        submitIcon: " ",
        emptyIcon: " ",
        disabled: false,
        labelWidth: "150",
        column: [
          {
            label: "活动类型",
            prop: "type",
            type: "select",
            dicData: [{ label: '大讲堂', value: 1, }, { label: '专业录播', value: 2, }, { label: '微学堂', value: 3, }, { label: '杏林集萃', value: 4,}],
            disabled: true,
            rules: [
              {
                required: true,
                message: "请选择活动类型",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "活动编码",
            prop: "code",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入活动编码",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "活动名称",
            prop: "name",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入活动名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "计划开始时间",
            prop: "invitationStartDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择计划开始时间",
                trigger: ["blur", "change"],
              },
            ],
            change: ({ value }) => {
              if (
                dayjs(value).valueOf() >=
                dayjs(this.form.invitationEndDate).valueOf()
              ) {
                this.form.invitationEndDate = "";
              }
            },
          },
          {
            label: "计划结束时间",
            prop: "invitationEndDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择计划结束时间",
                trigger: ["blur", "change"],
              },
            ],
            pickerOptions: {
              disabledDate: (time) => {
                let start = dayjs(this.form.invitationStartDate).valueOf();
                return time.getTime() < start;
              },
            },
          },
          {
            label: "临床项目类型",
            prop: "projectTypeName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入临床项目类型",
                trigger: ["blur", "change"],
              },
            ],
            disabled: true,
          },
          {
            label: "产品名",
            prop: "baseProductId",
            type: "select",
            props: {
              label: "key",
              value: "value",
            },
            dicData: [],
            rules: [
              {
                required: true,
                message: "请选择产品名",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "企业名称",
            prop: "entrustedCompanyId",
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            change: ({ value }) => {
              if (value) {
                this.entrustedCompanyId = value;
                this.initOptions();
              }
            },
            dicUrl: "/api/blade-csc/entrustedCompany/getList",
            rules: [
              {
                required: true,
                message: "请选择企业名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "业务人员",
            prop: "baseEmployeeId",
            type: "select",
            labelTip: "请先选择企业名称",
            rules: [
              {
                required: true,
                message: "请选择业务人员",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "业务部门",
            prop: "baseDepartmentName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请选择代表部门",
                trigger: ["blur", "change"],
              },
            ],
            disabled: true,
          },
        ],
      },
      form: {
        baseProductId: "",
        projectTypeName: "专业评审",
        planSearchNum: "0",
      },
      //医患服务方案列表
      serviceProjectList: [],

      //邀请会员
      formDoctor: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      loading: false,
      optionDoctor: {
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        addBtnIcon: " ",
        cancelBtnIcon: " ",
        saveBtnIcon: " ",
        updateBtnIcon: " ",
        addBtn: false,
        searchShowBtn: false,
        columnBtn: false,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        cellBtn: false,
        cancelBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "会员姓名",
            prop: "doctorName",
            type: "input",
            disabled: true,
          },
          {
            label: "工作单位",
            prop: "hospitalName",
            type: "input",
            disabled: true,
          },
          {
            label: "所在部门",
            prop: "departmentName",
            type: "input",
            disabled: true,
          },
          {
            label: "计划任务数量",
            prop: "planSearchNum",
            type: "input",
            cell: true,
          },
          // {
          //   label: "完成收集份数",
          //   prop: "finishSearchNum",
          //   type: "input",
          // },
        ],
      },
      data: [],
      selectionList: [],
      //弹窗
      dialogLoading: false,
      dialogSelectionList: [],
      doctorIds: [],
      showDialog: false,
      dialogData: [],
      dialogPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      dialogQuery: {},
      dialogOption: {
        selectable: (row) => {
          return !this.doctorIds.includes(row.doctorId);
        },
        height: "37vh",
        rowKey: "doctorId",
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        reserveSelection: true,
        border: true,
        index: true,
        menu: false,
        viewBtn: false,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "会员名称",
            prop: "doctorName",
            type: "input",
            search: true,
          },
          {
            label: "医院名称/工作单位",
            prop: "hospitalName",
            type: "input",
            search: true,
            searchLabelWidth: 150,
          },
          {
            label: "所在科室",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职称",
            prop: "professional",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "手机号码",
            prop: "phone",
            type: "input",
          },
        ],
      },
      //弹窗End
      //企业id
      entrustedCompanyId: "",
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.inviteDoctor_add, false),
        viewBtn: this.vaildData(this.permission.inviteDoctor_view, false),
        delBtn: this.vaildData(this.permission.inviteDoctor_delete, false),
        editBtn: this.vaildData(this.permission.inviteDoctor_edit, false),
      };
    },
  },
  async created() {
    this.id = this.$route.params.id;
    await this.initProductOptions();
    this.getDetail();
  },
  methods: {
    //获取业务人员
    initOptions() {
      if (!this.entrustedCompanyId) {
        this.$message({
          type: "error",
          message: "请先选择企业名称!",
        });
        return;
      }
      this.selectLoading = true;
      let query = {
        entrustedCompanyId: this.entrustedCompanyId,
      };
      getByEntrustedCompanyId(query).then((res) => {
        const data = res.data;
        this.options = data.data;
        this.selectLoading = false;
        this.entrustedCompanyChange();
      });
    },
    //业务人员搜索
    remoteMethodBaseEmployee(name) {
      if (name !== "") {
        if (!this.entrustedCompanyId) {
          this.$message({
            type: "error",
            message: "请先选择企业名称!",
          });
          return;
        }
        this.productSelectLoading = true;
        let query = {
          name: name,
          entrustedCompanyId: this.entrustedCompanyId,
        };
        getByEntrustedCompanyId(query).then((res) => {
          const data = res.data;
          this.options = data.data;
          this.selectLoading = false;
        });
      } else {
        this.initOptions();
      }
    },
    baseEmployeeFocus() {
      if (this.options.length == 0) {
        this.initOptions();
      }
    },
    //企业名称改变后判断是否还有该业务人员
    entrustedCompanyChange() {
      let serviceProjectList = this.options.filter((item) => {
        return item.id == this.form.baseEmployeeId;
      });
      //代表为空
      if (serviceProjectList.length == 0) {
        this.form.baseEmployeeId = ""; //代表id
        this.form.baseEmployeeName = ""; //代表名字
        this.form.baseEmployeeNumber = ""; //代表工号
        this.form.baseDepartmentName = ""; //部门名
        this.form.baseDepartmentId = ""; //部门id
      }
    },
    //代表姓名更改
    baseEmployeeChange(value) {
      let obj = this.options.filter((item) => {
        return item.id == value;
      });
      this.selectOption = obj[0];
      this.form.baseDepartmentName = this.selectOption.entrustedDeptShort;
    },
    //业务人员end
    toView(row) {
      this.$router.push({
        path: `/authenticationDoctor/detail/${
          row.doctorId ? row.doctorId : 0
        }/1`,
      });
    },
    rowCell(row, index) {
      this.$refs.crudDoctor.rowCell(row, index);
    },
    rowCancel(row, index) {
      row.$cellEdit = false;
      this.$refs.crudDoctor.rowCancel(row, index);
      this.onLoad(this.page);
    },
    rowStyle({ row }) {
      if (this.doctorIds.includes(row.doctorId)) {
        return {
          backgroundColor: "#eee",
        };
      }
    },
    //会员弹窗选中
    toggleRowSelection(data) {
      //第一个参数为数据，第二个参数为是否勾选
      this.$refs.dialogCrud.toggleRowSelection(data, true);
    },
    //获取会员列表
    getDialogList(params = {}) {
      this.dialogLoading = true;
      params.cityCode = this.form.cityCode;
      getPlanDoctor(
        this.dialogPage.currentPage,
        this.dialogPage.pageSize,
        Object.assign(params)
      ).then((res) => {
        const data = res.data.data;
        this.dialogPage.total = data.total;
        this.dialogData = data.records;
        this.showDialog = true;
        this.dialogLoading = false;
        this.$nextTick(() => {
          if (!this.$refs.dialogCrud.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.dialogCrud.doLayout();
          }
        });
      });
    },
    //获取已经选中会员id
    getExistDoctorIds() {
      getExistDoctorIds(this.id).then((res) => {
        const data = res.data.data;
        this.doctorIds = data;
        this.getDialogList();
      });
    },
    //清空搜索
    dialogSearchReset() {
      this.dialogQueryquery = {};
      this.getDialogList();
    },
    //搜索条件更改
    dialogSearchChange(params, done) {
      this.dialogQueryquery = params;
      this.dialogPage.currentPage = 1;
      this.getDialogList(params);
      done();
    },
    dialogCurrentChange(currentPage) {
      this.dialogPage.currentPage = currentPage;
      this.getDialogList();
    },
    dialogSizeChange(pageSize) {
      this.dialogPage.pageSize = pageSize;
      this.getDialogList();
    },
    //刷新
    dialogRefreshChange() {
      this.getDialogList();
    },
    //选中
    dialogSelectionChange(list) {
      this.dialogSelectionList = list;
    },
    closeDialog() {
      this.$refs.dialogCrud.toggleSelection();
    },
    //保存会员
    saveList() {
      if((this.form.type != 4) && ((this.dialogSelectionList.length + this.doctorIds.length) > 1)) {
        this.$message({
          type: "error",
          message: "只能邀请一个会员!",
        });
        return;
      }
      this.dialogSelectionList.map((item) => {
        item.planSearchNum = 1;
        item.lectureId = this.id;
        return item;
      });
      saveList(this.dialogSelectionList).then(
        () => {
          this.showDialog = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          console.log(error);
        }
      );
    },
    //打开会员弹窗
    openDialog() {
      this.dialogPage.currentPage = 1;
      this.getExistDoctorIds();
    },
    //end
    //邀请会员
    beforeOpen(done, type) {
      if (type == "add") {
        this.openDialog();
      } else {
        done();
      }
    },
    //修改邀请会员信息
    rowUpdate(row, index, done, loading) {
      if (validatenum(row.planSearchNum, 2) || row.planSearchNum <= 0) {
        this.$message({
          type: "error",
          message: "请输入大于0的整数!",
        });
        return;
      }

      savePlanSearchNum(row.id, row.planSearchNum).then(
        () => {
          this.onLoad(this.page);
          this.getDetail();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    //删除邀请会员
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return deleteDoctor(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    //搜索选择项更改
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },

    //选中
    selectionChange(list) {
      this.selectionList = list;
    },
    //取消选中
    selectionClear() {
      this.selectionList = [];
      this.$refs.crudDoctor.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.lectureId = this.id;
      getDoctorList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
        this.$nextTick(() => {
          if (!this.$refs.crudDoctor.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.crudDoctor.refreshTable();
            this.$refs.crudDoctor.doLayout();
          }
        });
      });
    },
    //end
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          _this.form.invitationStartDate = dayjs(
            _this.form.invitationStartDate
          ).format("YYYY-MM-DD");
          this.productNameChange(_this.form.baseProductId);
          this.selectOption = {
            id: _this.form.baseEmployeeId,
            name: _this.form.baseEmployeeName,
            jobNumber: _this.form.baseEmployeeNumber,
            entrustedDeptId: _this.form.baseDepartmentId,
          };
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },

    //自定义发起代表end
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      window.sessionStorage.setItem("active", 5);
      this.$router.go(-1);
    },

    //初始化数据
    async initProductOptions() {
      this.productSelectLoading = true;
      const res = await getProductList()
      const data = res.data;
      this.productOptions = data.data;
      this.productSelectLoading = false;
    },
    //涉及产品名称搜索
    remoteMethodProduct(query) {
      if (query !== "") {
        this.productSelectLoading = true;
        getProductList(query).then((res) => {
          const data = res.data;
          this.productOptions = data.data;
          this.productSelectLoading = false;
        });
      } else {
        this.initProductOptions();
      }
    },
    //获取焦点
    productNameFocus() {
      if (this.productOptions.length == 0) {
        this.initProductOptions();
      }
    },
    //涉及产品名称更改
    productNameChange(value) {
      let obj = this.productOptions.filter((item) => {
        return item.id == value;
      });
      if (value) {
        this.listByProductId();
      }

      this.productSelectOption = obj[0];
    },
    //涉及产品名称end
    //获取医患服务方案
    listByProductId() {
      listByProductId(this.form.baseProductId, 5).then(({ data: res }) => {
        this.option.column[4].dicData = res.data;
        this.serviceProjectList = res.data;
        this.baseProductChange();
      });
    },

    //产品改变后判断是否还有该条方案
    baseProductChange() {
      let serviceProjectList = this.serviceProjectList.filter((item) => {
        return item.id == this.form.serviceProjectId;
      });
      //方案为空
      if (serviceProjectList.length == 0) {
        this.form.serviceProjectId = "";
      }
    },

    //清空
    handleReset() {
      this.option.column[6].dicData = [];
      this.form.projectTypeName = "专业评审";
    },
    submit(form, done) {
      this.$refs.form.validate((valid) => {
        if (valid) {

          const params = {
            activityPlanType: 2,
            lectureDTO: {
              invitationStartDate:
                dayjs(form.invitationStartDate).format("YYYY-MM-DD") + " 00:00:00",
              invitationEndDate:
                dayjs(form.invitationEndDate).format("YYYY-MM-DD") + " 23:59:59",
              entrustedCompanyName: this.form.$entrustedCompanyId, //委托客户
              baseDepartmentName: form.baseDepartmentName,
              entrustedCompanyId: form.entrustedCompanyId,
              code: form.code,
              name: form.name,
              type: form.type,
              id: this.id,
              baseProductLine:this.productSelectOption.productLine, //产品线
              baseProductName: this.productSelectOption.productName, //产品名
              sfeProductId: this.productSelectOption.systemSfeId, //sfe产品id
              baseProductId: this.productSelectOption.id, //产品d
              baseEmployeeId: this.selectOption.id, //代表id
              baseEmployeeName: this.selectOption.name, //代表名字
              baseEmployeeNumber: this.selectOption.jobNumber, //代表工号
              baseDepartmentId: this.selectOption.entrustedDeptId, //部门id
            } }

          saveOrUpdate(params).then(
            () => {
              this.$message({
                type: "success",
                message: "保存成功!",
              });
              done();
              this.goBack();
            },
            (error) => {
              done();
              console.log(error);
            }
          );
        }
      });
    },

    handleClick(tab) {
      if (tab.name == "2") {
        this.onLoad(this.page);
      }
    },
  },
};
</script>

<style></style>
