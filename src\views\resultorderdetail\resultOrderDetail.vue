<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      :searchslot="true"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ type, size, row, index }" slot="resultOrderCode">
        <div
          class="to-view"
          @click="toResultOrder(row.resultOrderId)"
          v-if="row.resultOrderCode"
        >
          <a readonly>
            {{ row.resultOrderCode }}
          </a>
        </div>
        <div v-else>无</div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, remove } from "@/api/resultorderdetail/resultOrderDetail";
import option from "@/const/resultorderdetail/resultOrderDetail";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      sponsorHandleStatus: false,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.resultOrderDetail_add, false),
        viewBtn: this.vaildData(this.permission.resultOrderDetail_view, false),
        delBtn: this.vaildData(this.permission.resultOrderDetail_delete, false),
        editBtn: this.vaildData(this.permission.resultOrderDetail_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/resultorderdetail/detail/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    toResultOrder(id) {
      this.$router.push({
        path: `/resultorder/detail/${id}`,
      });
    },
    searchReset() {
      this.startTime = [];
      this.endTime = [];
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      //开始日期
      if (!this.validatenull(params.actStartTime)) {
        this.query.actStartTimeStart = params.actStartTime[0];
        this.query.actStartTimeEnd = params.actStartTime[1];
        delete this.query.actStartTime;
      }
      //结束日期
      if (!this.validatenull(params.actEndTime)) {
        this.query.actEndTimeStart = params.actEndTime[0];
        this.query.actEndTimeEnd = params.actEndTime[1];
        delete this.query.actEndTime;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);

      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
  },
};
</script>

<style></style>
