import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/meeting/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getPointList = (current, size, params) => {
  return request({
    url: "/api/blade-act/meeting/pointList",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getPage = (current, size, params) => {
  return request({
    url: "/api/blade-act/meeting/page",
    method: "post",
    data: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/meeting/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/meeting/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/meeting/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/meeting/submit",
    method: "post",
    data: row,
  });
};
//开始-结束-驳回计划
export const stopMeeting = (id, status) => {
  return request({
    url: "/api/blade-act/meeting/stopMeeting",
    method: "get",
    params: {
      id,
      status,
    },
  });
};

//生成会议二维码
export const createMeetingQRCode = (activityId, type) => {
  return request({
    url: "/api/blade-act/meeting/createMeetingQRCode",
    method: "get",
    params: {
      activityId,
      type,
    },
  });
};

//会议结束提交审核
export const submitAudit = (id) => {
  return request({
    url: "/api/blade-act/meeting/submitAudit",
    method: "get",
    params: {
      id,
    },
  });
};
//提交审核通用接口
export const submitRecheck = (row) => {
  return request({
    url: "/api/blade-act/meeting/submitRecheck",
    method: "post",
    data: row,
  });
};
//获取个人订单数据
export const meetingOrderList = (current, size, params) => {
  return request({
    url: "/api/blade-act/meeting/meetingOrderList",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
//重发合同
export const startPlanAgain = (id) => {
  return request({
    url: "/api/blade-act/meeting/startPlanAgain",
    method: "get",
    params: {
      id,
    },
  });
};
