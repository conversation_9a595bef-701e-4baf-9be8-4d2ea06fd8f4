export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  labelWidth: 150,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  menu: false,
  dialogClickModal: false,
  column: [
    {
      label: "姓名",
      prop: "doctor<PERSON>ame",
      type: "input",
    },
    {
      label: "身份证号",
      prop: "idCardNumber",
      type: "input",
    },
    {
      label: "完税金额",
      prop: "totalAmountLowercase",
      type: "input",
    },
    {
      label: "完税状态",
      prop: "taxCertStatus",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=tax_cert_status",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      disabled: true,
      dataType: "number",
      slot: true,
      width: 140,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "date",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      hide: true,
    },

    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
