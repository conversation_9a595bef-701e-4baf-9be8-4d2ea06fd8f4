<template>
  <div class="avue-logo">
    <transition name="fade">
      <div v-if="keyCollapse" class="logoImgSmall" key="0">
        <div class="logoImgSmall">
          <img height="40px" src="../../../public/img/logo64.png" />
        </div>
      </div>
    </transition>
    <transition-group name="fade">
      <template v-if="!keyCollapse">
        <div key="1" class="logoImg">
          <img height="54px" src="../../../public/img/logo.png" />
        </div>
        <!-- <span class="avue-logo_title" key="1">{{ website.indexTitle }} </span> -->
      </template>
    </transition-group>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "logo",
  data() {
    return {};
  },
  created() {},
  computed: {
    ...mapGetters(["website", "keyCollapse"]),
  },
  methods: {},
};
</script>

<style lang="scss">
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-active {
  transition: opacity 2.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.avue-logo {
  position: fixed;
  top: 0;
  left: 0;
  width: 240px;
  height: 64px;
  line-height: 64px;
  background-color: #9AD94E;
  font-size: 20px;
  overflow: hidden;
  box-sizing: border-box;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  color: rgba(255, 255, 255, 0.8);
  z-index: 1024;
  &_title {
    display: block;
    text-align: center;
    font-weight: 300;
    font-size: 20px;
  }
  &_subtitle {
    display: block;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
  }
}
.logoImg {
  width: 100%;
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}
.logoImgSmall {
  width: 100%;
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}
</style>
