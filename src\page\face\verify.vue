<template>
  <div class="face-verify">
    <Lottie :options="options" class="lottie-calss" loop :height="260" :width="260" @animCreated="handleAnimation" />
    <div v-if="status" class="text">身份核验成功<div class="small">返回微信小程序继续使用吧</div>
    </div>
    <div v-else class="text">身份核验失败<br>
      <div class="err-list">
        <div class="big">1.请确保本人操作</div>
        <div class="small">非本人操作将无法通过认证</div>
      </div>
      <div class="err-list">
        <div class="big">2.识别光线适中</div>
        <div class="small">请保证光线不要过暗或过亮</div>
      </div>
      <div class="err-list">
        <div class="big">3.正面对准手机</div>
        <div class="small">保持您的脸出现在取景框内</div>
      </div>
      <br><el-button type="danger" round @click="verifyBtn">返回重试</el-button>
    </div>
  </div>
</template>

<script>
import Lottie from 'vue-lottie'
import * as successFile from '@/assets/json/success.json'
import * as fail from '@/assets/json/fail.json'
import { mapGetters } from "vuex"
import {
  getFaceInfoSimpler,
} from "@/api/bdface/bdface";

export default {
  name: "faceverify",
  components: {
    Lottie
  },
  computed: {
    ...mapGetters(["userFace"]),
  },
  data() {
    return {
      anim: {}, animflow: {},
      options: {
        animationData: successFile.default
      },
      form: {},
      checked: false,
      loading: true,
      status: '',
    };
  },
  created() {
    this.status = this.$route.params.status == 'success' ? true : false;
    if (this.status) {
      this.options.animationData = successFile.default;
      //认证完成后，获取认证相关信息
      this.getInfo();
    } else {
      this.options.animationData = fail.default;
    }

  },
  methods: {
    handleAnimation(anim) {
      this.anim = anim
    },
    getInfo() {
      // 人脸识别认证结果 1认证一致 不在调用接口
      if (this.userFace.faceVerify == 1) {
        console.log('人脸识别认证结果 认证一致 不在调用接口')
        return;
      }
      getFaceInfoSimpler({ doctorId: this.userFace.doctorId, verifyToken: this.userFace.verifyToken }).then((res) => {
        this.loading = false;
        if (res.data.success) {
          // this.form = res.data.data;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    verifyBtn() {
      this.$router.push({ path: `/bdface/${this.userFace.doctorId}` })
    }
  }
};
</script>
<style lang="scss" scoped>
.face-verify {
  display: flex;
  margin: 50px auto;

  flex-direction: column;
  max-width: 800px;
  align-items: center;
  justify-content: center;

  .text {
    text-align: center;
    font-size: 36px;
    font-weight: bold;
    margin-top: 10px;
    width: 80%;

    .small {
      color: #999 !important;
      margin-top: 15px;
      font-size: 16px;
    }

    .err-list {
      width: 70%;
      font-size: 20px;
      font-weight: 500;
      text-align: center;
      margin: 20px auto 10px auto;

      .big {
        color: #333333;
        margin-bottom: 5px;
      }

      .small {
        color: #999 !important;
        font-size: 14px;
      }
    }
  }
}
</style>
