import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/openapi/sfe/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (params) => {
  return request({
    url: `/api/mp/api/contract/detail?activeId=${params.activeId}`,
    method: "get",
  });
};

export const getRetroactiveDetail = (params) => {
  return request({
    url: `/api/mp/api/contract/retroactiveDetail?activeId=${params.activeId}`,
    method: "get",
  });
};
