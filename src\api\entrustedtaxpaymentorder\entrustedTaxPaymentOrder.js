import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/entrustedTaxPaymentOrder/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/entrustedTaxPaymentOrder/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-svc/entrustedTaxPaymentOrder/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-svc/entrustedTaxPaymentOrder/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/entrustedTaxPaymentOrder/submit",
    method: "post",
    data: row,
  });
};

export const aloneHandle = (entrustedTaxPaymentOrderId) => {
  return request({
    url: "/api/blade-cmb/transferAccount/aloneHandle",
    method: "post",
    params: {
      entrustedTaxPaymentOrderId,
    },
  });
};
