<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @rowAdd="dialog = false"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!--      <template slot="backgroundImage" slot-scope="scope">
        <el-image style="width: 60px" :src="scope.row.backgroundImage" :preview-src-list="[scope.row.backgroundImage]" fit="contain"></el-image>
      </template>
      <template slot="barBackgroundImage" slot-scope="scope">
        <el-image style="width: 60px" :src="scope.row.barBackgroundImage" :preview-src-list="[scope.row.backgroundImage]" fit="barBackgroundImage"></el-image>
      </template> -->
      <template slot="menuLeft">
        <el-button
          type="danger"
          icon="el-icon-delete"
          plain
          v-if="permission.pageInfo_delete"
          @click="handleDelete"
          >删 除</el-button
        >
      </template>
      <template slot-scope="{ type, size, row, index }" index="1" slot="menu">
        <template>
          <el-button
            :size="option.size"
            type="text"
            icon="el-icon-turn-off"
            @click="changeStatus(row)"
            v-if="row.status == 1"
            >停用</el-button
          >
          <el-button
            :size="option.size"
            type="text"
            icon="el-icon-turn-off"
            v-else
            @click="changeStatus(row)"
            >启用</el-button
          >
        </template>
        <el-button
          :size="option.size"
          v-if="permission.pageInfo_view"
          type="text"
          icon="el-icon-view"
          @click="toView(row)"
          >查看</el-button
        >
        <el-button
          :size="option.size"
          v-if="permission.pageInfo_delete"
          type="text"
          icon="el-icon-edit"
          @click="$refs.crud.rowEdit(row, index), (pageId = row.id)"
          >编辑</el-button
        >
        <el-button
          :size="option.size"
          v-if="permission.pageInfo_delete"
          type="text"
          icon="el-icon-delete"
          @click="rowDel(row)"
          >删除</el-button
        >
      </template>
    </avue-crud>
    <el-dialog
      title="请勾选关联宣传页展示功能图标，确定保存："
      :visible.sync="dialog"
      width="60%"
      append-to-body
    >
      <div style="margin-left: 15px">
        已选
        <span v-for="(item, index) of lastList" :key="index"
          >{{ item.name }}&nbsp;&nbsp;</span
        >
      </div>
      <avue-crud
        ref="crud2"
        :option="option2"
        :data="data2"
        :page.sync="page2"
        v-model="form2"
        @current-change="currentChange2"
        @size-change="sizeChange2"
        @selection-change="selectionChange2"
        :before-open="beforeOpen2"
      ></avue-crud>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialog = false">取 消</el-button>
        <el-button type="primary" @click="confirm()">确 定</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  changeStatus,
  getIconList,
} from "@/api/pageInfo/pageInfo";
// import option from '@/const/pageInfo/pageInfo';
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      option: {
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "模板名称",
            prop: "name",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入模板名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "模板编号",
            prop: "code",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入模板编号",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "主页背景图",
            prop: "backgroundImage",
            type: "upload",
            listType: "picture-img",
            dataType: "string",
            action: "/api/blade-resource/oss/endpoint/put-file",
            propsHttp: {
              res: "data",
              url: "link",
            },
            rules: [
              {
                required: true,
                message: "请上传主页背景图",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "导航栏背景图",
            prop: "barBackgroundImage",
            type: "upload",
            listType: "picture-img",
            dataType: "string",
            action: "/api/blade-resource/oss/endpoint/put-file",
            propsHttp: {
              res: "data",
              url: "link",
            },
            rules: [
              {
                required: true,
                message: "请上传导航栏背景图",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "生效状态",
            prop: "status",
            type: "select",
            align: "center",
            width: 100,
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
            value: 0,
            slot: true,
            editDisplay: false,
          },
          {
            label: "关联图标",
            hide: true,
            prop: "iconNameList",
            placeholder: "请选择关联图标",
            labelWidth: "100",
            span: 24,
            rules: [
              {
                required: true,
                message: "请选泽关联图标",
                trigger: ["blur", "change"],
              },
            ],
            append: "去选择",
            type: "input",
            click: () => {
              this.shows();
            },
          },

          {
            label: "创建人",
            prop: "createUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建部门",
            prop: "createDept",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建时间",
            prop: "createTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改人",
            prop: "updateUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改时间",
            prop: "updateTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "是否已删除",
            prop: "isDeleted",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "版本控制",
            prop: "version",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
        ],
      },
      dialog: false, //选择关联图标dialog
      option2: {
        menu: false,
        height: "400px",
        addBtn: false,
        tip: false,
        border: true,
        index: true,
        selection: true,
        column: [
          {
            label: "图标名称",
            prop: "name",
          },
          {
            label: "选中图标",
            prop: "pitchOn",
            dataType: "string",
            type: "img",
          },
          {
            label: "未选中图标",
            prop: "unPitchOn",
            dataType: "string",
            type: "img",
          },
        ],
      },
      form2: {},
      page2: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      data2: [],
      lastList: [], //选中行
      form: {
        pageIconRelList: [],
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      // option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.pageInfo_add, false),
        viewBtn: this.vaildData(this.permission.pageInfo_view, false),
        delBtn: this.vaildData(this.permission.pageInfo_delete, false),
        editBtn: this.vaildData(this.permission.pageInfo_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    toggleSelection(val) {
      this.$nextTick(() => {
        this.$refs.crud2.toggleSelection([val]);
      });
    },
    confirm() {
      if (this.lastList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      } else if (this.lastList.length > 3) {
        this.$message.warning("最多选择三条数据");
        return;
      } else {
        let obj = [];
        let iconsName = [];
        this.lastList.map((i, index) => {
          // obj.push({ iconName: i.name, iconId: i.id, orderNum: i.$index });
          obj.push({ iconName: i.name, iconId: i.id, orderNum: index });
          iconsName.push(i.name);
        });
        this.lastList = obj;
        this.form.iconNameList = iconsName.join(",");
        this.form.pageIconRelList = this.lastList;
        this.dialog = false;
      }
    },
    // 分页
    currentChange2(currentPage) {
      this.page2.currentPage = currentPage;
      this.shows();
    },
    sizeChange2(pageSize) {
      this.page2.pageSize = pageSize;
      this.shows();
    },
    // 选中行
    selectionChange2(list) {
      this.lastList = list;
    },
    shows() {
      getIconList(this.page2.currentPage, this.page2.pageSize).then((res) => {
        this.page2.total = res.data.data.total;
        this.data2 = res.data.data.records;
        if (!this.dialog) {
          this.dialog = true;
          let pageIconRelList = this.form.pageIconRelList;
          if (Array.isArray(pageIconRelList) && pageIconRelList.length > 0) {
            pageIconRelList.map((item) => {
              this.data2.map((item2) => {
                if (item.iconId == item2.id || item.id == item2.id) {
                  this.toggleSelection(item2);
                }
              });
            });
          }
        }
      });
    },
    changeStatus(row) {
      changeStatus(row.id).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          console.log(error);
        }
      );
    },

    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    //打开前回调
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          let iconsName = [];
          res.data.data.iconInfoList.map((i) => {
            iconsName.push(i.name);
          });
          this.form.pageIconRelList = res.data.data.iconInfoList;
          this.form.iconNameList = iconsName.join(",");
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
