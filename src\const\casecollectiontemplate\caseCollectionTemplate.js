export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  delBtn: false,
  dialogClickModal: false,
  searchLabelWidth: "120",
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "客户名称",
      prop: "entrustedCompanyName",
      overHidden: true,
    },
    {
      label: "对应产品名称",
      prop: "templateProductName",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入关联产品名称",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "模版名称/标题",
      prop: "templateName",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入模版名称/标题",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "服务项目名称",
      prop: "projectTypeName",
    },
    {
      label: "关联产品目录ID",
      prop: "templateProductId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "模版启用状态",
      prop: "enable",
      type: "select",
      rules: [
        {
          required: true,
          message: "请选择模版启用状态",
          trigger: ["blur", "change"],
        },
      ],
      dicData: [
        {
          label: "停用",
          value: 0,
        },
        {
          label: "启用",
          value: 1,
        },
      ],
    },
    {
      label: "模版确认状态",
      prop: "confirmStatus",
      type: "select",
      rules: [
        {
          required: true,
          message: "请选择模版确认状态",
          trigger: ["blur", "change"],
        },
      ],
      dicData: [
        {
          label: "待确认",
          value: 0,
        },
        {
          label: "已确认",
          value: 1,
        },
        {
          label: "已拒绝",
          value: 2,
        },
      ],
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态 ",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
