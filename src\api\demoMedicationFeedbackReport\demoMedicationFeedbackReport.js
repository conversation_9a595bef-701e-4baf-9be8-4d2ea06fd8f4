import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/medicationFeedbackReport/list",
    method: "post",
    data: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/demo/medicationFeedbackReport/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/demo/medicationFeedbackReport/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/demo/medicationFeedbackReport/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/demo/medicationFeedbackReport/submit",
    method: "post",
    data: row,
  });
};
//活动审核
export const audit = (row) => {
  return request({
    url: "/api/blade-act/demo/medicationFeedbackReport/audit",
    method: "post",
    data: row,
  });
};
