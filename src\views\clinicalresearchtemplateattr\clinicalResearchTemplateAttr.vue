<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="attrType" slot-scope="{ row }">
        <el-tag v-if="row.attrType == 1" type="warning" size="small"
          >单选</el-tag
        >
        <el-tag v-if="row.attrType == 2" type="success" size="small"
          >多选</el-tag
        >
        <el-tag v-if="row.attrType == 3" size="small" type="info">文本</el-tag>
        <el-tag v-if="row.attrType == 4" size="small">图片</el-tag>
      </template>
      <template slot-scope="{ type, disabled }" slot="attrValueForm">
        <el-tag
          :key="tag"
          v-for="tag in attrValue"
          :closable="type == 'add' || type == 'edit'"
          :disable-transitions="false"
          @close="handleClose(tag)"
        >
          {{ tag }}
        </el-tag>
        <template v-if="type == 'add' || type == 'edit'">
          <el-input
            class="input-new-tag"
            v-if="inputVisible"
            v-model="inputValue"
            ref="saveTagInput"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
          >
          </el-input>
          <el-button v-else class="button-new-tag" @click="showInput"
            >+ 问卷题目选项</el-button
          >
          <div style="margin-top: 30px; width: 85%; color: red">
            <div>
              <div>
                温馨提示:
                <div>
                  问卷的题目选项中如存在需要客户主动填写内容的，请放在最后一个选择项，且选项内容固定输入为<br />“其他（请输入）”<br />
                </div>
              </div>
            </div>
          </div>
        </template>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from "@/api/clinicalresearchtemplateattr/clinicalResearchTemplateAttr";
import option from "@/const/clinicalresearchtemplateattr/clinicalResearchTemplateAttr";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      inputVisible: false,
      inputValue: "",
      attrValue: [],
      form: { attrType: "", attrValue: "问卷题目选项" },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.clinicalResearchTemplateAttr_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.clinicalResearchTemplateAttr_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.clinicalResearchTemplateAttr_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.clinicalResearchTemplateAttr_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  watch: {
    "form.attrType"(value) {
      if (value == 1 || value == 2) {
        this.option.column[5].addDisplay = true;
        this.option.column[5].editDisplay = true;
        this.option.column[5].viewDisplay = true;
      } else {
        this.option.column[5].addDisplay = false;
        this.option.column[5].editDisplay = false;
        this.option.column[5].viewDisplay = false;
      }
    },
  },
  methods: {
    //问卷题目选项
    handleClose(tag) {
      this.attrValue.splice(this.attrValue.indexOf(tag), 1);
    },
    showInput() {
      let value = this.attrValue.join(",");
      if (value.length >= 500) {
        this.$message({
          type: "error",
          message: "问卷题目选项最多输入500字!",
        });
        return;
      }
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue == "") {
        this.$message({
          type: "error",
          message: "问卷题目选项不能为空!",
        });
        return;
      }
      let reg = ",";
      let reg2 = ",";
      if (inputValue.indexOf(reg) != -1 || inputValue.indexOf(reg2) != -1) {
        this.$message({
          type: "error",
          message: "问卷题目选项不能输入逗号!",
        });
        return;
      }

      let value = this.attrValue.join(",");
      let length = value.length + this.inputValue.length + 1;
      if (length >= 500) {
        this.$message({
          type: "error",
          message: "问卷题目选项最多输入500字!",
        });
        return;
      }
      this.form.attrValue = inputValue;
      this.attrValue.push(inputValue);
      this.inputVisible = false;
      this.inputValue = "";
    },
    //end
    rowSave(row, done, loading) {
      if (
        this.form.attrType != 3 &&
        this.form.attrType != 4 &&
        this.attrValue.length == 0
      ) {
        this.$message({
          type: "error",
          message: "问卷题目选项不能为空!",
        });
        loading();
        return;
      }
      if (this.form.attrType != 3) {
        row.attrValue = this.attrValue.join(",");
      } else {
        row.attrValue = "";
      }
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      if (
        this.form.attrType != 3 &&
        this.form.attrType != 4 &&
        this.attrValue.length == 0
      ) {
        this.$message({
          type: "error",
          message: "问卷题目选项不能为空!",
        });
        loading();
        return;
      }
      if (this.form.attrType != 3) {
        row.attrValue = this.attrValue.join(",");
      } else {
        row.attrValue = "";
      }
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    beforeOpen(done, type) {
      this.attrValue = [];
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          this.attrValue = this.form.attrValue
            .split(",")
            .filter((item) => item != "");
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
