import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-svc/orderDoctorPaymentPoint/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-svc/orderDoctorPaymentPoint/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-svc/orderDoctorPaymentPoint/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-svc/orderDoctorPaymentPoint/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-svc/orderDoctorPaymentPoint/submit',
    method: 'post',
    data: row
  })
}

export const auditPass = (row) => {
  return request({
    url: 'api/blade-svc/orderDoctorPaymentPoint/auditPass',
    method: 'post',
    data: row
  })
}
export const auditReject = (row) => {
  return request({
    url: 'api/blade-svc/orderDoctorPaymentPoint/auditReject',
    method: 'post',
    data: row
  })
}
export const thaw = (ids) => {
  return request({
    url: "/api/blade-svc/orderDoctorPaymentPoint/thaw",
    method: "post",
    params: {
      ids,
    },
  });
}



