export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      label: "功能名称",
      prop: "functionName",
      type: "input",
      span: 24,
    },
    {
      label: "图标名称",
      prop: "name",
      type: "input",
      search: true,
      span: 24,
    },
    {
      label: "选中图标",
      prop: "pitchOn",
      type: "upload",
      listType: "picture-img",
      dataType: "string",
      action: "/api/blade-resource/oss/endpoint/put-file",
      propsHttp: {
        res: "data",
        url: "link",
      },
      rules: [
        {
          required: true,
          message: "请上传选中图标",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "未选中图标",
      prop: "unPitchOn",
      type: "upload",
      listType: "picture-img",
      dataType: "string",
      action: "/api/blade-resource/oss/endpoint/put-file",
      propsHttp: {
        res: "data",
        url: "link",
      },
      rules: [
        {
          required: true,
          message: "请上传未选中图标",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      label: "是否选中",
      prop: "",
      type: "select",
      align: "center",
      width: 100,
      dicData: [
        {
          label: "未选中",
          value: 1,
        },
        {
          label: "已选中",
          value: 2,
        },
      ],
      value: 0,
      slot: true,
    },

    {
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      label: "功能地址",
      prop: "url",
      type: "input",
    },
    {
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      label: "序号",
      prop: "orderNum",
      type: "input",
    },
    {
      label: "系统状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
