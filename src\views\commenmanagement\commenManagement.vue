<template>
  <basic-container>
    <avue-crud :option="option"
               :search.sync="search"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad"
               @tree-load="load"
    >
      <template #interaction="{ row }">
            <span class="interaction">
              <span><i class="interaction-icon el-icon-thumb"></i>{{ row.likeNum || 0 }}</span>
              <span><i class="interaction-icon el-icon-chat-square"></i>{{ row.commentNum || 0 }}</span>
            </span>
      </template>
      <template #status="{ row }">
            <el-tag v-if="row.status == 1" type="info">已下架</el-tag>
            <el-tag v-if="row.status == 2" type="success">已发布</el-tag>
            <el-tag v-if="row.status == 0" type="warning">待审核</el-tag>
      </template>
      <template slot-scope="{ row }" slot="menu">
<!--        v-if="permission.commenManagement_view"-->
<!--        <el-button-->
<!--          type="text"-->
<!--          @click="viewDetailClick(row)"-->
<!--        >查看详情</el-button>-->
        <el-button
          type="text"
          v-if="permission.commenManagement_edit && (row.status != '2')"
          @click="rowUpdate([row], 2)"
        >发布</el-button>
        <el-button
          type="text"
          v-if="permission.commenManagement_edit && (row.status == '2')"
          @click="rowUpdate([row], 1)"
        >下架</el-button>
        <el-button
          type="text"
          v-if="permission.commenManagement_delete && (row.status != '2')"
          @click="rowUpdate([row], 4)"
        >删除</el-button>
      </template>
      <template slot="menuLeft">
        <el-dropdown>
          <el-button>
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="rowUpdate(selectionList, 2)">批量发布</el-dropdown-item>
            <el-dropdown-item @click.native="rowUpdate(selectionList, 1)">批量下架</el-dropdown-item>
            <el-dropdown-item @click.native="rowDel(selectionList.map(item => item.id))">批量删除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </avue-crud>
    <el-dialog
      title="评论详情"
      append-to-body
      :visible.sync="showDialog"
      width="500px"
    >
      <div class="comment-section">
        <comment-list :list="commentList" />
      </div>
      <span slot="footer" class="dialog-footer">
          <el-button @click="showDialog = false">关 闭</el-button>
        </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, updateStatus, remove, treeById} from "@/api/commenmanagement/commenManagement";
  import option from "@/option/commenmanagement/commenManagement";
  import {mapGetters} from "vuex";
  import {exportBlob} from "@/api/common";
  import {getToken} from '@/util/auth';
  import {downloadXls} from "@/util/util";
  import {dateNow} from "@/util/date";
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';
  import CommentList from "@/views/commenmanagement/commentList.vue";

  export default {
    components: {CommentList},
    data() {
      return {
        showDialog: false,
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        commentList: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.commenManagement_add, false),
          viewBtn: this.vaildData(this.permission.commenManagement_view, false),
          delBtn: this.vaildData(this.permission.commenManagement_delete, false),
          editBtn: this.vaildData(this.permission.commenManagement_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      if(this.$route.query.contentId) {
        this.search.contentId = this.$route.query.contentId;
        this.query.contentId = this.$route.query.contentId;
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      viewDetailClick(row) {
        this.showDialog = true
        treeById(row.id).then(res => {
          this.commentList = res.data.data;
        });
      },
      rowUpdate(selection, status) {
        const ids = selection.map(item => item.id)

        if(ids.length === 0 ){
          this.$message.warning("请选择至少一条数据");
          return;
        }
        const statusName = {
          2: '发布',
          1: '下架',
          4: '删除'
        }
        this.$confirm(`确定是否${statusName[status]}?`, {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return updateStatus({ ids, status });
          })
          .then(() => {
            this.onLoad(this.page);

            this.$refs.crud.refreshTable();
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      rowDel(ids) {
        if(ids.length === 0 ){
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定是否删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleExport() {
        let downloadUrl = `/api/blade-commenManagement/commenManagement/export-commenManagement?${this.website.tokenHeader}=${getToken()}`;
        const {
        } = this.query;
        let values = {
        };
        this.$confirm("是否导出数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          NProgress.start();
          exportBlob(downloadUrl, values).then(res => {
            downloadXls(res.data, `评论管理表${dateNow()}.xlsx`);
            NProgress.done();
          })
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;

        getList(page.currentPage, page.pageSize, Object.assign(params, this.query, { type: this.active })).then(res => {
          const data = res.data.data ;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      load(tree, treeNode, resolve) {
        treeById(tree.id).then(res => {
          resolve(res.data.data)
        })
      },
    }
  };
</script>

<style>
.interaction {
  display: flex;
  gap: 12px;
  color: #ababab;
}
.interaction-icon {
  margin-right: 5px;
}
.comment-section {
  padding: 0 20px;
}
</style>
