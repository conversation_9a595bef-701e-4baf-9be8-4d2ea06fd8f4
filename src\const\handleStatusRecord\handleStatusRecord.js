export default {
  title: "代发状态记录",
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  selection: true,
  dialogClickModal: false,
  menu: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "业务请求状态",
      prop: "reqsta",
      type: "select",
      dicData: [
        {
          label: "数据接收中",
          value: "OPR",
        },
        {
          label: "等待审批",
          value: "AUT",
        },
        {
          label: "终审完毕",
          value: "NTE",
        },
        {
          label: "银行人工审批",
          value: "APW",
        },
        {
          label: "可疑",
          value: "WRF",
        },
        {
          label: "银行处理中",
          value: "BNK",
        },
        {
          label: "完成",
          value: "FIN",
        },
      ],
    },
    {
      label: "业务请求结果",
      prop: "rtnflg",
      type: "select",
      dicData: [
        {
          label: "成功",
          value: "S",
        },
        {
          label: "失败",
          value: "F",
        },
        {
          label: "撤销",
          value: "C",
        },
        {
          label: "过期 企业过期不审批",
          value: "D",
        },
        {
          label: "否决 企业审批否决",
          value: "R",
        },
      ],
    },
    {
      label: "错误描述",
      prop: "errdsp",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
