<template>
  <div>
    <basic-container class="el-card__body">
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
        @submit="submit"
      >
        <template slot-scope="{ disabled, size }" slot="resultOrderFile">
          <div style="cursor: pointer">
            <el-tag @click="previewOrder(form.resultOrderFile)"
              >《企业服务成果清单》</el-tag
            >
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
      <div style="height: 50px"></div>
      <!-- 成果总结 -->
      <avue-crud
        :option="resultOrderProjectOption"
        :table-loading="loading"
        :data="resultOrderProjectData"
        :page.sync="resultOrderProjectPage"
        v-model="form"
        ref="resultOrderProjectCrud"
        @current-change="currentChangeResultOrderProject"
        @size-change="sizeChangeResultOrderProject"
        @refresh-change="refreshChangeResultOrderProject"
        @selection-change="selectionDoctorChange"
        @on-load="onLoadResultOrderProject"
      >
      </avue-crud>
      <div style="height: 50px"></div>
      <!-- 医师明细报告 -->
      <avue-crud
        :option="resultOrderDoctorOption"
        :table-loading="loading"
        :data="resultOrderDoctorData"
        :page.sync="resultOrderDoctorPage"
        v-model="form"
        ref="resultOrderDoctorCrud"
        @current-change="currentChangeResultOrderDoctor"
        @size-change="sizeChangeResultOrderDoctor"
        @refresh-change="refreshChangeResultOrderDoctor"
        @selection-change="selectionDoctorChange"
        @on-load="onLoadResultOrderDoctor"
      >
        <template slot-scope="{ type, size, row, index }" slot="orderNumber">
          <div
            @click="openOrderDoctor(row)"
            style="cursor: pointer; color: rgb(58, 159, 218)"
          >
            {{ row.orderNumber }}
          </div>
        </template>
        <template slot="menuLeft">
          <el-button
            type="primary"
            v-if="permission.resultOrder_handleExport"
            @click="handleExport"
            >导 出
          </el-button>
        </template>
      </avue-crud>
      <div style="height: 50px"></div>
      <!-- 平台服务报告 -->
      <avue-crud
        :table-loading="resultOrderPlatformLoading"
        :data="resultOrderPlatformData"
        :option="resultOrderPlatformOption"
        @refresh-change="onLoadResultOrderPlatform"
        @on-load="onLoadResultOrderPlatform"
      >
        <template slot="filePath" slot-scope="{ type, size, row, index }">
          <el-button
            size="small"
            type="text"
            @click="previewOrder(row.filePath)"
            >预览报告
          </el-button>
        </template>
      </avue-crud>

      <div style="height: 200px"></div>

      <!-- 会员订单详情 -->
      <el-dialog
        title="会员订单详情"
        append-to-body
        :visible.sync="orderDoctorDialog"
        width="75%"
        v-if="orderDoctorDialog"
      >
        <avue-crud
          :table-loading="orderDoctorLoading"
          :data="orderDoctorData"
          :option="orderDoctorOption"
          :page.sync="orderDoctorPage"
          @current-change="orderDoctorCurrentChange"
          @size-change="orderDoctorizeChange"
          @refresh-change="orderDoctorRefreshChange"
          @on-load="onLoadOrderDoctor"
        >
          <template slot-scope="{ row }" slot="menu">
            <el-button type="text" @click="toOrderdoctorView(row)"
              >查看</el-button
            >
          </template>
        </avue-crud>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import {
  getDetail,
  resultOrderProjectList,
  resultOrderDoctorList,
  orderDoctorList,
  resultOrderPlatformList,
} from "@/api/resultorder/resultOrder";
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from "nprogress";
export default {
  data() {
    return {
      activityLoading: true,
      sponsorHandleStatus: false,
      id: "",
      //基础信息
      option: {
        disabled: true,
        labelWidth: "150",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "编号",
            prop: "code",
            readonly: true,
          },
          {
            label: "客户名称",
            prop: "entrustedCompanyId",
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            dicUrl: `/api/blade-csc/entrustedCompany/getList`,
          },

          {
            label: "业务期间",
            prop: "accountPeriod",
            type: "month",
            format: "yyyy年MM月",
            valueFormat: "yyyy年MM月",
          },
          {
            label: "服务开始日期",
            prop: "actStartTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "服务结束日期",
            prop: "actEndTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "服务会员数量",
            prop: "numberService",
            readonly: true,
          },
          {
            label: "服务活动金额",
            prop: "actFee",
            readonly: true,
          },
          {
            label: "成果单总金额",
            prop: "actTotalFee",
            readonly: true,
          },

          {
            label: "成果单创建时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            readonly: true,
          },
          // {
          //   label: "成果单总结人员",
          //   prop: "summaryPersonnel",
          // },
          // {
          //   label: "成果单提交时间",
          //   prop: "submitDate",
          //   type: "datetime",
          //   format: "yyyy-MM-dd HH:mm:ss",
          //   valueFormat: "yyyy-MM-dd HH:mm:ss",
          //   readonly: true,
          // },

          {
            label: "结算单编号",
            prop: "settlementActivityCode",
            type: "input",
          },
          {
            label: "结算单金额",
            prop: "settlementActivityAmount",
            type: "input",
          },
          {
            label: "服务成果单报告",
            prop: "resultOrderFile",
          },
        ],
      },
      form: {},
      loading: false,
      //会员明细列表
      dialogTitle: "专业学术会议",
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      doctorOption: {
        maxHeight: "600",
        title: "会员明细列表",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        menu: false,
        selection: true,
        dialogClickModal: false,
        headerAlign: "center",
        align: "center",
        column: [
          {
            label: "会员名称",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "病例信息征集",
            children: [
              {
                label: "计划数量",
                prop: "caseCollectionPlanNum",
              },
              {
                label: "验收数量",
                prop: "caseCollectionNum",
              },
              {
                label: "服务费用",
                prop: "caseCollectionFee",
              },
            ],
          },
          {
            label: "临床信息调研",
            children: [
              {
                label: "计划数量",
                prop: "clinicalResearchPlanNum",
              },
              {
                label: "验收数量",
                prop: "clinicalResearchNum",
              },
              {
                label: "服务费用",
                prop: "clinicalResearchFee",
              },
            ],
          },
          {
            label: "用药反馈",
            children: [
              {
                label: "计划数量",
                prop: "medicationFeedbackPlanNum",
              },
              {
                label: "验收数量",
                prop: "medicationFeedbackNum",
              },
              {
                label: "服务费用",
                prop: "medicationFeedbackFee",
              },
            ],
          },
          {
            label: "结算总费用(元)",
            prop: "settleSumFee",
            type: "input",
          },
          {
            label: "服务总数量",
            prop: "settleSumNum",
            type: "input",
          },
        ],
      },
      doctorData: [],
      //end
      // 成果总结
      resultOrderProjectPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      resultOrderProjectOption: {
        maxHeight: "600",
        title: "服务成果总结",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        menu: false,
        selection: true,
        dialogClickModal: false,
        headerAlign: "center",
        align: "center",
        column: [
          {
            label: "编号",
            prop: "code",
            type: "input",
          },
          {
            label: "服务项目编号",
            prop: "projectCode",
            type: "input",
          },
          {
            label: "服务项目大类",
            prop: "projectCategory",
            type: "input",
          },
          {
            label: "服务项目名称",
            prop: "projectTypeName",
            type: "input",
          },
          {
            label: "提交数量",
            prop: "submitNumber",
            type: "input",
          },
          {
            label: "提交金额",
            prop: "submitAmount",
            type: "input",
          },

          {
            label: "验收数量",
            prop: "checkNumber",
            type: "input",
          },
          {
            label: "验收金额",
            prop: "checkAmount",
            type: "input",
          },
          {
            label: "达标率",
            prop: "complianceRate",
            type: "input",
          },
        ],
      },
      resultOrderProjectData: [],
      //end
      // 医师明细报告
      resultOrderDoctorPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      resultOrderDoctorOption: {
        maxHeight: "600",
        title: "服务会员明细",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        menu: false,
        selection: true,
        dialogClickModal: false,
        headerAlign: "center",
        align: "center",
        column: [
          {
            label: "会员姓名",
            prop: "name",
            type: "input",
          },
          {
            label: "手机号码",
            prop: "phone",
            type: "input",
          },
          {
            label: "服务类别数量",
            prop: "projectNumber",
            type: "input",
          },
          {
            label: "服务订单数量",
            prop: "orderNumber",
            type: "input",
          },
          {
            label: "验收金额",
            prop: "checkAmount",
            type: "input",
          },
        ],
      },
      resultOrderDoctorData: [],
      //end
      // 会员订单详情
      doctorId: "",
      orderDoctorDialog: false,
      orderDoctorPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      orderDoctorOption: {
        height: "500",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "订单编号",
            prop: "code",
            type: "input",
          },
          {
            label: "客户名称",
            prop: "entrustedCompanyName",
            type: "input",
          },
          {
            label: "订单发布时间",
            prop: "realityStartDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "订单计划开始时间",
            prop: "invitationStartDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "订单计划结束时间",
            prop: "invitationEndDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },

          {
            label: "产品名",
            prop: "baseProductName",
            type: "input",
          },
          {
            label: "服务项目类型",
            prop: "projectTypeName",
            type: "input",
          },
          {
            label: "订单任务数量",
            prop: "planNum",
            type: "input",
            width: "120",
            overHidden: true,
          },
          {
            label: "订单接单时间",
            prop: "receivingOrderData",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
        ],
      },
      orderDoctorData: [],
      orderDoctorLoading: false,
      // end
      // 平台服务报告
      resultOrderPlatformLoading: false,
      resultOrderPlatformOption: {
        maxHeight: "600",
        title: "平台管理服务",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        menu: false,
        selection: true,
        dialogClickModal: false,
        headerAlign: "center",
        align: "center",
        column: [
          {
            label: "平台名称",
            prop: "platformName",
            type: "input",
          },
          {
            label: "业务期间",
            prop: "accountPeriod",
            type: "input",
          },
          {
            label: "服务开始时间",
            prop: "beginDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "服务结束时间",
            prop: "endDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "服务管理金额",
            prop: "amount",
            type: "input",
          },
          // {
          //   label: "报告",
          //   prop: "filePath",
          //   type: "input",
          // },
        ],
      },
      resultOrderPlatformData: [],
      //end
    };
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  created() {
    this.id = this.$route.params.id;
    this.init();
  },
  methods: {
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    init() {
      this.getDetail();
    },
    getDetail() {
      this.activityLoading = true;
      getDetail(this.id).then((res) => {
        this.activityLoading = false;
        let data = res.data;
        this.form = data.data;
      });
    },
    submit() {},
    // 成果总结
    onLoadResultOrderProject(page) {
      resultOrderProjectList(page.currentPage, page.pageSize, this.id).then(
        (res) => {
          const data = res.data.data;
          this.resultOrderProjectPage.total = data.total;
          data.records.map((item) => {
            item.complianceRate = item.complianceRate * 100 + "%";
            return item;
          });
          this.resultOrderProjectData = data.records;
          this.loading = false;
        }
      );
    },
    refreshChangeResultOrderProject() {
      this.onLoadResultOrderProject(this.resultOrderProjectPage);
    },
    currentChangeResultOrderProject(currentPage) {
      this.resultOrderProjectPage.currentPage = currentPage;
    },
    sizeChangeResultOrderProject(pageSize) {
      this.resultOrderProjectPage.pageSize = pageSize;
    },
    //end
    // 医师明细报告
    handleExport() {
      let downloadUrl = `/api/blade-svc/resultOrderDoctorDetail/export-resultOrderDoctorDetail?${
        this.website.tokenHeader
      }=${getToken()}`;
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, { resultOrderId: this.id }).then((res) => {
          downloadXls(res.data, `服务会员明细${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    onLoadResultOrderDoctor(page) {
      resultOrderDoctorList(page.currentPage, page.pageSize, this.id).then(
        (res) => {
          const data = res.data.data;
          this.resultOrderDoctorPage.total = data.total;
          this.resultOrderDoctorData = data.records;
          this.loading = false;
        }
      );
    },
    refreshChangeResultOrderDoctor() {
      this.onLoadResultOrderDoctor(this.resultOrderDoctorPage);
    },
    currentChangeResultOrderDoctor(currentPage) {
      this.resultOrderDoctorPage.currentPage = currentPage;
    },
    sizeChangeResultOrderDoctor(pageSize) {
      this.resultOrderDoctorPage.pageSize = pageSize;
    },
    // 会员订单详情
    toOrderdoctorView(row) {
      this.$router.push({
        path: `/orderdoctor/detail/${row.id}`,
      });
    },
    openOrderDoctor(row) {
      console.log(row);
      this.orderDoctorPage.currentPage = 1;
      this.orderDoctorPage.pageSize = 10;
      this.doctorId = row.doctorId;
      this.orderDoctorDialog = true;
      this.orderDoctorLoading = true;
    },
    onLoadOrderDoctor(page) {
      orderDoctorList(
        page.currentPage,
        page.pageSize,
        this.doctorId,
        this.id
      ).then((res) => {
        const data = res.data.data;
        this.orderDoctorPage.total = data.total;
        this.orderDoctorData = data.records;

        this.orderDoctorLoading = false;
      });
    },
    orderDoctorRefreshChange() {
      this.onLoadOrderDoctor(this.resultOrderDoctorPage);
    },
    orderDoctorCurrentChange(currentPage) {
      this.orderDoctorPage.currentPage = currentPage;
    },
    orderDoctorizeChange(pageSize) {
      this.orderDoctorPage.pageSize = pageSize;
    },
    //end

    //平台服务报告
    onLoadResultOrderPlatform() {
      this.resultOrderPlatformLoading = true;
      resultOrderPlatformList(this.id).then((res) => {
        const data = res.data.data;
        this.resultOrderPlatformData = data.records;
        this.resultOrderPlatformLoading = false;
      });
    },
    //end

    previewOrder(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}
.sub-button {
  display: flex;
  justify-content: center;
}
.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
