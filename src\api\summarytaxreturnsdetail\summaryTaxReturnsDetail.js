import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-svc/summaryTaxReturnsDetail/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-svc/summaryTaxReturnsDetail/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-svc/summaryTaxReturnsDetail/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-svc/summaryTaxReturnsDetail/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-svc/summaryTaxReturnsDetail/submit',
    method: 'post',
    data: row
  })
}
export const downloadIdCard = (id) => {
  return request({
    url: '/api/blade-svc/summaryTaxReturnsDetail/downloadIdCard?summaryTaxReturnsId='+id,
    method: 'get',
    params: {}
  })
}
export const downloadIdCardSeal = (id) => {
  return request({
    url: '/api/blade-svc/summaryTaxReturnsDetail/downloadIdCardSeal?summaryTaxReturnsId='+id,
    method: 'get',
    params: {}
  })
}
export const idCardDispose = (id) => {
  return request({
    url: '/api/blade-svc/summaryTaxReturnsDetail/idCardDispose?summaryTaxReturnsId='+id,
    method: 'get',
    params: {}
  })
}
export const toResultOrder = (id) => {
  return request({
    url: "/api/blade-svc/summaryTaxReturns/create",
    method: "post",
    params: {
      id,
    },
  });
};


