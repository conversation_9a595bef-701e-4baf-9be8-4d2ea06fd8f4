<template>
  <div>
    <basic-container>
      <div style="position: relative">
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
        @submit="submit"
      >
        <template slot-scope="{ disabled, size }" slot="serviceResultFile">
          <div style="cursor: pointer">
            <el-tag @click="previewOrder(form.serviceResultFile)"
              >《自然人成果清单》</el-tag
            >
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>

      <!--订单明细-->
      <avue-crud
        :option="orderOption"
        :table-loading="loading"
        :data="orderData"
        @refresh-change="refreshChangeActivity"
      >
        <template slot-scope="{ type, size, row, index }" slot="code">
          <div
            class="to-view"
            @click="toOrderdoctorDetail(row.id)"
            v-if="row.code"
          >
            <a readonly>
              {{ row.code }}
            </a>
          </div>
          <div v-else>无</div>
        </template>
      </avue-crud>
      <div style="height: 50px"></div>
      <!-- 成果明细 -->
      <avue-crud
        :table-loading="loading"
        :data="activityData"
        :option="activityOption"
        @refresh-change="refreshChangeActivity"
      >
        <template
          slot="file"
          slot-scope="{ type, size, row, index }"
          v-if="row.actTypeName != '汇总'"
        >
          <el-button
            v-if="!validatenull(row.file)"
            size="small"
            type="text"
            @click="previewPDF(row)"
            >预览报告
          </el-button>
        </template>
      </avue-crud>

      <div style="height: 200px"></div>
      <el-dialog
        title="报告预览"
        :visible.sync="dialogVisiblePdf"
        append-to-body
        width="50%"
      >
        <iframe
          :src="serviceManagerReport"
          width="100%"
          height="600"
          title="协议"
          frameBorder="no"
          border="0"
          marginWidth="0"
          marginHeight="0"
          scrolling="no"
          allowTransparency="yes"
        ></iframe>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/resultorderdetail/resultOrderDetail";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      //订单明细
      orderOption: {
        maxHeight: "600",
        calcHeight: 65,
        title: "订单明细",
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        menu: false,
        dialogClickModal: false,
        column: [
          {
            label: "订单编号",
            prop: "code",
            type: "input",
          },
          {
            label: "订单开始时间",
            prop: "invitationStartDate",
            type: "date",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd",
          },

          {
            label: "订单结束时间",
            prop: "invitationEndDate",
            type: "date",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "订单发布时间",
            prop: "realityStartDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "订单接单时间",
            prop: "receivingOrderData",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "接单人员姓名",
            prop: "doctorName",
            type: "input",
          },

          {
            label: "接单人员单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "接单人员部门",
            prop: "departmentName",
            type: "input",
          },

          {
            label: "订单计划数量",
            prop: "planNum",
            type: "input",
          },
          {
            label: "订单完成数量",
            prop: "finishNum",
            type: "input",
          },
        ],
      },
      orderData: [],
      //end
      loading: true,
      dialogVisiblePdf: false,
      id: "",
      //基础信息
      form: {},
      option: {
        disabled: true,
        labelWidth: "170",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "自然人成果编号",
            prop: "code",
            type: "input",
          },
          {
            label: "客户名称",
            prop: "entrustedCompanyName",
            type: "input",
          },
          {
            label: "平台方",
            prop: "generalContractor",
            type: "input",
          },
          {
            label: "会员名称",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "业务期间",
            prop: "accountPeriod",
            type: "input",
          },
          {
            label: "活动开始日期",
            prop: "actStartTime",
            type: "date",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "活动结束日期",
            prop: "actEndTime",
            type: "date",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "服务项目数量",
            prop: "checkNumber",
            type: "input",
          },
          {
            label: "服务总金额",
            prop: "serviceResultAmount",
            type: "input",
          },
          // {
          //   label: "协调部门",
          //   prop: "employeeDept",
          //   type: "input",
          // },
          // {
          //   label: "协调代表",
          //   prop: "employeeName",
          //   type: "input",
          // },
          {
            label: "自然人服务成果单",
            prop: "serviceResultFile",
            type: "input",
          },
        ],
      },

      dialogData2: [],
      activityData: [],
      activityOption: {
        maxHeight: "600",
        title: "成果明细",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        menu: false,
        dialogClickModal: false,
        column: [
          {
            label: "服务项目",
            prop: "projectTypeName",
            type: "input",
          },

          {
            label: "产品线",
            prop: "baseProductLine",
            type: "input",
          },
          {
            label: "产品名称",
            prop: "baseProductName",
            type: "input",
          },

          {
            label: "项目方案",
            prop: "serviceProjectName",
            type: "input",
          },
          {
            label: "订单单价",
            prop: "taskUnitPrice",
            type: "input",
          },

          {
            label: "计划任务量",
            prop: "planNumber",
            type: "input",
          },

          {
            label: "完成任务量",
            prop: "finishNumber",
            type: "input",
          },
          {
            label: "验收任务量",
            prop: "resultNumber",
            type: "input",
          },
          {
            label: "自然人结算金额",
            prop: "doctorAmount",
            type: "input",
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    ids() {
      let ids = [];
      this.selectionDoctorList.forEach((ele) => {
        ids.push(ele.doctorId);
      });
      return ids.join(",");
    },
  },
  created() {
    this.id = this.$route.params.id;
    this.init();
  },
  methods: {
    toOrderdoctorDetail(id) {
      this.$router.push({
        path: `/orderdoctor/detail/${id}`,
      });
    },
    previewOrder(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    previewPDF(row) {
      this.dialogVisiblePdf = true;
      this.serviceManagerReport = row.file;
    },
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    init() {
      this.getDetail();
    },
    getDetail() {
      this.loading = true;
      this.loading = true;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          let data = res.data;
          this.form = data.data;
          this.activityData = data.data.orderProductStatistics
            ? data.data.orderProductStatistics
            : [];
          this.orderData = data.data.orderDoctorList
            ? data.data.orderDoctorList
            : [];
          this.loading = false;
          this.loading = false;
        }
      });
    },
    //活动汇总
    refreshChangeActivity() {
      this.getDetail();
    },
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}
.sub-button {
  display: flex;
  justify-content: center;
}
.button {
  position: absolute;
  right: 40px;
  top: -2px;
  z-index: 99;
}
</style>
