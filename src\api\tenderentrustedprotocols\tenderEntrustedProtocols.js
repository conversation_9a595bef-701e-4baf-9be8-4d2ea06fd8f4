import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-ts/tenderEntrustedProtocols/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-ts/tenderEntrustedProtocols/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-ts/tenderEntrustedProtocols/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-ts/tenderEntrustedProtocols/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-ts/tenderEntrustedProtocols/submit',
    method: 'post',
    data: row
  })
}

// 查询预览协议文件
export const getProtocolFile = (protocolId) => {
  return request({
    url:'/api/blade-ts/tenderEntrustedProtocols/getProtocolFile',
    method: 'get',
    params: {
      protocolId
    }
  })
}

