<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button v-if="permission.servicePaymentOrder_review&&row.confirmStatus==0" :size="option.size" type="text" @click="onAudit(row,1)" >确认推送</el-button
        >
      </template>
    </avue-crud>
    <el-dialog
      title="线下支付确认"
      :visible.sync="dialogVisible"
      append-to-body
      width="30%"
    >
      <avue-form
        ref="form"
        :option="option2"
        v-model="payAffirmForm"
        @submit="submit"
      ></avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  submitReview,
} from "@/api/servicepaymentorder/servicePaymentOrder";
import option from "@/const/servicepaymentorder/servicePaymentOrder";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      dialogVisible: false,
      option2: {
        submitBtn: true,
        emptyBtn: false,
        column: [
          {
            label: "流水号",
            prop: "paySerialNumber",
            type: "input",
            labelWidth: 150,
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入流水号",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },
      payAffirmForm: {
        paySerialNumber: "",
        id: "",
      },
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.servicePaymentOrder_add, false),
        viewBtn: this.vaildData(
          this.permission.servicePaymentOrder_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.servicePaymentOrder_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.servicePaymentOrder_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/servicepaymentorder/detail/${this.form.id}`,
        });
      } else if (["edit"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data.data;
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    onAudit(row, confirmStatus) {
      let _this = this;
      if (confirmStatus == 1) {
        this.$confirm("确定将该条数据通过?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let data = {
              confirmStatus: confirmStatus,
              id: row.id,
              confirmResult: "同意",
            };
            return submitReview(data);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      } else {
        _this.$DialogForm.show({
          title: "审核",
          width: "30%",
          menuPosition: "right",
          option: this.dialogOption,
          beforeClose: (done) => {
            setTimeout(() => {
              done();
            }, 100);
          },
          callback: (res) => {
            res.data.confirmStatus = confirmStatus;
            res.data.id = _this.id;
            submitReview(res.data).then(
              () => {
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
                res.close();
              },
              (error) => {
                res.done();
                console.log(error);
              }
            );
          },
        });
      }
    },
  },
};
</script>

<style></style>
