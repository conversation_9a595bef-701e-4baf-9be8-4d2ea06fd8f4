import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-clinicalResearchProject/clinicalResearchProject/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-clinicalResearchProject/clinicalResearchProject/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-clinicalResearchProject/clinicalResearchProject/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-clinicalResearchProject/clinicalResearchProject/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-clinicalResearchProject/clinicalResearchProject/submit',
    method: 'post',
    data: row
  })
}

export const stopProject = (id, status) => {
  return request({
    url: '/api/blade-clinicalResearchProject/clinicalResearchProject/enable',
    method: "get",
    params: {
      id,
      status,
    },
  })
}


export const confirmProject = (id, status) => {
  return request({
    url: '/api/blade-clinicalResearchProject/clinicalResearchProject/confirmTemplate',
    method: "get",
    params: {
      id,
      status,
    },
  })
}
