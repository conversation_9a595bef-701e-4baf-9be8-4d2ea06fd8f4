import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (EntrustedCompanyId) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/detail",
    method: "get",
    params: {
      EntrustedCompanyId,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/submit",
    method: "post",
    data: row,
  });
};
export const updateEntrustedCompany = (row) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/updateEntrustedCompany",
    method: "post",
    data: row,
  });
};

// 开通电子签章
export const regEsignAccount = (id) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/regEsignAccount",
    method: "get",
    params: {
      id,
    },
  });
};
// 申请电子签章
export const regEsignSeal = (id, hText) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/regEsignSeal",
    method: "get",
    params: {
      id,
      hText,
    },
  });
};
// 启用停用电子签章
export const stopEsignAccount = (id, status) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/stopEsignAccount",
    method: "get",
    params: {
      id,
      status,
    },
  });
};
//审核状态 3 审核通过 4审核不通过
export const audit = (row) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/audit",
    method: "post",
    data: row,
  });
};
// 停用启用切换
export const enable = (id) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/enable",
    method: "post",
    params: {
      id,
    },
  });
};

//新增企业会员
export const addEntrustedCompany = (row) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/addEntrustedCompany",
    method: "post",
    data: row,
  });
};

// 获取企业会员合作关系数据
export const getEntrustedRelList = (entrustedId) => {
  return request({
    url: "/api/blade-csc/entrustedThirdRel/getEntrustedRelList",
    method: "get",
    params: {
      entrustedId,
    },
  });
};

// 终止合作
export const updateAuditStatus = (entrustedThirdRelId) => {
  return request({
    url: "/api/blade-csc/entrustedThirdRel/updateAuditStatus",
    method: "post",
    params: {
      entrustedThirdRelId,
    },
  });
};
// 重新生成会员协议
export const generateProtocol = (EntrustedCompanyId) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/generateProtocol",
    method: "get",
    params: {
      EntrustedCompanyId,
    },
  });
};
//天眼查查询
export const getTycBaseInfo = (name) => {
  return request({
    url: "/api/blade-csc/entrustedCompany/getTycBaseInfo",
    method: "get",
    params: {
      name,
    },
  });
};
