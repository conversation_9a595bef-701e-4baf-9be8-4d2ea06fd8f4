<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #fileUrl="scope">
        <el-tag v-if="scope.row.fileUrl" @click="download(scope.row.fileUrl)"
          >点击下载</el-tag
        >
      </template>

      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button
          type="text"
          @click="accountCheckingResult(row)"
          v-if="permission.reconciliationInfo_delete && !row.fileUrl"
          >生成对账单文件
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  update,
  remove,
  accountCheckingResult,
  submitReconciliationInfo,
} from "@/api/reconciliationInfo/reconciliationInfo";
import { mapGetters } from "vuex";

export default {
  data() {
    var validateBegdat = (rule, value, callback) => {
      console.log("开始日期");
      if (this.form.enddat) {
        let startTime = Date.parse(value);
        let endTime = Date.parse(this.form.enddat);

        if (startTime > endTime) {
          callback(new Error("开始时间不能大于结束时间!"));
        } else {
          callback();
        }
      }
    };
    var validateEnddat = (rule, value, callback) => {
      if (this.form.begdat) {
        let startTime = Date.parse(this.form.begdat);
        let endTime = Date.parse(value);
        if (startTime > endTime) {
          callback(new Error("开始时间不能大于结束时间!"));
        } else {
          callback();
        }
      }
    };
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        selection: true,
        addBtnText: "查询代发明细对账单",
        editBtn: false,
        dialogClickModal: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        addBtnIcon: " ",
        cancelBtnIcon: " ",
        saveBtnIcon: " ",
        updateBtnIcon: " ",
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "开始日期",
            prop: "begdat",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择开始日期",
                trigger: "blur",
              },
              { required: true, validator: validateBegdat, trigger: "blur" },
            ],
          },
          {
            label: "结束日期",
            prop: "enddat",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() > Date.now() - 24 * 60 * 60 * 1000;
              },
            },
            rules: [
              {
                required: true,
                message: "请选择结束日期",
                trigger: "blur",
              },
              { required: true, validator: validateEnddat, trigger: "blur" },
            ],
          },
          {
            label: "业务参考号",
            prop: "ptyref",
            type: "input",
          },
          {
            label: "文件获取地址",
            prop: "fileUrl",
            type: "input",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "文件生成时间",
            prop: "fintim",
            type: "date",
            addDisplay: false,
            editDisplay: false,
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "总笔数",
            prop: "total",
            type: "input",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "查询标记",
            prop: "begidx",
            type: "input",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "打印任务编号",
            prop: "printid",
            type: "input",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "创建人",
            prop: "createUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建部门",
            prop: "createDept",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建时间",
            prop: "createTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改人",
            prop: "updateUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改时间",
            prop: "updateTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "是否已删除",
            prop: "isDeleted",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "版本控制",
            prop: "version",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
        ],
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.reconciliationInfo_add, false),
        viewBtn: this.vaildData(this.permission.reconciliationInfo_view, false),
        delBtn: this.vaildData(
          this.permission.reconciliationInfo_delete,
          false
        ),
        editBtn: this.vaildData(this.permission.reconciliationInfo_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    accountCheckingResult(row) {
      accountCheckingResult(row.printid).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "生成成功,请点击下载!",
        });
      });
    },
    download(url) {
      window.open(url);
    },
    rowSave(row, done, loading) {
      submitReconciliationInfo(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
