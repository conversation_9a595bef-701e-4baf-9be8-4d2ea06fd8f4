import { setStore, getStore, removeStore } from "@/util/store";
import website from "@/config/website";

const common = {
  state: {
    language: getStore({ name: "language" }) || "zh",
    isCollapse: false,
    isFullScren: false,
    isMenu: true,
    isShade: false,
    screen: -1,
    isLock: getStore({ name: "isLock" }) || false,
    showTag: true,
    showDebug: true,
    showCollapse: true,
    showSearch: true,
    showLock: true,
    showFullScren: true,
    showTheme: true,
    showMenu: true,
    showColor: true,
    colorName: getStore({ name: "colorName" }) || "#9AD94E",
    themeName: getStore({ name: "themeName" }) || "theme-white",
    lockPasswd: getStore({ name: "lockPasswd" }) || "",
    website: website,
    userFace: getStore({ name: "userFace" }) || {},
    createEvidenceFileStatus:
      getStore({ name: "createEvidenceFileStatus" }) || 2,
  },
  mutations: {
    SET_LANGUAGE: (state, language) => {
      state.language = language;
      setStore({
        name: "language",
        content: state.language,
      });
    },
    SET_SHADE: (state, active) => {
      state.isShade = active;
    },
    SET_COLLAPSE: (state) => {
      state.isCollapse = !state.isCollapse;
    },
    SET_FULLSCREN: (state) => {
      state.isFullScren = !state.isFullScren;
    },
    SET_IS_MENU: (state, menu) => {
      state.isMenu = menu;
    },
    SET_LOCK: (state) => {
      state.isLock = true;
      setStore({
        name: "isLock",
        content: state.isLock,
        type: "session",
      });
    },
    SET_SCREEN: (state, screen) => {
      state.screen = screen;
    },
    SET_COLOR_NAME: (state, colorName) => {
      state.colorName = colorName;
      setStore({
        name: "colorName",
        content: state.colorName,
      });
    },
    SET_THEME_NAME: (state, themeName) => {
      state.themeName = themeName;
      setStore({
        name: "themeName",
        content: state.themeName,
      });
    },
    SET_LOCK_PASSWD: (state, lockPasswd) => {
      state.lockPasswd = lockPasswd;
      setStore({
        name: "lockPasswd",
        content: state.lockPasswd,
        type: "session",
      });
    },
    SET_USER_FACE: (state, userFace) => {
      state.userFace = userFace;
      setStore({
        name: "userFace",
        content: state.userFace,
      });
    },
    //createEvidenceFileStatus
    SET_CREATE_EVIDENCE_FILE_STATUS: (state, createEvidenceFileStatus) => {
      state.createEvidenceFileStatus = createEvidenceFileStatus;
      setStore({
        name: "createEvidenceFileStatus",
        content: state.createEvidenceFileStatus,
      });
    },
    CLEAR_LOCK: (state) => {
      state.isLock = false;
      state.lockPasswd = "";
      removeStore({
        name: "lockPasswd",
        type: "session",
      });
      removeStore({
        name: "isLock",
        type: "session",
      });
    },
  },
};
export default common;
