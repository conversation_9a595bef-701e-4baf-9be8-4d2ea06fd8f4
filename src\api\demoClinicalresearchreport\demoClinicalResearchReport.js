import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchReport/list",
    method: "post",
    data: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchReport/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchReport/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchReport/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchReport/submit",
    method: "post",
    data: row,
  });
};

export const audit = (row) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchReport/audit",
    method: "post",
    data: row,
  });
};

export const batchAudit = (data) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchReport/audit-list",
    method: "post",
    data: data,
  });
};
