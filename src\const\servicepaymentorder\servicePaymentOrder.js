export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  labelWidth: 150,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  viewBtnIcon: " ",
  viewBtnText: "查看支付明细",
  dialogClickModal: false,
  searchLabelWidth: "120",
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "支付单编码",
      prop: "code",
      type: "input",
    },
    {
      label: "客户名称",
      prop: "enterpriseName",
      type: "input",

      search: true,
      rules: [
        {
          required: true,
          message: "请输入客户名称",
          trigger: "blur",
        },
      ],
    },
    {
      label: "企业结算单编号",
      prop: "settlementCode",
      type: "input",
      search: true,
    },
    {
      label: "企业结算单名称",
      prop: "settlementName",
      type: "input",
    },
    {
      label: "企业发票单编号",
      prop: "invoiceCode",
      type: "input",
      hide: true,
    },
    {
      label: "企业发票单名称",
      prop: "invoiceName",
      type: "input",
      hide: true,
    },
    {
      label: "订单总金额",
      prop: "settlementTotalAmount",
      type: "input",
    },

    {
      label: "推送状态",
      prop: "confirmStatus",
      type: "select",
      dicData: [
        {
          label: "待推送",
          value: 0,
        },
        {
          label: "已推送",
          value: 1,
        },
      ],
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
