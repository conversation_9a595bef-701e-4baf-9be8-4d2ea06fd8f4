<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      <!--      <div v-if="form.planStatus == 1">-->
      <!--        SFE同步不需要复审 复审结果通过SFE核报完成-->
      <div v-if="false">
        <div class="button">
          <el-button
            :loading="submitAuditStatus"
            v-if="
              permission.meeting_submitAudit &&
              (form.submitType == 1 || form.submitType == 3)
            "
            type="success"
            @click="submitAudit()"
            >提交审核</el-button
          >
          <!-- 初审 -->
          <div
            v-if="
              permission.meeting_approval &&
              form.submitType == 2 &&
              form.approvalBusinessType == 1
            "
          >
            <el-button
              v-if="form.approvalStatus == 0"
              type="success"
              @click="onAudit(1)"
              >通过</el-button
            >
            <el-button
              v-if="form.approvalStatus == 0"
              type="danger"
              @click="onAudit(2)"
              >驳回</el-button
            >
          </div>
          <!-- 复审 -->
          <div
            v-if="
              permission.meeting_confirm &&
              form.submitType == 2 &&
              form.approvalBusinessType == 2
            "
          >
            <el-button
              v-if="form.confirmStatus == 0"
              type="success"
              @click="onAudit(1)"
              >通过</el-button
            >
            <el-button
              v-if="form.confirmStatus == 0"
              type="danger"
              @click="onAudit(2)"
              >驳回</el-button
            >
          </div>
        </div>
      </div>
    </basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <avue-form
            ref="form"
            v-if="!validatenull(form)"
            :option="option"
            v-model="form"
            @submit="submit"
          >
          </avue-form>
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>
        <el-tab-pane label="活动讲者" name="2">
          <!-- 邀请讲者 -->
          <avue-crud
            :page.sync="meetingDoctorPage"
            :option="meetingDoctorOption"
            :data="meetingDoctorList"
            @current-change="currentChangeMeetingDoctor"
            @size-change="sizeChangeMeetingDoctor"
            @refresh-change="refreshChangeMeetingDoctor"
            ref="meetingDoctor"
          >
            <template slot="doctorName" slot-scope="{ row }">
              <div class="to-view" @click="toDoctorView(row)">
                <a readonly>
                  {{ row.doctorName }}
                </a>
              </div>
            </template>
            <template slot="coursework" slot-scope="{ row }">
              <div style="cursor: pointer" v-if="row.coursework">
                <el-tag @click="previewCoursework(row.coursework)"
                  >预览课件</el-tag
                >
              </div>
              <div v-else>无</div>
            </template>
            <template slot="doctorAgreement" slot-scope="{ row }">
              <div style="cursor: pointer" v-if="row.doctorAgreement">
                <el-tag @click="previewPDF(row.doctorAgreement)"
                  >预览合同</el-tag
                >
              </div>
              <div v-else>无</div>
            </template>
            <template slot="agreementStatus" slot-scope="{ row }">
              <el-tag v-if="row.agreementStatus == 0" size="small"
                >待签署</el-tag
              >
              <el-tag
                v-if="row.agreementStatus == 1"
                type="success"
                size="small"
                >同意</el-tag
              >
              <el-tag v-if="row.agreementStatus == 2" type="danger" size="small"
                >拒绝</el-tag
              >
              <el-tag v-if="row.agreementStatus == 3" type="danger" size="small"
                >作废</el-tag
              >
            </template>
          </avue-crud>
        </el-tab-pane>
        <!-- 议程信息 -->
        <!-- <el-tab-pane label="议程信息" name="3">
          <avue-crud
            :page.sync="meetingAgendaPage"
            :option="meetingAgendaOption"
            :data="meetingAgendaList"
            @current-change="currentChangeMeetingAgenda"
            @size-change="sizeChangeMeetingAgenda"
            @refresh-change="refreshChangeMeetingAgenda"
            ref="meetingAgenda"
          >
          </avue-crud>
        </el-tab-pane> -->
        <el-tab-pane label="参会人员记录" name="4">
          <!-- 参会人员记录 -->

          <avue-crud
            :page.sync="meetingAttendPage"
            :option="meetingAttendOption"
            :data="meetingAttendList"
            @current-change="currentChangeMeetingAttend"
            @size-change="sizeChangeMeetingAttend"
            @refresh-change="refreshChangeMeetingAttend"
            ref="meetingAttend"
          >
          </avue-crud>
        </el-tab-pane>
        <!-- 会议费用 -->
        <!-- <el-tab-pane label="会议费用" name="5">
          <avue-crud
            :page.sync="meetingCostPage"
            :option="meetingCostOption"
            :data="meetingCostList"
            :permission="meetingCostPermissionList"
            @row-update="rowUpdateMeetingCost"
            @row-save="rowSaveMeetingCost"
            @row-del="rowDelMeetingCost"
            @current-change="currentChangeMeetingCost"
            @size-change="sizeChangeMeetingCost"
            @refresh-change="refreshChangeMeetingCost"
            ref="meetingCost"
          >
          </avue-crud>
        </el-tab-pane> -->
        <el-tab-pane label="会议凭证材料" name="6">
          <!-- 会议附件 -->
          <avue-crud
            :before-open="beforeOpen"
            :page.sync="meetingEvidencePage"
            :option="meetingEvidenceOption"
            :data="meetingEvidenceList"
            @current-change="currentChangeMeetingEvidence"
            @size-change="sizeChangeMeetingEvidence"
            @refresh-change="refreshChangeMeetingEvidence"
            @row-save="rowSaveMeetingEvidence"
            @row-del="rowDelMeetingEvidence"
            :permission="permissionList"
            ref="meetingEvidence"
          >
            <!--            材料SFE传输-->
            <!--            <template slot-scope="{ type, size, row, index }" slot="menu">-->
            <!--              <el-button-->
            <!--                v-if="permission.meeting_meetingEvidence_view"-->
            <!--                :size="size"-->
            <!--                type="text"-->
            <!--                @click="toOpen(row.attachLink)"-->
            <!--                >预览-->
            <!--              </el-button>-->
            <!--              <el-button-->
            <!--                v-if="-->
            <!--                  permission.meeting_meetingEvidence_delete &&-->
            <!--                  row.name.indexOf('_录制') == -1-->
            <!--                "-->
            <!--                :size="size"-->
            <!--                type="text"-->
            <!--                @click="rowDelMeetingEvidence(row)"-->
            <!--                >删除-->
            <!--              </el-button>-->
            <!--            </template>-->
          </avue-crud>
        </el-tab-pane>
        <!--        <el-tab-pane label="腾讯会议预约" name="7">-->
        <!--          &lt;!&ndash; 腾讯会议信息 &ndash;&gt;-->
        <!--          <avue-crud-->
        <!--            :page.sync="meetingTencentPage"-->
        <!--            :option="meetingTencentOption"-->
        <!--            :data="meetingTencentList"-->
        <!--            @current-change="currentChangeMeetingTencent"-->
        <!--            @size-change="sizeChangeMeetingTencent"-->
        <!--            @refresh-change="refreshChangeMeetingTencent"-->
        <!--            ref="meetingTencent"-->
        <!--          >-->
        <!--            <template slot="menuLeft">-->
        <!--              <el-button-->
        <!--                v-if="-->
        <!--                  permission.meeting_meetingTencent_createMeeting &&-->
        <!--                  meetingTencentOption.menu &&-->
        <!--                  form.format == 1-->
        <!--                "-->
        <!--                type="primary"-->
        <!--                size="small"-->
        <!--                @click="addAppoint"-->
        <!--                >申请会议室-->
        <!--              </el-button>-->
        <!--            </template>-->
        <!--            <template slot-scope="{ type, size, row, index }" slot="menu">-->
        <!--              <el-button-->
        <!--                v-if="-->
        <!--                  permission.meeting_meetingTencent_cancelMeeting &&-->
        <!--                  row.meetingStatus == 1-->
        <!--                "-->
        <!--                type="text"-->
        <!--                @click="cancelMeeting()"-->
        <!--                >取消会议</el-button-->
        <!--              >-->
        <!--              <el-button-->
        <!--                v-if="-->
        <!--                  permission.meeting_meetingTencent_createMeetingQRCode &&-->
        <!--                  row.meetingStatus == 1-->
        <!--                "-->
        <!--                type="text"-->
        <!--                @click="createMeetingQRCode(row)"-->
        <!--                >参会链接分享</el-button-->
        <!--              >-->
        <!--            </template>-->
        <!--          </avue-crud>-->
        <!--        </el-tab-pane>-->
      </el-tabs>
    </basic-container>
    <el-dialog
      title="预约"
      append-to-body
      :visible.sync="dialogVisible"
      width="50%"
      :before-close="closeAppoint"
    >
      <el-form label-width="120px" :model="dialogForm" ref="ruleForm">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="会议日期：">
              <el-date-picker
                v-model="dialogForm.meetingDate"
                @change="changMeetingDate"
                type="date"
                placeholder="选择会议日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会议主题：">
              <el-input
                placeholder="请输入会议主题"
                v-model="dialogForm.subject"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="margin: 20px">
          <div
            style="
              display: flex;
              margin: 20px 50px;
              font-size: 18px;
              justify-content: space-between;
            "
          >
            <div class="date-title">
              <div
                style="
                  background-color: #c8c9cc;
                  width: 32px;
                  height: 16px;
                  margin-right: 10px;
                "
              ></div>
              <div>已过期</div>
            </div>
            <div class="date-title">
              <div
                style="
                  background-color: #ffa4a4;
                  width: 32px;
                  height: 16px;
                  margin-right: 10px;
                "
              ></div>
              <div>已约满</div>
            </div>
            <div class="date-title">
              <div
                style="
                  background-color: #1d90a2;
                  width: 32px;
                  height: 16px;
                  margin-right: 10px;
                "
              ></div>
              <div>当前预约</div>
            </div>
          </div>
          <div style="min-height: 250px" class="button_wrap">
            <div
              class="preinstallItem"
              :class="
                item.available == 0
                  ? 'selectPreinstallItem0'
                  : item.available == 1
                  ? 'selectPreinstallItem1'
                  : item.available == 2
                  ? 'selectPreinstallItem2'
                  : item.available == 3
                  ? 'selectPreinstallItem3'
                  : ''
              "
              v-for="(item, index) in timeArr"
              :key="index"
              @click="changTime(item, index)"
            >
              <div>
                {{ item.timePoint }}
              </div>
            </div>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeAppoint">取消</el-button>
        <el-button
          :loading="dialogButtonLoading"
          type="primary"
          @click="saveAppoint"
          style="margin-left: 20px"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="参会链接分享"
      append-to-body
      :visible.sync="viewDialogVisible"
      width="40%"
    >
      <div
        style="
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        "
      >
        <div>
          <div class="qr-img">
            <img :src="imageUrl" alt="" />
          </div>
          <div style="display: flex">
            <div style="width: 280px">
              <el-input
                disabled
                v-model="kyMeetingUrl"
                placeholder="参会链接"
              ></el-input>
            </div>
            <div>
              <div class="copy" @click="copy(kyMeetingUrl)">复制链接</div>
            </div>
          </div>
        </div>
        <div style="margin-top: 30px; width: 85%">
          <div>
            <div>
              温馨提示：客户可通过扫描二维码或点击分享的入会链接参与线上会议。
              <div>
                注意：线上会议使用腾讯会议客户端，支持移动端、网页端或微信小程序端参会，请提前下载准备好可使用的客户端软件。线上会议到达预约的开始时间后才可以正常加入，如有其他问题请联系平台业务人员
              </div>
            </div>
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="downloadImage(imageUrl)"
          >下载二维码</el-button
        >
      </span>
    </el-dialog>
    <!-- 新增凭证 -->
    <el-dialog
      title="新增"
      @close="addEvidenceForm = {}"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="addDialogVisible"
      width="60%"
    >
      <avue-form
        v-if="addDialogVisible"
        ref="addEvidence"
        :upload-after="uploadAfter"
        :option="addEvidenceOption"
        v-model="addEvidenceForm"
      >
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取 消</el-button>
        <el-button
          :loading="addEvidenceLoading"
          type="primary"
          @click="addEvidence()"
          >确定</el-button
        >
      </span>
    </el-dialog>

    <!-- 审核驳回 -->
    <el-dialog
      title="驳回"
      @close="auditForm = {}"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="auditDialogVisible"
      width="30%"
    >
      <avue-form
        v-if="auditDialogVisible"
        ref="auditForm"
        :option="auditOption"
        v-model="auditForm"
      >
        <template slot-scope="{ disabled, size }" slot="quickReplyList">
          <div class="quickReplyList">
            <el-tag
              style="cursor: pointer"
              :key="tag.id"
              v-for="tag in quickReplyList"
              :disable-transitions="false"
              @click="clickQuickReply(tag)"
            >
              {{ tag.content }}
            </el-tag>
          </div>
        </template>
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取 消</el-button>
        <el-button :loading="auditLoading" type="primary" @click="auditClick()"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDetail, submitAudit, submitRecheck } from "@/api/meeting/meeting";
//邀请讲者
import { getList as getMeetingDoctorList } from "@/api/meetingdoctor/meetingDoctor";
//参会人员记录
import { getList as getMeetingAttendList } from "@/api/meetingattend/meetingAttend";
//会议费用
import {
  add as meetingCostAdd,
  getList as getMeetingCostList,
  remove as removeMeetingCost,
  update as updateMeetingCost,
} from "@/api/meetingcost/meetingCost";
//会议附件
import {
  add as meetingEvidenceAdd,
  getList as getMeetingEvidenceList,
  remove as removeMeetingEvidence,
  submitList,
} from "@/api/meetingevidence/meetingEvidence";
//腾讯会议
import {
  appointed,
  cancelMeeting,
  createMeeting,
  createMeetingQRCode,
  getList as getMeetingTencentList,
} from "@/api/meetingtencent/meetingTencent";
//议程信息
import { getList as getMeetingAgendaList } from "@/api/meetingagenda/meetingAgenda";
import { mapGetters } from "vuex";
import dayjs from "dayjs";
import { downloadFileBlob } from "@/util/util";
import { Base64 } from "js-base64";
import { projectQuickReplyList } from "@/api/sysprojectQuickReply/projectQuickReply";

export default {
  data() {
    var checkNum = (rule, value, callback) => {
      if (isNaN(value)) {
        callback(new Error("请输入数字值"));
      } else {
        callback();
      }
    };
    return {
      //查看
      viewDialogVisible: false,
      imageUrl: "",
      activeName: "1",
      kyMeetingUrl: "",
      //审核弹窗
      quickReplyList: [],
      auditLoading: false,
      auditDialogVisible: false,
      auditForm: {},
      auditOption: {
        submitText: "完成",
        span: 24,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "验收说明",
            prop: "remark",
            type: "textarea",
            maxlength: 500,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入验收说明",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "快捷回复",
            prop: "quickReplyList",
            type: "input",
          },
        ],
      },

      //end
      id: "",
      form: {},
      option: {
        searchShowBtn: false,
        columnBtn: false,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        column: [
          {
            label: "会议编号",
            prop: "code",
            type: "input",
          },
          {
            label: "会议名称",
            prop: "name",
            type: "input",
          },
          {
            label: "会议类型",
            prop: "type",
            type: "input",
          },

          {
            label: "举办形式",
            prop: "format",
            type: "select",
            dicData: [
              {
                label: "线上",
                value: 1,
              },
              {
                label: "线下",
                value: 2,
              },
            ],
          },
          {
            label: "会议目的",
            prop: "purpose",
            type: "input",
          },
          {
            label: "预算金额",
            prop: "budgetAmount",
            type: "input",
          },

          {
            label: "计划开始时间",
            prop: "invitationStartDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "计划结束时间",
            prop: "invitationEndDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "活动发布时间",
            prop: "releaseTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "活动超期关闭时间",
            prop: "overdueCloseTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "会议状态",
            prop: "meetingStatus",
            type: "select",
            dicData: [
              {
                label: "未开始",
                value: 1,
              },
              {
                label: "进行中",
                value: 2,
              },
              {
                label: "已结束",
                value: 3,
              },
              {
                label: "已变更",
                value: 4,
              },
              {
                label: "已取消",
                value: 5,
              },
              {
                label: "已否决",
                value: 6,
              },
            ],
          },
          {
            label: "会议简介",
            prop: "description",
            type: "input",
          },
          {
            label: "会议主办方",
            prop: "organizerName",
            type: "input",
          },
          {
            label: "客户名称",
            prop: "entrustedCompanyName",
            type: "input",
          },
          {
            label: "活动负责人",
            prop: "baseEmployeeName",
            type: "input",
          },
          {
            label: "负责人部门",
            prop: "baseDepartmentName",
            type: "input",
          },
          {
            label: "产品名称",
            prop: "baseProductName",
            type: "input",
          },
          {
            label: "产品线",
            prop: "baseProductLine",
            type: "input",
          },

          // {
          //   label: "会议地址-省",
          //   prop: "provinceCode",
          //   type: "input",
          // },
          // {
          //   label: "会议地址-市",
          //   prop: "cityCode",
          //   type: "input",
          // },
          {
            label: "会议详细地址",
            prop: "address",
            type: "input",
          },

          {
            label: "会议总结",
            prop: "summary",
            type: "input",
          },
          // {
          //   label: "初审时间",
          //   prop: "approvalDate",
          //   type: "datetime",
          //   format: "yyyy-MM-dd HH:mm:ss",
          //   valueFormat: "yyyy-MM-dd HH:mm:ss",
          // },
          // {
          //   label: "初审状态",
          //   prop: "approvalStatus",
          //   type: "select",
          //   dicData: [
          //     {
          //       label: "待审核",
          //       value: 0,
          //     },
          //     {
          //       label: "通过",
          //       value: 1,
          //     },
          //     {
          //       label: "驳回",
          //       value: 2,
          //     },
          //   ],
          // },
          // {
          //   label: "初审说明",
          //   prop: "approvalRemark",
          //   type: "input",
          // },
          // {
          //   label: "初审人",
          //   prop: "approvalOfficer",
          //   type: "input",
          // },
          {
            label: "审核时间",
            prop: "confirmDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "审核状态",
            prop: "confirmStatus",
            type: "select",
            dicData: [
              {
                label: "待验收",
                value: 0,
              },
              {
                label: "通过",
                value: 1,
              },
              {
                label: "驳回",
                value: 2,
              },
            ],
          },
          // {
          //   label: "审核说明",
          //   prop: "confirmResult",
          //   type: "input",
          // },
          {
            label: "审核人",
            prop: "confirmer",
            type: "input",
          },
        ],
      },
      //邀请讲者信息
      meetingDoctorPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      meetingDoctorOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "部门",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职称",
            prop: "professional",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "讲者级别",
            prop: "doctorLevel",
            type: "input",
          },
          {
            label: "讲者角色",
            prop: "identityRole",
            type: "select",
            dicData: [
              {
                label: "主讲者",
                value: 1,
              },
              {
                label: "主席或主持人",
                value: 2,
              },
              {
                label: "点评人",
                value: 3,
              },
            ],
          },
          {
            label: "合同费用",
            prop: "doctorFee",
            type: "input",
          },
          {
            label: "讲者课件",
            prop: "coursework",
            type: "input",
          },
          {
            label: "签署状态",
            prop: "agreementStatus",
            type: "select",
            dicData: [
              {
                label: "同意",
                value: 1,
              },
              {
                label: "拒绝",
                value: 2,
              },
              {
                label: "作废",
                value: 3,
              },
            ],
          },
          {
            label: "签署时间",
            prop: "agreementTime",
            type: "input",
          },
          {
            label: "合同协议",
            prop: "doctorAgreement",
            type: "input",
          },
        ],
      },
      meetingDoctorList: [],
      //参会人员记录
      meetingAttendPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      meetingAttendOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "参会角色",
            prop: "roleType",
            type: "select",
            dicData: [
              {
                label: "讲者",
                value: 0,
              },
              {
                label: "外部参会客户",
                value: 1,
              },
              {
                label: "内部参会人员",
                value: 2,
              },
            ],
          },
          {
            label: "姓名",
            prop: "name",
            type: "input",
          },
          {
            label: "单位",
            prop: "organization",
            type: "input",
          },
          {
            label: "部门",
            prop: "department",
            type: "input",
          },
          {
            label: "联系电话",
            prop: "phone",
            type: "input",
          },
          {
            label: "签到状态",
            prop: "signInStatus",
            type: "select",
            dicData: [
              {
                label: "未签到",
                value: 1,
              },
              {
                label: "已签到",
                value: 2,
              },
            ],
          },
          {
            label: "签到时间",
            prop: "signInTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "签到位置",
            prop: "signInLocation",
            type: "input",
          },

          {
            label: "首次入会时间",
            prop: "firstJoinTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "最后离开时间",
            prop: "leaveTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "参会时长(分钟)",
            prop: "duration",
            type: "input",
          },
        ],
      },
      meetingAttendList: [],
      //会议费用
      meetingCostPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      meetingCostOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        // viewBtn: true,
        addBtn: true,
        addBtnIcon: " ",
        editBtn: true,
        delBtn: true,
        refreshBtn: true,

        menu: true,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "费用项目",
            prop: "feeType",
            type: "select",
            dicData: [
              {
                label: "场租费用",
                value: 1,
              },
              {
                label: "参会费用",
                value: 2,
              },
              {
                label: "礼品费用",
                value: 3,
              },
              {
                label: "餐饮费用",
                value: 4,
              },
              {
                label: "差旅费用",
                value: 5,
              },
              {
                label: "其他费用",
                value: 6,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择费用项目",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "费用名称",
            prop: "feeName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入费用名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "费用单价",
            prop: "unitPrice",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入费用单价",
                trigger: ["blur", "change"],
              },
              {
                validator: checkNum,
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "费用数量",
            prop: "quantity",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入费用数量",
                trigger: ["blur", "change"],
              },
              {
                validator: checkNum,
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "费用总金额",
            prop: "totalAmount",
            type: "input",
            addDisplay: false,
            editDisplay: false,
          },
        ],
      },
      meetingCostList: [],
      //会议附件
      meetingEvidencePage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      // 新增会议凭证
      addEvidenceLoading: false,
      addEvidenceUrl: [],
      addEvidenceIndex: [],
      addDialogVisible: false,
      addEvidenceForm: {},
      addEvidenceOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "凭证材料",
            prop: "attachLink",
            listType: "picture-card",
            span: 24,
            // accept: "image/png, image/jpeg, audio/mp4, video/mp4",
            type: "upload",
            action: "/api/blade-resource/oss/endpoint/put-file",
            propsHttp: {
              res: "data",
              url: "link",
            },
            rules: [
              {
                required: true,
                message: "请上传文件",
                trigger: ["blur", "change"],
              },
            ],
            row: true,
          },
        ],
      },
      meetingEvidenceOption: {
        grid: true,
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        addBtnIcon: " ",
        refreshBtn: true,
        viewBtn: false,
        delBtn: false,
        menu: true,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "凭证名称",
            prop: "name",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入凭证名称",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "凭证材料",
            prop: "attachLink",
            listType: "picture-card",
            // accept: "image/png, image/jpeg, audio/mp4, video/mp4",
            type: "upload",
            action: "/api/blade-resource/oss/endpoint/put-file",
            propsHttp: {
              res: "data",
              url: "link",
            },
            rules: [
              {
                required: true,
                message: "请上传文件",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "凭证形式",
            prop: "attachType",
            type: "select",
            dicData: [
              {
                label: "图片",
                value: 1,
              },
              {
                label: "视频",
                value: 2,
              },
              // {
              //   label: "文档",
              //   value: 3,
              // },
            ],
            addDisplay: false,
            rules: [
              {
                required: true,
                message: "请选择凭证形式",
                trigger: ["blur", "change"],
              },
            ],
            hide: true,
          },
        ],
      },
      meetingEvidenceList: [],
      //腾讯会议
      meetingTencentPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      meetingTencentOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: true,
        delBtn: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "会议主题",
            prop: "meetingName",
            type: "input",
          },

          // {
          //   label: "是否开启录制",
          //   prop: "attachType",
          //   type: "select",
          //   dicData: [
          //     {
          //       label: "不开启",
          //       value: 0,
          //     },
          //     {
          //       label: "开启",
          //       value: 1,
          //     },
          //   ],
          // },
          {
            label: "预约开始时间",
            prop: "startTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "预约结束时间",
            prop: "endTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          // {
          //   label: "是否允许成员在主持人进会前加入会议",
          //   prop: "settingAllowInBeforeHost",
          //   type: "select",
          //   dicData: [
          //     {
          //       label: "不允许",
          //       value: 0,
          //     },
          //     {
          //       label: "允许",
          //       value: 1,
          //     },
          //   ],
          // },
          {
            label: "会议主持人",
            prop: "name",
            type: "input",
          },
          {
            label: "预约状态",
            prop: "meetingStatus",
            type: "input",
            dicData: [
              {
                label: "生效中",
                value: 1,
              },
              {
                label: "已取消",
                value: 2,
              },
              {
                label: "已作废",
                value: 3,
              },
              {
                label: "已结束",
                value: 4,
              },
            ],
          },
        ],
      },
      meetingTencentList: [],

      //议程信息
      meetingAgendaPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      meetingAgendaOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "议程主题",
            prop: "title",
            type: "input",
          },
          // {
          //   label: "议程级别",
          //   prop: "level",
          //   type: "input",
          // },
          {
            label: "议程开始时间",
            prop: "startTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "议程结束时间",
            prop: "endTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "议程时长",
            prop: "duration",
            type: "input",
          },
          {
            label: "讲者姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "议程地点",
            prop: "address",
            type: "input",
          },
          {
            label: "议程顺序",
            prop: "agendaOrder",
            type: "input",
          },
        ],
      },
      meetingAgendaList: [],
      //申请会议室
      dialogVisible: false,
      dialogForm: { meetingDate: "", subject: "" },
      pickerOptions: {
        disabledDate: (time) => {
          return (
            time.getTime() < dayjs().valueOf() - 86400000 ||
            time.getTime() > dayjs().valueOf() + 60 * 60 * 1000 * 24 * 29
          );
        },
      },
      appointTimeArr: [], //预约选中时间数组
      //0:预约满 1:可以预约 2:已经过期
      timeArr: [],
      dialogButtonLoading: false,
      submitAuditStatus: false, //提交审核状态
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  watch: {
    "addEvidenceForm.attachLink"(value) {
      let missingIndexes = this.findMissingIndexes(this.addEvidenceUrl, value);
      if (missingIndexes.length > 0) {
        missingIndexes.forEach((item) => {
          delete this.addEvidenceForm[
            this.addEvidenceOption.column[item + 1].prop
          ];
          this.addEvidenceOption.column.splice(item + 1, 1); // 删除指定下标的元素
        });
        this.addEvidenceOption.column.map((item, index) => {
          if (index > 0) {
            item.label = "凭证名称" + index;
          }
          return item;
        });
        this.addEvidenceUrl = JSON.parse(JSON.stringify(value));
      }
    },
  },
  computed: {
    ...mapGetters(["permission"]),
    meetingCostPermissionList() {
      return {
        addBtn: this.vaildData(this.permission.meeting_meetingCost_add, false),
        viewBtn: this.vaildData(
          this.permission.meeting_meetingCost_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.meeting_meetingCost_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.meeting_meetingCost_edit,
          false
        ),
      };
    },
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.meeting_meetingEvidence_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.meeting_meetingEvidence_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.meeting_meetingEvidence_delete,
          false
        ),
        editBtn: false,
      };
    },
  },
  methods: {
    findMissingIndexes(arr1, arr2) {
      let missingIndexes = [];
      if (!arr1 || !arr2) {
        return missingIndexes;
      }
      for (let i = 0; i < arr1.length; i++) {
        if (!arr2.includes(arr1[i])) {
          missingIndexes.push(i);
        }
      }

      return missingIndexes;
    },
    // 会议室预约
    //1.点击预约时
    addAppoint() {
      this.dialogVisible = true;
      this.getAppointed(dayjs().format("YYYY-MM-DD"));
    },

    //2.获取时间数组
    getAppointed(date) {
      appointed(date, this.id).then((res) => {
        let list = res.data.data.list.map((item) => {
          item.timePoint = dayjs(item.timePoint).format("HH:mm:ss");
          return item;
        });
        this.timeArr = list;
        this.appointTimeArr = [];
        this.dialogForm = {
          meetingDate: date,
          subject: this.dialogForm.subject
            ? this.dialogForm.subject
            : res.data.data.subject,
        };
      });
    },

    //3.日期切换
    changMeetingDate(value) {
      this.getAppointed(value);
    },
    //4.选中时间点时，判断状态及改变状态
    changTime(val, index) {
      if (val.available == 0 || val.available == 2) {
        return;
      }
      if (this.timeArr[index].available == 3) {
        this.arrayCancel(this.timeArr);
      } else {
        if (this.appointTimeArr.length == 9) {
          this.$message({
            type: "error",
            message: "最多只能选择9个时间段!!!",
          });
          return;
        }
        this.timeArr[index].available = 3;
        this.appointTimeArr.push(index);
        this.appointTimeArr = this.appointTimeArr.sort(function (a, b) {
          return a - b;
        });
        if (
          this.appointTimeArr.length > 1 &&
          !this.isConsecutive(this.appointTimeArr)
        ) {
          if (
            !this.loopArray(
              this.timeArr,
              this.appointTimeArr[0],
              this.appointTimeArr[this.appointTimeArr.length - 1]
            )
          ) {
            this.$message.error("请选择连续的预约时间!!!");
            this.timeArr.map((item) => {
              if (item.available == 3) {
                item.available = 1;
              }
              return item;
            });
            this.appointTimeArr = [];
          } else {
            this.arrayModify(
              this.timeArr,
              this.appointTimeArr[0],
              this.appointTimeArr[this.appointTimeArr.length - 1]
            );
          }
        }
      }
    },
    //循环判断是否有已过期活已经占用
    loopArray(arr, start, end) {
      let flag = true;
      // 使用slice()获取从start到end的子数组，并使用forEach循环遍历
      arr.slice(start, end + 1).forEach(function (item) {
        if (item.available == 0 || item.available == 2) {
          flag = false;
        }
      });
      return flag;
    },
    //选择连续数据
    arrayModify(arr, start, end) {
      this.appointTimeArr = [];
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].available == 3) {
          arr[i].available = 1;
        }
      }
      if (end - start >= 9) {
        this.$message({
          type: "error",
          message: "最多只能选择9个时间段!!!",
        });
        end = start + 8;
      }
      for (let i = start; i < end + 1; i++) {
        arr[i].available = 3;
        this.appointTimeArr.push(i);
      }
    },
    //时间取消
    arrayCancel(arr) {
      this.appointTimeArr = [];
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].available == 3) {
          arr[i].available = 1;
        }
      }
    },
    //5.保存
    saveAppoint() {
      if (this.appointTimeArr.length > 9) {
        this.$message({
          type: "error",
          message: "最多只能选择9个时间段!!!",
        });
        return;
      }
      //保存时，开始时间点和结束时间点必须存在，即选中数组的长度为2，否则做出提示，并且将选中的下标对赢得时间点状态重置为0，即未选中
      if (this.isConsecutive(this.appointTimeArr)) {
        this.dialogButtonLoading = true;
        let startTime =
          this.dialogForm.meetingDate +
          " " +
          this.timeArr[this.appointTimeArr[0]].timePoint;
        startTime = dayjs(startTime).valueOf();
        let endTime = dayjs(
          this.dialogForm.meetingDate +
            " " +
            this.timeArr[this.appointTimeArr[this.appointTimeArr.length - 1]]
              .timePoint
        ).valueOf();
        let data = {
          startTime: Math.floor(startTime / 1000),
          endTime: Math.floor(endTime / 1000),
          id: this.id,
          subject: this.dialogForm.subject,
        };
        createMeeting(data).then(
          () => {
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.dialogVisible = false;
            this.dialogButtonLoading = false;
            this.getMeetingTencentList(this.meetingTencentPage);
          },
          (error) => {
            this.dialogButtonLoading = false;
            console.log(error);
          }
        );
      } else {
        this.$message.error("请选择连续的预约时间!!!");
      }
    },
    //是否连续
    isConsecutive(arr) {
      if (arr.length <= 1) {
        return false; // 数组长度小于等于1，无法判断连续性
      }
      arr.sort((a, b) => a - b); // 将数组排序
      for (let i = 1; i < arr.length; i++) {
        // 判断相邻两个数字是否相差1，若不是则返回false
        if (arr[i] - arr[i - 1] !== 1) {
          return false;
        }
      }

      return true; // 数组中的数字是连续的
    },
    //6.弹窗关闭
    closeAppoint() {
      this.dialogVisible = false;
    },
    //end
    previewPDF(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    previewCoursework(i) {
      if (i) {
        window.open(
          "http://1.94.42.192:8886/onlinePreview?url=" +
            encodeURIComponent(Base64.encode(i))
        );
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    handleClick(tabs) {
      if (tabs.name == 2) {
        //邀请讲者
        this.getMeetingDoctorList(this.meetingDoctorPage);
      } else if (tabs.name == 3) {
        //议程信息
        this.getMeetingAgendaList(this.meetingAgendaPage);
      } else if (tabs.name == 4) {
        //参会人员记录
        this.getMeetingAttendList(this.meetingAttendPage);
      } else if (tabs.name == 5) {
        //会议费用
        this.getMeetingCostList(this.meetingCostPage);
      } else if (tabs.name == 6) {
        //会议附件
        this.getMeetingEvidenceList(this.meetingEvidencePage);
      } else if (tabs.name == 7) {
        //腾讯会议
        this.getMeetingTencentList(this.meetingTencentPage);
      }
    },
    // 邀请讲者
    getMeetingDoctorList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingDoctorList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingDoctorPage.total = data.total;
        this.meetingDoctorList = data.records;
        this.loading = false;
        this.$nextTick(() => {
          if (!this.$refs.meetingDoctor.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.meetingDoctor.doLayout();
          }
        });
      });
    },
    refreshChangeMeetingDoctor() {
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    //分页
    currentChangeMeetingDoctor(currentPage) {
      this.meetingDoctorPage.currentPage = currentPage;
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    sizeChangeMeetingDoctor(pageSize) {
      this.meetingDoctorPage.pageSize = pageSize;
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    //end
    //参会人员记录
    getMeetingAttendList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingAttendList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingAttendPage.total = data.total;
        this.meetingAttendList = data.records;
        this.loading = false;
        this.$nextTick(() => {
          if (!this.$refs.meetingAttend.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.meetingAttend.doLayout();
          }
        });
      });
    },
    refreshChangeMeetingAttend() {
      this.getMeetingAttendList(this.meetingAttendPage);
    },
    //分页
    currentChangeMeetingAttend(currentPage) {
      this.meetingAttendPage.currentPage = currentPage;
      this.getMeetingAttendList(this.meetingAttendPage);
    },
    sizeChangeMeetingAttend(pageSize) {
      this.meetingAttendPage.pageSize = pageSize;
      this.getMeetingAttendList(this.meetingAttendPage);
    },
    //end
    //会议费用
    rowSaveMeetingCost(row, done, loading) {
      row.meetingId = this.id;
      meetingCostAdd(row).then(
        () => {
          this.getMeetingCostList(this.meetingCostPage);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowDelMeetingCost(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeMeetingCost(row.id);
        })
        .then(() => {
          this.getMeetingCostList(this.meetingCostPage);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowUpdateMeetingCost(row, index, done, loading) {
      updateMeetingCost(row).then(
        () => {
          this.getMeetingCostList(this.meetingCostPage);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    getMeetingCostList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingCostList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingCostPage.total = data.total;
        this.meetingCostList = data.records;
        this.loading = false;
        this.$nextTick(() => {
          if (!this.$refs.meetingCost.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.meetingCost.doLayout();
          }
        });
      });
    },
    refreshChangeMeetingCost() {
      this.getMeetingCostList(this.meetingCostPage);
    },
    //分页
    currentChangeMeetingCost(currentPage) {
      this.meetingCostPage.currentPage = currentPage;
      this.getMeetingCostList(this.meetingCostPage);
    },
    sizeChangeMeetingCost(pageSize) {
      this.meetingCostPage.pageSize = pageSize;
      this.getMeetingCostList(this.meetingCostPage);
    },
    //end
    //会议附件
    uploadAfter(res, done) {
      this.addEvidenceUrl.push(res.link);
      this.addEvidenceIndex.push(1);
      let key = "evidenceName" + this.addEvidenceIndex.length;
      this.addEvidenceOption.column.push({
        label: "凭证名称" + this.addEvidenceUrl.length,
        prop: key,
        type: "input",
        rules: [
          {
            required: true,
            message: "请输入凭证名称",
            trigger: ["blur", "change"],
          },
        ],
      });
      this.addEvidenceForm[key] = res.originalName;
      done();
    },
    beforeOpen(done, type) {
      if (type == "add") {
        this.addDialogVisible = true;
        this.addEvidenceUrl = [];
        this.addEvidenceIndex = [];
        this.addEvidenceOption = {
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "凭证材料",
              prop: "attachLink",
              listType: "picture-card",
              multiple: true,
              span: 24,
              // accept: "image/png, image/jpeg, audio/mp4, video/mp4",
              type: "upload",
              action: "/api/blade-resource/oss/endpoint/put-file",
              propsHttp: {
                res: "data",
                url: "link",
              },
              rules: [
                {
                  required: true,
                  message: "请上传文件",
                  trigger: ["blur", "change"],
                },
              ],
              row: true,
            },
          ],
        };
      } else {
        done();
      }
    },
    addEvidence() {
      let valuesArray = Object.values(this.addEvidenceForm);
      this.$refs.addEvidence.validate((valid) => {
        this.addEvidenceLoading = true;
        if (valid) {
          let meetingEvidences = [];
          this.addEvidenceForm.attachLink.map((item, index) => {
            let obj = {
              attachLink: item,
              attachOrder: index + 1,
              attachType: this.checkMediaType(item),
              name: valuesArray[index + 1],
            };
            meetingEvidences.push(obj);
          });
          let form = {
            meetingId: this.id,
            meetingEvidences: meetingEvidences,
          };
          submitList(form).then(
            () => {
              this.addEvidenceUrl = [];
              this.addDialogVisible = false;
              this.addEvidenceLoading = false;
              this.$message({
                type: "success",
                message: "操作成功",
              });
              this.getMeetingEvidenceList(this.meetingEvidencePage);
            },
            (error) => {
              this.addEvidenceLoading = false;
              console.log(error);
            }
          );
        }
      });
    },
    // 打开新页面
    toOpen(i) {
      window.open(
        "http://1.94.42.192:8886/onlinePreview?url=" +
          encodeURIComponent(Base64.encode(i))
      );
    },
    rowSaveMeetingEvidence(form, done) {
      form.meetingId = this.id;
      // form.attachType = this.checkMediaType(form.attachLink);
      meetingEvidenceAdd(form).then(
        () => {
          this.$message({
            type: "success",
            message: "操作成功",
          });
          this.getMeetingEvidenceList(this.meetingEvidencePage);
          done();
        },
        (error) => {
          done();
          console.log(error);
        }
      );
    },

    checkMediaType(url) {
      // 获取URL中的文件名
      var filename = url.substring(url.lastIndexOf("/") + 1);

      // 获取文件扩展名
      var extension = filename
        .substring(filename.lastIndexOf(".") + 1)
        .toLowerCase();

      // 支持的图片格式
      var imageExtensions = ["png", "jpg", "jpeg", "gif", "bmp", "svg"];
      // 支持的视频格式
      var videoExtensions = ["mp4", "mov", "avi", "mkv", "wmv", "flv", "webm"];
      // 如果文件扩展名在视频格式数组中，则返回“视频”，否则返回“图片”
      if (imageExtensions.includes(extension)) {
        return 1;
      } else if (videoExtensions.includes(extension)) {
        return 2;
      } else {
        return 3;
      }
    },

    rowDelMeetingEvidence(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeMeetingEvidence(row.id);
        })
        .then(() => {
          this.getMeetingEvidenceList(this.meetingEvidencePage);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    getMeetingEvidenceList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingEvidenceList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingEvidencePage.total = data.total;
        this.meetingEvidenceList = data.records;
        this.loading = false;
        this.$nextTick(() => {
          if (!this.$refs.meetingEvidence.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.meetingEvidence.doLayout();
          }
        });
      });
    },
    refreshChangeMeetingEvidence() {
      this.getMeetingEvidenceList(this.meetingEvidencePage);
    },
    //分页
    currentChangeMeetingEvidence(currentPage) {
      this.meetingEvidencePage.currentPage = currentPage;
      this.getMeetingEvidenceList(this.meetingEvidencePage);
    },
    sizeChangeMeetingEvidence(pageSize) {
      this.meetingEvidencePage.pageSize = pageSize;
      this.getMeetingEvidenceList(this.meetingEvidencePage);
    },

    //end
    //腾讯会议
    getMeetingTencentList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingTencentList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingTencentPage.total = data.total;
        this.meetingTencentList = data.records;
        this.meetingTencentList.map((item) => {
          item.startTime = item.startTime * 1000;
          item.endTime = item.endTime * 1000;
          return item;
        });
        this.loading = false;
        this.$nextTick(() => {
          if (!this.$refs.meetingTencent.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.meetingTencent.doLayout();
          }
        });
      });
    },
    //刷新
    refreshChangeMeetingTencent() {
      this.getMeetingTencentList(this.meetingTencentPage);
    },
    //分页
    currentChangeMeetingTencent(currentPage) {
      this.meetingTencentPage.currentPage = currentPage;
      this.getMeetingTencentList(this.meetingTencentPage);
    },
    sizeChangeMeetingTencent(pageSize) {
      this.meetingTencentPage.pageSize = pageSize;
      this.getMeetingTencentList(this.meetingTencentPage);
    },
    //end
    //议程信息
    getMeetingAgendaList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingAgendaList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingAgendaPage.total = data.total;
        this.meetingAgendaList = data.records;
        this.loading = false;
        this.$nextTick(() => {
          if (!this.$refs.meetingAgenda.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.meetingAgenda.doLayout();
          }
        });
      });
    },
    //刷新
    refreshChangeMeetingAgenda() {
      this.getMeetingAgendaList(this.meetingAgendaPage);
    },
    //分页
    currentChangeMeetingAgenda(currentPage) {
      this.meetingAgendaPage.currentPage = currentPage;
      this.getMeetingAgendaList(this.meetingAgendaPage);
    },
    sizeChangeMeetingAgenda(pageSize) {
      this.meetingAgendaPage.pageSize = pageSize;
      this.getMeetingAgendaList(this.meetingAgendaPage);
    },

    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("报告为空无法预览");
      }
    },

    //提交审核
    submitAudit() {
      this.submitAuditStatus = true;
      submitAudit(this.id).then(
        () => {
          this.submitAuditStatus = false;
          this.getDetail();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          this.submitAuditStatus = false;
          console.log(error);
        }
      );
    },
    //验收
    onAudit(auditStatus) {
      let _this = this;
      if (auditStatus == 1) {
        this.$confirm("确定将该条数据审核通过?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let data = {
              status: auditStatus,
              id: _this.id,
              approvalBusinessType: _this.form.approvalBusinessType,
              remark: "同意",
            };
            return submitRecheck(data);
          })
          .then(() => {
            // this.getDetail();
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.goBack();
          });
      } else {
        projectQuickReplyList({ moduleCode: "meeting" }).then(
          ({ data: res }) => {
            this.quickReplyList = res.data;
            this.auditForm.status = auditStatus;
            this.auditForm.id = _this.id;
            this.auditForm.approvalBusinessType =
              this.form.approvalBusinessType;
            this.auditDialogVisible = true;
          }
        );
      }
    },
    clickQuickReply(tag) {
      this.auditForm.remark = tag.content;
    },
    auditClick() {
      this.$refs.auditForm.validate((valid) => {
        if (valid) {
          this.auditLoading = true;
          submitRecheck(this.auditForm).then(
            () => {
              this.auditDialogVisible = false;
              this.auditLoading = false;
              // this.getDetail();
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              this.goBack();
            },
            (error) => {
              console.log(error);
              this.auditLoading = false;
            }
          );
        }
      });
    },
    // 打开新页面
    toopen(i) {
      window.open(i, "_blank");
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          if (_this.form.submitType == 1 || _this.form.submitType == 3) {
            //会议费用
            _this.meetingCostOption.menu = true;
            _this.meetingCostOption.addBtn = true;
            // 会议凭证
            _this.meetingEvidenceOption.addBtn = false;
            //腾讯会议
            _this.meetingTencentOption.menu = true;
          } else {
            //会议费用
            _this.meetingCostOption.menu = false;
            _this.meetingCostOption.addBtn = false;
            // 会议凭证
            _this.meetingEvidenceOption.addBtn = false;
            //腾讯会议
            _this.meetingTencentOption.menu = false;
          }
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },

    createMeeting() {
      let startTime = this.dialogForm.startTime;
      startTime = dayjs(startTime).valueOf();
      if (startTime < dayjs().valueOf()) {
        this.$message.error("开始时间不能早于当前时间!!!");
        return;
      }
      let endTime = dayjs(this.dialogForm.endTime).valueOf();
      if (endTime < startTime) {
        this.$message.error("结束时间不能早于开始时间!!!");
        return;
      }
      let data = {
        startTime: Math.floor(startTime / 1000),
        endTime: Math.floor(endTime / 1000),
        id: this.id,
        subject: this.dialogForm.subject,
      };
      createMeeting(data).then(
        () => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.dialogVisible = false;
          this.getMeetingTencentList(this.meetingTencentPage);
        },
        (error) => {
          console.log(error);
        }
      );
    },
    cancelMeeting() {
      this.$confirm("确定取消会议预约?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return cancelMeeting(this.id);
        })
        .then(() => {
          this.getMeetingTencentList(this.meetingTencentPage);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    copy(kyMeetingUrl) {
      this.$copyText(kyMeetingUrl).then(
        () => {
          this.$message({
            type: "success",
            message: "复制成功!",
          });
        },
        (e) => {
          this.$message({
            type: "error",
            message: "复制失败!" + e,
          });
        }
      );
    },
    //生成二维码
    createMeetingQRCode(row) {
      this.$message({
        message: "正在生成中,请稍后!",
        duration: 0,
      });
      this.kyMeetingUrl = row.kyMeetingUrl;
      createMeetingQRCode(row.id).then(
        (res) => {
          this.$message.closeAll();
          if (res.data.success) {
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.imageUrl = res.data.data;
            this.viewDialogVisible = true;
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (err) => {
          console.log(err);
          this.$message.closeAll();
          this.$message({
            type: "error",
            message: "生成失败,请稍后再试或联系管理员",
          });
        }
      );
    },
    // 下载签到码
    downloadImage(imageUrl) {
      downloadFileBlob(imageUrl, "签到码");
    },
    toDoctorView(row) {
      this.$router.push({
        path: `/authenticationDoctor/detail/${
          row.doctorId ? row.doctorId : 0
        }/1`,
      });
    },
    submit() {},
    toMeeting() {
      let url = window.location.href;
      // url = url.replace("/myiframe", "");
      console.log(url);
      // if (url) {
      //   this.$router.push({
      //     path: `/meeting/detail/${url}`,
      //   });
      // } else {
      //   this.$message.error("会议为空无法预览");
      // }
    },
  },
};
</script>

<style scoped lang="scss">
.el-card__body {
  position: relative;
}

.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}

/* 预约 */
.date-title {
  display: flex;
  align-items: center;
  font-size: 16px;
}
.date-title :nth-child(2) {
  height: 25px;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100% !important;
}

.button_wrap {
  margin: 0 auto;
  border-radius: 6px;
  border: 1px solid #1d90a2;
  width: 95%;
  display: flex;
  flex-wrap: wrap;
  /* 允许子项换行 */
  align-content: flex-start;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.preinstallItem {
  border-radius: 6px;
  font-size: 13px;
  min-width: 50px;
  line-height: 26px;
  text-align: center;
  border: 1px solid #e5e6eb;
  margin-left: 15px;
  margin-top: 15px;
  padding: 5px 14px;
}

.selectPreinstallItem0 {
  background-color: #fab6b6;
  border-color: #fab6b6;
  cursor: not-allowed;
}

.selectPreinstallItem1 {
  cursor: pointer;
}
.selectPreinstallItem1:hover {
  color: #1d90a2;
  border-color: #bbdee3;
  background-color: #e8f4f6;
}
.selectPreinstallItem2 {
  background-color: #c8c9cc;
  border-color: #c8c9cc;
  cursor: not-allowed;
}
.selectPreinstallItem3 {
  cursor: pointer;
  border: 1px solid transparent;
  background-color: #1d90a2;
  color: white;
}

.selectPreinstallItem3:hover {
  background-color: #1d90a2;
  color: white;
}
.copy {
  height: 34px;
  line-height: 34px;
  border: 1px solid #e6e6e6;
  width: 80px;
  text-align: center;
  cursor: pointer;
}
.qr-img {
  width: 250px;
  margin: 0 auto;
  img {
    width: 100%;
  }
}
</style>
