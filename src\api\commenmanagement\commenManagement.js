import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-commenManagement/commenManagement/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-commenManagement/commenManagement/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const treeById = (id) => {
  return request({
    url: '/api/blade-commenManagement/commenManagement/treeById',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-commenManagement/commenManagement/remove',
    method: 'post',
    data: ids
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-commenManagement/commenManagement/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-commenManagement/commenManagement/submit',
    method: 'post',
    data: row
  })
}

export const updateStatus = (row) => {
  return request({
    url: '/api/blade-commenManagement/commenManagement/updateStatus',
    method: 'post',
    data: row
  })
}

