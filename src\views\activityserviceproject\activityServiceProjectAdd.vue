<template>
  <div>
    <basic-container
      ><el-page-header @back="goBack" :content="$route.name"></el-page-header
    ></basic-container>
    <basic-container>
      <!--临床项目合作-->
      <avue-form ref="form" :option="option" v-model="form" @submit="submit">
        <!-- 疾病领域 -->
        <template slot-scope="{ disabled, size }" slot="diseaseDomain"
          ><el-tag
            :key="tag"
            v-for="tag in diseaseDomain"
            closable
            :disable-transitions="false"
            @close="handleClose(tag)"
          >
            {{ tag }}
          </el-tag>
          <el-input
            class="input-new-tag"
            v-if="inputVisible"
            v-model="inputValue"
            ref="saveTagInput"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
          >
          </el-input>
          <el-button v-else class="button-new-tag" @click="showInput"
            >+ 疾病领域</el-button
          ></template
        >
        <!-- 对应产品名称 -->
        <template slot-scope="{ disabled, size }" slot="productId">
          <el-select
            v-model="form.productId"
            @change="productChange"
            @focus="productFocus"
            filterable
            remote
            reserve-keyword
            placeholder="请选择对应产品名称"
            :remote-method="remoteMethod"
            :loading="selectLoading"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.productName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </template>
      </avue-form>
    </basic-container>
  </div>
</template>

<script>
import {
  add,
  getProductList,
  listByProductId as getListByProductId,
} from "@/api/activityserviceproject/activityServiceProject";
import { listByProductId } from "@/api/caseCollection/caseCollection";
import dayjs from "dayjs";
export default {
  data() {
    return {
      //对应产品名称
      selectOption: {}, //选中的对象
      options: [],
      selectLoading: false,
      //end
      //疾病领域
      inputVisible: false,
      inputValue: "",
      diseaseDomain: [],
      //end
      //参与目标科室
      inputVisible2: false,
      inputValue2: "",
      //end

      id: "",
      query: {},
      loading: false,
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      form: {
        chargingCost: "",
        diseaseDomain: "疾病领域",
        departmentName: "参与目标科室",
      },
      //服务项目类型
      actType: "8",
      option: {
        disabled: false,
        labelWidth: "170",
        submitIcon: " ",
        emptyIcon: " ",
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",
            column: [
              {
                label: "企业名称",
                prop: "entrustedCompanyId",
                type: "select",
                props: {
                  label: "name",
                  value: "id",
                },
                dicUrl: "/api/blade-csc/entrustedCompany/getList",
                rules: [
                  {
                    required: true,
                    message: "请选择企业名称",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "服务项目类型",
                prop: "projectType",
                type: "select",
                dicUrl:
                  "/api/blade-act/activityProjectType/getClinicalCooperation",
                props: {
                  label: "projectTypeName",
                  value: "id",
                },

                rules: [
                  {
                    required: true,
                    message: "请选择服务项目类型",
                    trigger: ["blur", "change"],
                  },
                ],
                change: ({ item }) => {
                  if (item) {
                    // 5/8
                    this.projectTypeChange(item.actType);
                  }
                },
              },
              {
                label: "对应产品名称",
                prop: "productId",
                type: "input",
                display: false,
                rules: [
                  {
                    required: true,
                    message: "请输入对应产品名称",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "项目方案名称",
                prop: "projectName",
                type: "input",
                maxlength: 50,
                showWordLimit: true,
                rules: [
                  {
                    required: true,
                    message: "请输入项目方案名称",
                    trigger: ["blur", "change"],
                  },
                ],
                span: 24,
              },
              {
                label: "项目开始日期",
                prop: "startDate",
                type: "date",
                format: "yyyy年MM月dd日",
                valueFormat: "yyyy-MM-dd",
                rules: [
                  {
                    required: true,
                    message: "请选择项目开始日期",
                    trigger: ["blur", "change"],
                  },
                ],
                change: ({ value }) => {
                  if (
                    dayjs(value).valueOf() > dayjs(this.form.endDate).valueOf()
                  ) {
                    this.form.endDate = "";
                  }
                },
                pickerOptions: {
                  // disabledDate: (time) => {
                  //   return (
                  //     time.getTime() < dayjs().valueOf() - 24 * 60 * 60 * 1000
                  //   );
                  // },
                },
              },
              {
                label: "项目截止日期",
                prop: "endDate",
                type: "date",
                format: "yyyy年MM月dd日",
                valueFormat: "yyyy-MM-dd",
                rules: [
                  {
                    required: true,
                    message: "请选择项目截止日期",
                    trigger: ["blur", "change"],
                  },
                ],
                pickerOptions: {
                  disabledDate: (time) => {
                    // let now = dayjs().valueOf() - 24 * 60 * 60 * 1000;
                    let date = dayjs(this.form.startDate).valueOf();
                    let start;
                    // if (now < date) {
                      start = date;
                    // } else {
                    //   start = now;
                    // }
                    return time.getTime() < start;
                  },
                },
              },
              {
                label: "涉及疾病领域",
                prop: "diseaseDomain",
                type: "input",
                rules: [
                  {
                    required: true,
                    message: "请输入涉及疾病领域",
                    trigger: ["blur", "change"],
                  },
                ],
                row: true,
                span: 24,
              },
              {
                label: "参与会员要求",
                prop: "doctorClaim",
                type: "input",
                maxlength: 100,
                showWordLimit: true,
                span: 24,
              },
              {
                label: "项目发起部门",
                prop: "orgDepartment",
                type: "input",
                maxlength: 20,
                showWordLimit: true,
                rules: [
                  {
                    required: true,
                    message: "请输入项目发起部门",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "项目负责人员",
                prop: "orgPersonnel",
                type: "input",
                maxlength: 20,
                showWordLimit: true,
                rules: [
                  {
                    required: true,
                    message: "请输入项目负责人员",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "项目验收部门",
                prop: "checkDepartment",
                type: "input",
                maxlength: 20,
                showWordLimit: true,
                rules: [
                  {
                    required: true,
                    message: "请输入项目验收部门",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "项目验收人员",
                prop: "checkPersonnel",
                type: "input",
                maxlength: 20,
                showWordLimit: true,
                rules: [
                  {
                    required: true,
                    message: "请输入项目验收人员",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "项目开展目的",
                prop: "projectPurpose",
                type: "textarea",
                minRows: 3,
                maxRows: 5,
                rules: [
                  {
                    required: true,
                    message: "请输入项目开展目的",
                    trigger: ["blur", "change"],
                  },
                ],
                span: 24,
                maxlength: 500,
                showWordLimit: true,
              },
              {
                label: "项目开展内容",
                prop: "projectContent",
                type: "textarea",
                minRows: 3,
                maxRows: 5,
                rules: [
                  {
                    required: true,
                    message: "请输入临床项目内容",
                    trigger: ["blur", "change"],
                  },
                ],
                span: 24,
                maxlength: 500,
                showWordLimit: true,
              },
            ],
          },
        ],
      },
      //模板列表
      templateNameList: [],
    };
  },
  created() {
    // this.id = this.$route.params.id;
    // this.getDetail();
  },
  methods: {
    //服务项目类型修改
    projectTypeChange(actType) {
      this.actType = actType;
      if (actType == 5 || actType == 8) {
        this.option.group[0].column[2].display = true;
      } else {
        this.option.group[0].column[2].display = false;
        this.form.productId = "";
      }
      if (actType == 5) {
        this.form.templateId = "";
      } else {
        if (!this.validatenull(this.form.productId)) {
          if (actType == 8) {
            this.listByProductId(this.form.productId);
          } else if (actType == 4) {
            this.getListByProductId(this.form.productId);
          }
        }
      }
    },
    //初始化数据
    initOptions() {
      this.selectLoading = true;
      getProductList().then((res) => {
        const data = res.data;
        this.options = data.data;
        this.selectLoading = false;
      });
    },
    //涉及产品名称搜索
    remoteMethod(query) {
      if (query !== "") {
        this.selectLoading = true;
        getProductList(query).then((res) => {
          const data = res.data;
          this.options = data.data;
          this.selectLoading = false;
        });
      } else {
        this.initOptions();
      }
    },
    //获取焦点
    productFocus() {
      if (this.options.length == 0) {
        this.initOptions();
      }
    },
    //涉及产品名称更改
    productChange(value) {
      let obj = this.options.filter((item) => {
        return item.id == value;
      });
      this.selectOption = obj[0];
      if (value && this.actType == 8) {
        this.listByProductId(value);
      } else if (value && this.actType == 4) {
        this.getListByProductId(value);
      }
    },
    //涉及产品名称end
    //模版
    listByProductId(value) {
      listByProductId(value).then(({ data: res }) => {
        this.option.group[0].column[3].dicData = res.data;
        this.templateNameList = res.data;
        this.baseProductChange();
      });
    },
    //问卷调研模板
    getListByProductId(value) {
      getListByProductId(value).then(({ data: res }) => {
        this.option.group[0].column[3].dicData = res.data;
        this.templateNameList = res.data;
        this.baseProductChange();
      });
    },

    //产品改变后判断是否还有该条模板
    baseProductChange() {
      let templateNameList = this.templateNameList.filter((item) => {
        return item.id == this.form.templateId;
      });
      //方案为空
      if (templateNameList.length == 0) {
        this.form.templateId = "";
      }
    },
    //end
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    //疾病领域
    handleClose(tag) {
      this.diseaseDomain.splice(this.diseaseDomain.indexOf(tag), 1);
    },
    showInput() {
      let value = this.diseaseDomain.join(", ");
      if (value.length >= 500) {
        this.$message({
          type: "error",
          message: "疾病领域最多输入500字!",
        });
        return;
      }
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue == "") {
        this.inputVisible = false;
        return;
      }
      let reg = ",";
      let reg2 = "，";
      if (inputValue.indexOf(reg) != -1 || inputValue.indexOf(reg2) != -1) {
        this.$message({
          type: "error",
          message: "疾病领域不能输入逗号!",
        });
        return;
      }
      let value = this.diseaseDomain.join(", ");
      let length = value.length + this.inputValue.length + 1;
      if (length >= 500) {
        this.$message({
          type: "error",
          message: "疾病领域最多输入500字!",
        });
        return;
      }
      this.diseaseDomain.push(inputValue);
      this.inputVisible = false;
      this.inputValue = "";
    },
    //end
    handleInputConfirm2() {
      let inputValue = this.inputValue2;
      if (inputValue == "") {
        this.inputVisible2 = false;
        return;
      }
      let reg = ",";
      let reg2 = "，";
      if (inputValue.indexOf(reg) != -1 || inputValue.indexOf(reg2) != -1) {
        this.$message({
          type: "error",
          message: "参与医疗机构不能输入逗号!",
        });
        return;
      }
      let value = this.departmentName.join(", ");
      let length = value.length + this.inputValue.length + 1;
      if (length >= 500) {
        this.$message({
          type: "error",
          message: "参与医疗机构总字数最多500字!",
        });
        return;
      }
      this.departmentName.push(inputValue);
      this.inputVisible2 = false;
      this.inputValue2 = "";
    },
    submit(form, done) {
      if (this.diseaseDomain.length == 0) {
        this.$message({
          type: "error",
          message: "疾病领域不能为空!",
        });
        done();
        return;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          form.entrustedCompanyName = this.form.$entrustedCompanyId; //企业名称
          form.templateName = this.form.$templateId; //模版名称
          form.productId = this.selectOption.id; //对应产品名称id
          form.productName = this.selectOption.productName; //对应产品名称
          form.diseaseDomain = this.diseaseDomain.join(",");
          add(form).then(
            () => {
              this.goBack();
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              done();
            },
            (error) => {
              done();
              console.log(error);
            }
          );
        }
      });
    },
    openSignature() {},
  },
};
</script>

<style scope>
.avue-crud__menu {
  min-height: 0 !important;
}
h4 {
  margin: 5px 0 !important;
}
.first-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
