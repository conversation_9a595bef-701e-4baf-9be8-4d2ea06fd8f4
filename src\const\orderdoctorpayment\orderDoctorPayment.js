export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  viewBtnIcon: " ",
  dialogClickModal: false,
  column: [
    {
      label: "会员订单编号",
      prop: "orderDoctorCode",
      type: "input",
      search: true,
      searchLabelWidth: "120",
    },
    {
      label: "会员订单ID",
      prop: "orderDoctorId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "结算单ID",
      prop: "doctorSettlementId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "结算单编号",
      prop: "doctorSettlementCode",
      type: "input",
      search: true,
      searchLabelWidth: "120",
    },
    {
      label: "注册医师Id",
      prop: "doctorId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "会员姓名",
      prop: "doctorName",
      type: "input",
      search: true,
    },
    {
      label: "会员手机号",
      prop: "doctorPhone",
      type: "input",
      search: true,
      searchLabelWidth: "120",
    },
    {
      label: "身份证号",
      prop: "doctorIdCard",
      type: "input",
      search: true,
    },
    {
      label: "银行卡号",
      prop: "doctorCardNo",
      type: "input",
    },
    {
      label: "支付金额",
      prop: "paymentAmount",
      type: "input",
    },

    {
      label: "支付时间",
      prop: "paymentTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
    },
    {
      label: "支付凭证",
      prop: "paymentEvidence",
      type: "input",
    },
    {
      label: "支付结果",
      prop: "paymentResult",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=payment_result",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      search: true,
      searchLabelWidth: "120",
    },
    {
      label: "失败原因",
      prop: "failReason",
      type: "input",
    },
  ],
};
