import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-settlementCycle/settlementCycle/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-settlementCycle/settlementCycle/detail",
    method: "get",
    params: {
      id,
    },
  });
};
//结算项目明细
export const settlementCycleProjectList = (
  current,
  size,
  settlementCycleId
) => {
  return request({
    url: "/api/blade-settlementCycleProjectDetail/settlementCycleProjectDetail/list",
    method: "get",
    params: {
      settlementCycleId,
      current,
      size,
    },
  });
};

//应税项目明细
export const settlementCycleTaxItemList = (
  current,
  size,
  settlementCycleId
) => {
  return request({
    url: "/api/blade-settlementCycleTaxItem/settlementCycleTaxItem/list",
    method: "get",
    params: {
      settlementCycleId,
      current,
      size,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-settlementCycle/settlementCycle/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-settlementCycle/settlementCycle/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-settlementCycle/settlementCycle/submit",
    method: "post",
    data: row,
  });
};

//确认开票
export const confim = (ids) => {
  return request({
    url: "/api/blade-settlementCycle/settlementCycle/confim",
    method: "post",
    params: {
      ids,
    },
  });
};
