<template>
  <div>
    <basic-container>
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
      >
        <template slot-scope="{}" slot="entrustedCompanyName">
          <div class="to-view" @click="toEntrustedCompanyView()">
            <a readonly>
              {{ form.entrustedCompanyName }}
            </a>
          </div>
        </template>
        <template slot-scope="{}" slot="tenderInfoName">
          <div class="to-view" @click="toTenderInfoView()">
            <a readonly>
              {{ form.tenderInfoName }}
            </a>
          </div>
        </template>
      </avue-form>
      <template v-else> <el-skeleton :rows="10" animated /> </template
    ></basic-container>
    <basic-container v-if="form.protocolFile">
      <iframe
        :src="form.protocolFile"
        width="100%"
        :height="iframeHeight"
        title="协议"
        frameBorder="no"
        border="0"
        marginWidth="0"
        marginHeight="0"
        scrolling="no"
        allowTransparency="yes"
      ></iframe>
    </basic-container>
    <div style="height: 5vh"></div>
  </div>
</template>

<script>
import { getDetail } from "@/api/tenderentrustedprotocols/tenderEntrustedProtocols";
export default {
  data() {
    return {
      iframeHeight: window.innerHeight - 180,
      option: {
        labelWidth: "150",
        submitBtn: false,
        emptyBtn: false,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        editBtn: false,
        disabled: true,
        column: [
          {
            label: "编号",
            prop: "code",
            type: "input",
            editDisabled: true,
            addDisplay: false, //新增时是否显示
          },
          {
            label: "合同名称",
            prop: "name",
            type: "input",
            search: true,
          },
          {
            label: "企业名称",
            prop: "entrustedCompanyName",
            type: "input",
            search: true,
          },
          {
            label: "招募名称",
            prop: "tenderInfoName",
            type: "input",
          },
          {
            label: "开始日期",
            prop: "serviceStartDate",
            span: 12,
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "结束日期",
            prop: "serviceEndtDate",
            span: 12,
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "企业签约人",
            prop: "signName",
            type: "input",
          },
          {
            label: "企业签约时间",
            prop: "signTime",
            span: 12,
            type: "datetime",
            format: "yyyy-MM-dd ",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
        ],
      },
      form: {},
    };
  },
  computed: {},
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  methods: {
    toTenderInfoView() {
      this.$router.push({
        path: `/tenderInfoDoctor/detail/${this.form.tenderId}`,
      });
    },

    //去企业会员详情
    toEntrustedCompanyView() {
      this.$router.push({
        path: `/entrustedcompany/detail/${this.form.entrustedCompanyId}/1`,
      });
    },
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },

    // 获取详情
    getDetail() {
      getDetail(this.id).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            this.form = data.data;
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
  },
};
</script>

<style></style>
