.login-container {
  display: flex;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 auto;

}
.login-container-left{
  display: flex;
  align-items: center;
  position: relative;
  width: 75%;
  height: 100%;
  background-image: url("/img/login/banner1.png");
  background-size: cover;
  background-position: 90% center ;
  background-repeat:no-repeat;
  .login-img{
    position: absolute;
    top: 60px;
    left: 5%;
    width: 230px;
    img{
      width: 230px;
    }
  }

  .login-img2{
    margin: 0 auto;
    width: 892px;
    height: 513px;
    img{
      width: 892px;
      height: 513px;
    }
  }
}





.login-left {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  justify-content: center;
  flex-direction: column;
  background-color: #8b9aac;
  color: #fff;
  float: left;
  width: 43%;
  position: relative;
}





.login-introduce {
  position: absolute;
  left: 120px;
  top: calc(50% - 70px);
  z-index: 99;
  overflow: hidden;
  text-align: center;
  > :first-child {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 15px;
  }
  > :nth-child(2) {
    margin: 0;
    font-size: 26px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 40px;
  }
  > :last-child {
    margin: 0;
    font-size: 19px;
    font-weight: 400;
    color: #666666;
    line-height: 22px;
    width: 85%;
    margin: 0 auto;
    text-align: center;
  }
}




.login-main {

  .el-tabs__nav-wrap::after {
    height: 0px;
  }
  .el-tabs__item {
    font-size: 20px;
  }
  .el-tabs__item:hover {
    color: #303133;
  }
  .is-active:hover{
    // font-size: 32px;

  }
  //.el-checkbox__input.is-checked+.el-checkbox__label{
  //  color: lightgray !important;
  //}
  .links .el-checkbox__label {
    color: lightgray !important;
  }
  .links .el-checkbox__inner {
    border-color: #9AD94E !important;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #9AD94E !important;
    border-color: #9AD94E !important;
  }
  .el-tabs__nav-scroll {
    margin-top: 55px;
    // height: 46px;
    justify-content: space-between !important;
  }

  .el-tabs__active-bar {
    z-index: 99;
  }

  margin: 0 auto;
  margin-left: 80px;
  border-radius: 5px;
  box-sizing: border-box;
  .el-tabs {
    width: 100%;
    margin: 0 auto;
  }
  .el-tabs__nav-scroll {
    display: flex;
    justify-content: center;
  }
  .is-active {
    color: #9AD94E !important;

  }
  .el-tabs__active-bar {
    background-color:  #9AD94E !important;
  }
}

.login-main > h3 {
  margin-bottom: 20px;
}

.login-main > p {
  color: #76838f;
}

.login-title {
  color: #333;
  margin-bottom: 40px;
  font-weight: bold;
  font-size: 32px;
  text-align: left;
  letter-spacing: 4px;
}

.login-menu {
  margin-top: 40px;
  width: 100%;
  text-align: center;

  a {
    color: #999;
    font-size: 12px;
    margin: 0px 8px;
  }
}


.login-submit {
  width: 100%;
  height: 45px;
  border: 1px solid #9AD94E !important;
  background: none;
  font-size: 18px;
  letter-spacing: 2px;
  font-weight: 300;
  background: #9AD94E;
  color: #fff !important;
  cursor: pointer;
  margin-top: 30px;
  font-family: "neo";
  transition: 0.25s;
}

.login-submit:hover {
  // border: none;
  background: rgb(29, 144, 161) !important;
  color: #fff !important;
}

.login-form {
  margin: 10px 0;

  .el-input--small{
    font-size: 16px;
  }
  i {
    color: #333;
  }

  .el-form-item__content {
    width: 100%;
  }

  .el-form-item {
    margin-bottom: 25px;
  }
  .el-input__inner{
    height: 42px;
    line-height: 42px;
    input{
      padding-bottom: 20px;
    }

  }

}

.size-icon {
  height: 60px;
  font-size: 25px;
  margin-right: 5px;
}


.login-code {
  margin-top: -2px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.login-code-img {
  z-index: 999;
  width: 120px;
  max-height: 55px;
  background-color: #fdfdfd;
  border: 1px solid #f0f0f0;
  color: #333;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 5px;
  line-height: 38px;
  text-indent: 5px;
  text-align: center;
  cursor: pointer !important;
}
.background {
  position: absolute;
  width: 100%;
  height: 100%;
}
.links{
  position: relative;
  margin: 0 auto;
  width: 400px;
  margin-top: 20px
}

.login-container{
  .el-input__suffix {
    display: flex;
    align-items: center;
  }
  .el-button--primary:focus, .el-button--primary:hover{
    // border: none;
    background: #9AD94E !important;
    color: #fff !important;
  }
}
.login-container-right{
  margin-top: 250px;
}
.footer-copyright{
    text-align: center;
    margin-top: 20px;
    padding-bottom: 20px;
    color: #999;
    cursor: pointer;
}


