<template>
  <div>
    <basic-container>
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <avue-form ref="form" :option="option" v-model="form">
        <template slot-scope="{ type, disabled }" slot="issue">
          <div class="preinstall">
            <div class="title">可用病例征集属性信息库</div>
            <div class="preinstallList">
              <div
                class="preinstallItem"
                :class="idList.includes(item.id) ? 'selectPreinstallItem' : ''"
                v-for="(item, index) in preinstallList"
                :key="index"
              >
                <div>
                  {{ item.attrName }}
                </div>
              </div>
            </div>
          </div>
        </template>
        <template slot-scope="{ type, disabled }" slot="issueList">
          <div>
            <div class="title">
              已关联的病例征集属性
              <el-tooltip
                class="item"
                effect="dark"
                content="请点选已关联的行属性对征集的属性进行排序显示，确定后保存"
                placement="right"
              >
                <i class="el-icon-info"></i>
              </el-tooltip>
            </div>

            <div style="width: 95%; margin: 0 auto">
              <avue-crud :option="topicOption" :data="selectPreinstallList">
                <template slot="attrType" slot-scope="{ row }">
                  <el-tag v-if="row.attrType == 1" type="warning" size="small"
                    >单选</el-tag
                  >
                  <el-tag v-if="row.attrType == 2" type="success" size="small"
                    >多选</el-tag
                  >
                  <el-tag v-if="row.attrType == 3" size="small">文本</el-tag>
                  <el-tag v-if="row.attrType == 4" size="small">图片</el-tag>
                </template>
                <template slot="requiredStatus" slot-scope="{ row }">
                  <el-tag
                    v-if="row.requiredStatus == 1"
                    type="success"
                    size="small"
                    >是</el-tag
                  >
                  <el-tag
                    v-if="row.requiredStatus == 0"
                    type="info"
                    size="small"
                    >否</el-tag
                  >
                </template>
              </avue-crud>
            </div>
          </div>
        </template>
      </avue-form>
    </basic-container>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getDetail } from "@/api/casecollectiontemplate/caseCollectionTemplate";
import { getIssueList } from "@/api/casecollectiontemplate/caseCollectionTemplate";
export default {
  data() {
    return {
      //关联产品名称
      selectOption: {}, //选中的对象
      options: [],
      selectLoading: false,
      //选中的问题列表
      selectPreinstallList: [],
      idList: [],
      //预设问题
      preinstallList: [],
      id: "",
      //基础信息
      option: {
        labelWidth: "150",
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        column: [
          {
            label: "客户名称",
            prop: "entrustedCompanyName",
          },
          {
            label: "对应产品名称",
            prop: "templateProductName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请选择对应产品名称",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "模版名称/标题",
            prop: "templateName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入模版名称/标题",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "服务项目名称",
            prop: "projectTypeName",
          },
          {
            span: 24,
            labelPosition: "top",
            label: "",
            prop: "issue",
            type: "input",
          },
          {
            span: 24,
            labelPosition: "top",
            label: "",
            prop: "issueList",
            type: "input",
          },
        ],
      },
      form: {},
      data: [],
      topicOption: {
        height: "400",
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        refreshBtn: false,
        columnBtn: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "病例征集属性名称",
            prop: "attrName",
          },
          {
            label: "属性类型",
            prop: "attrType",
            type: "select",
            dicData: [
              {
                label: "单选",
                value: 1,
              },
              {
                label: "多选",
                value: 2,
              },
              {
                label: "文本",
                value: 3,
              },
            ],
          },
          {
            label: "属性值选项",
            prop: "attrValue",
            type: "input",
          },
          {
            label: "是否必填",
            prop: "requiredStatus",
            type: "switch",
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  methods: {
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          this.getIssueList();
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    //问题列表
    getIssueList() {
      getIssueList().then((res) => {
        let data = res.data;
        data.data.map((item) => {
          return (item.requiredStatus = 0);
        });
        this.preinstallList = data.data;
        this.initialize();
      });
    },
    //初始化数据
    initialize() {
      if (this.form.relEntities.length > 0) {
        this.form.relEntities.map((item) => {
          this.idList.push(item.caseTemplateAttrId);
          this.preinstallList.map((item2) => {
            if (item.caseTemplateAttrId == item2.id) {
              item2.requiredStatus = item.requiredStatus;
              this.selectPreinstallList.push(item2);
            }
          });
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.title {
  margin: 15px 30px;
  font-size: 18px;
  font-weight: 600;
}

.preinstallList {
  margin: 0 auto;
  border-radius: 6px;
  border: 1px solid #1d90a2;
  width: 95%;
  // height: 400px;
  display: flex;
  flex-wrap: wrap;
  /* 允许子项换行 */
  align-content: flex-start;
  padding-bottom: 15px;
}

.preinstallItem {
  cursor: pointer;
  border-radius: 6px;
  font-size: 13px;
  min-width: 50px;
  line-height: 26px;
  text-align: center;
  border: 1px solid #e5e6eb;
  margin-left: 15px;
  margin-top: 15px;
  padding: 5px 14px;
}

.preinstallItem:hover {
  background-color: #f2f3f5;
}

.selectPreinstallItem {
  cursor: not-allowed;
  border: 1px solid transparent;
  background-color: #1d90a2;
  color: white;
}

.selectPreinstallItem:hover {
  background-color: #1d90a2;
  color: white;
}
</style>
