<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="jobStatus" slot-scope="{ row }">
        <el-tag v-if="row.jobStatus == 0" type="info" size="small">离职</el-tag>
        <el-tag v-if="row.jobStatus == 1" type="success" size="small"
          >在职</el-tag
        >
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button
          v-if="
            permission.entrustedEmployee_createEmployeeUser && !scope.row.userId
          "
          :size="scope.size"
          :type="scope.type"
          @click="createEmployeeUser(scope.row)"
          >开通账号</el-button
        ></template
      >
    </avue-crud>
    <el-dialog
      title="查看"
      @close="form = {}"
      append-to-body
      :visible.sync="viewDialogVisible"
      width="60%"
    >
      <avue-form :option="viewDialogOption" v-model="viewDialogForm">
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  createEmployeeUser,
} from "@/api/entrustedemployee/entrustedEmployee";
import { tree } from "@/api/entrusteddept/entrustedDept";
import option from "@/const/entrustedemployee/entrustedEmployee";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      //查看
      viewDialogVisible: false,
      viewDialogForm: {},
      viewDialogOption: {
        disabled: true,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: "100",
        column: [
          {
            label: "企业名称",
            prop: "entrustedCompanyId",
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            dicUrl: "/api/blade-csc/entrustedCompany/getList",
          },
          {
            label: "姓名",
            prop: "name",
            type: "input",
          },
          {
            label: "工号",
            prop: "jobNumber",
            type: "input",
          },
          {
            label: "职位",
            prop: "jobPosition",
            type: "input",
          },
          {
            label: "部门简称",
            prop: "entrustedDeptShort",
            type: "input",
          },
          {
            label: "部门全称",
            prop: "entrustedDept",
            type: "input",
          },
          {
            label: "手机号码",
            prop: "phone",
            type: "input",
          },
          {
            label: "在职状态",
            prop: "jobStatus",
            type: "select",
            dicData: [
              {
                label: "离职",
                value: 0,
              },
              {
                label: "在职",
                value: 1,
              },
            ],
          },
        ],
      },
      //end
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      dicData: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.entrustedEmployee_add, false),
        viewBtn: this.vaildData(this.permission.entrustedEmployee_view, false),
        delBtn: this.vaildData(this.permission.entrustedEmployee_delete, false),
        editBtn: this.vaildData(this.permission.entrustedEmployee_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  mounted() {
    this.initData();
  },

  methods: {
    createEmployeeUser(row) {
      createEmployeeUser(row.id).then(({ data: res }) => {
        if (res.success) {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "开通成功!",
          });
        } else {
          this.$message({
            type: "error",
            message: res.message,
          });
        }
      });
    },
    initData() {
      tree().then((res) => {
        const column = this.findObject(this.option.column, "entrustedDeptId");
        column.dicData = res.data.data;
        this.dicData = res.data.data;
      });
    },
    getTitleAndFullNameById(data, id) {
      function findItem(items, targetId) {
        for (const item of items) {
          if (item.id === targetId) {
            return {
              entrustedDeptShort: item.title,
              entrustedDept: item.fullName,
            };
          }
          if (item.hasChildren) {
            const result = findItem(item.children, targetId);
            if (result) {
              return result;
            }
          }
        }
        return null;
      }

      return findItem(data, id);
    },
    rowSave(row, done, loading) {
      Object.assign(
        row,
        this.getTitleAndFullNameById(this.dicData, row.entrustedDeptId)
      );
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      Object.assign(
        row,
        this.getTitleAndFullNameById(this.dicData, row.entrustedDeptId)
      );
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        getDetail(this.form.id).then((res) => {
          this.viewDialogVisible = true;
          this.viewDialogForm = res.data.data;
        });
      } else if ("edit" == type) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          done();
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
