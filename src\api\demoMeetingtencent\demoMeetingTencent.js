import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/meetingTencent/page",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/demo/meetingTencent/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/demo/meetingTencent/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/demo/meetingTencent/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/demo/meetingTencent/submit",
    method: "post",
    data: row,
  });
};

//申请会议室
export const createMeeting = (data) => {
  return request({
    url: "/api/blade-act/demo/meeting/createMeeting",
    method: "post",
    data,
  });
};

//取消会议
export const cancelMeeting = (id) => {
  return request({
    url: "/api/blade-act/demo/meeting/cancelMeeting",
    method: "get",
    params: {
      id,
    },
  });
};
//生成二维码
export const createMeetingQRCode = (id) => {
  return request({
    url: "/api/blade-act/demo/meetingTencent/createMeetingQRCode",
    method: "get",
    params: {
      id,
    },
  });
};

//获取预约时间点
export const appointed = (date, meetingId) => {
  return request({
    url: "/api/blade-act/demo/meeting/getMeetingInfo",
    method: "get",
    params: {
      date,
      meetingId,
    },
  });
};
