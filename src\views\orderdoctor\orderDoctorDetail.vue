<template>
  <div>
    <basic-container>
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
      >
        <!-- 任务编号 -->
        <template slot-scope="{}" slot="orderEntrustedCode">
          <div class="to-view" @click="toOrderEntrustedView()">
            <a readonly>
              {{ form.orderEntrustedCode }}
            </a>
          </div>
        </template>
        <!-- 计划编号 -->
        <template slot-scope="{}" slot="planCode">
          <div class="to-view" @click="toActivityPlan()">
            <a readonly>
              {{ form.planCode }}
            </a>
          </div>
        </template>
        <!-- <template slot-scope="{ disabled, size }" slot="protocolFile">
          <div style="cursor: pointer">
            <el-tag @click="preview(form.protocolFile)"
              >预览《会员订单合同》</el-tag
            >
          </div>
        </template> -->
      </avue-form>
      <template v-else> <el-skeleton :rows="10" animated /> </template>
      <div style="height: 50px"></div>
      <avue-crud
        :option="crudOption"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :permission="permissionList"
        :before-open="beforeOpen"
        v-model="curdForm"
        ref="crud"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
      >
        <!-- 初审状态 -->
        <template slot="approvalStatus" slot-scope="{ row }">
          <div v-if="actType == 2">
            <div v-if="row.submitType != 1">
              <el-tag v-if="row.approvalStatus == 0" size="small"
                >待审核</el-tag
              >
              <el-tag v-if="row.approvalStatus == 1" type="success" size="small"
                >通过</el-tag
              >
              <el-tag v-if="row.approvalStatus == 2" type="danger" size="small"
                >驳回</el-tag
              >
            </div>
          </div>
          <div v-else>
            <el-tag v-if="row.approvalStatus == 0" size="small">待审核</el-tag>
            <el-tag v-if="row.approvalStatus == 1" type="success" size="small"
              >通过</el-tag
            >
            <el-tag v-if="row.approvalStatus == 2" type="danger" size="small"
              >驳回</el-tag
            >
          </div>
        </template>
        <!-- 验收状态 -->
        <template slot="confirmStatus" slot-scope="{ row }">
          <div v-if="actType == 2">
            <div v-if="row.submitType != 1">
              <el-tag v-if="row.confirmStatus == 0" size="small">待验收</el-tag>
              <el-tag v-if="row.confirmStatus == 1" type="success" size="small"
                >通过</el-tag
              >
              <el-tag v-if="row.confirmStatus == 2" type="danger" size="small"
                >驳回</el-tag
              >
            </div>
          </div>
          <div v-else>
            <el-tag v-if="row.confirmStatus == 0" size="small">待验收</el-tag>
            <el-tag v-if="row.confirmStatus == 1" type="success" size="small"
              >通过</el-tag
            >
            <el-tag v-if="row.confirmStatus == 2" type="danger" size="small"
              >驳回</el-tag
            >
          </div>
        </template>

        <template slot="menuLeft"> </template>

        <template slot-scope="{ type, size, row, index }" slot="codeNumber">
          <div class="to-view" @click="toView(row.id)" v-if="row.codeNumber">
            <a readonly>
              {{ row.codeNumber }}
            </a>
          </div>
          <div v-else>无</div>
        </template>
      </avue-crud>
    </basic-container>
    <div style="height: 5vh"></div>
  </div>
</template>

<script>
import { getDetail } from "@/api/orderdoctor/orderDoctor";
import { getList } from "@/api/caseCollectionReport/caseCollectionReport";
import { getList as getClinicalResearchReportlist } from "@/api/clinicalresearchreport/clinicalResearchReport";
import { getList as getMedicationFeedbackReportList } from "@/api/medicationFeedbackReport/medicationFeedbackReport";
import { meetingOrderList as getMeetingList } from "@/api/meeting/meeting";
import { orderDoctorList as getLectureList } from "@/api/lecture/lecture";
import { getList as getProfessionalReviewReportList } from "@/api/professionalreviewreport/professionalReviewReport";

import { mapGetters } from "vuex";
export default {
  data() {
    return {
      iframeHeight: window.innerHeight - 180,
      option: {
        labelWidth: "150",
        submitBtn: false,
        emptyBtn: false,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        editBtn: false,
        disabled: true,
        column: [
          {
            label: "订单编号",
            prop: "code",
            type: "input",
          },
          {
            label: "计划编号",
            prop: "planCode",
            type: "input",
          },
          // {
          //   label: "任务编号",
          //   prop: "orderEntrustedCode",
          //   type: "input",
          // },
          {
            label: "客户名称",
            prop: "entrustedCompanyName",
            type: "input",
          },
          {
            label: "平台名称",
            prop: "taskPlateName",
            type: "input",
          },
          {
            label: "产品名",
            prop: "baseProductName",
            type: "input",
          },
          {
            label: "产品线",
            prop: "baseProductLine",
            type: "input",
          },
          {
            label: "服务项目类型",
            prop: "projectTypeName",
            type: "input",
          },
          // {
          //   label: "客户业务部门",
          //   prop: "baseDepartmentName",
          //   type: "input",
          // },
          // {
          //   label: "客户业务人员",
          //   prop: "baseEmployeeName",
          //   type: "input",
          // },
          {
            label: "订单发布时间",
            prop: "realityStartDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "订单计划开始时间",
            prop: "invitationStartDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },

          {
            label: "订单计划结束时间",
            prop: "invitationEndDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },

          {
            label: "税后单价(元)",
            prop: "taskUnitPrice",
            type: "input",
          },
          {
            label: "订单任务数量",
            prop: "planNum",
            type: "input",
          },
          {
            label: "订单计划金额",
            prop: "planAmount",
            type: "input",
          },
          {
            label: "订单任务状态",
            prop: "orderStatus",
            type: "input",
          },
          {
            label: "接单人员姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "接单人员手机",
            prop: "doctorPhone",
            type: "input",
          },
          {
            label: "所在单位名称",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "所在部门名称",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "订单接单时间",
            prop: "receivingOrderData",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "订单接单数量",
            prop: "planNum",
            type: "input",
          },
          {
            label: "订单接单金额",
            prop: "packageAcceptanceAmount",
            type: "input",
          },
          {
            label: "订单验收数量",
            prop: "resultNum",
            type: "input",
          },
          // {
          //   label: "订单合同",
          //   prop: "protocolFile",
          //   type: "input",
          // },
          {
            label: "支付方式",
            prop: "payType",
            type: "select",
            dicData: [
              {
                label: "积分",
                value: 1,
              },
              {
                label: "非积分",
                value: 2,
              },
            ],
          },
        ],
      },
      form: {},
      crudForm: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      crudOption: {
        height: "auto",
        calcHeight: 135,
        title: "会员订单明细",
        tip: false,
        searchShow: true,
        searchMenuPosition: "right",
        border: true,
        index: true,
        addBtn: false,
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        menu: false,
        searchLabelWidth: "120",
        column: [
          {
            label: "编码",
            prop: "codeNumber",
            type: "input",
          },
          {
            label: "客户名称",
            prop: "entrustedCompanyName",
            type: "input",
            span: 24,
          },
          {
            label: "业务人员",
            prop: "baseEmployeeName",
            type: "input",
          },
          {
            label: "会员姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "所属单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "所属部门",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "产品名称",
            prop: "baseProductName",
            type: "input",
            hide: false,
          },
          {
            label: "产品线",
            prop: "baseProductLine",
            type: "input",
            hide: false,
          },

          {
            label: "提交时间",
            prop: "submitTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "初审时间",
            prop: "approvalDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "初审状态",
            prop: "approvalStatus",
            type: "select",
            dicData: [
              {
                label: "待审核",
                value: 0,
              },
              {
                label: "通过",
                value: 1,
              },
              {
                label: "驳回",
                value: 2,
              },
            ],
          },
          {
            label: "复审时间",
            prop: "confirmDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },

          {
            label: "复审状态",
            prop: "confirmStatus",
            type: "select",

            dicData: [
              {
                label: "待验收",
                value: 0,
              },
              {
                label: "通过",
                value: 1,
              },
              {
                label: "驳回",
                value: 2,
              },
            ],
          },
        ],
      },
      data: [],
      orgData: [], //业务部门id
      id: "",
      actType: 8,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.caseCollectionReport_add, false),
        viewBtn: this.vaildData(
          this.permission.caseCollectionReport_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.caseCollectionReport_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.caseCollectionReport_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.id = this.$route.params.id;

    this.getDetail();
  },
  methods: {
    toView(id) {
      if (this.actType == 1) {
        this.$router.push({
          path: `/meeting/detail/${id}`,
        });
      } else if (this.actType == 2) {
        if (this.form.letureType == 1) {
          this.$router.push({
            path: `/lecture_1/detail/${id}`,
          });
        } else if (this.form.letureType == 2) {
          this.$router.push({
            path: `/lecture_2/detail/${id}`,
          });
        } else if (this.form.letureType == 3) {
          this.$router.push({
            path: `/lecture_3/detail/${id}`,
          });
        } else if (this.form.letureType == 4) {
          this.$router.push({
            path: `/lecture_4/detail/${id}`,
          });
        }
      } else if (this.actType == 8) {
        this.$router.push({
          path: `/caseCollectionReport/detail/${id}`,
        });
      } else if (this.actType == 5) {
        this.$router.push({
          path: `/medicationFeedbackReport/detail/${id}`,
        });
      } else if (this.actType == 4) {
        this.$router.push({
          path: `/clinicalresearchreport/detail/${id}`,
        });
      }
      else if (this.actType == 6) {
        this.$router.push({
          path: `/professionalreviewreport/detail/${id}`,
        });
      }
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.orderDoctorId = this.id;
      if (this.actType == 1) {
        params.doctorId = this.form.doctorId;
        params.taskPlanId = this.form.taskPlanId;
        getMeetingList(
          page.currentPage,
          page.pageSize,
          Object.assign(params, this.query)
        ).then((res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      } else if (this.actType == 2) {
        getLectureList(
          page.currentPage,
          page.pageSize,
          Object.assign(params, this.query)
        ).then((res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
      if (this.actType == 8) {
        getList(
          page.currentPage,
          page.pageSize,
          Object.assign(params, this.query)
        ).then((res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      } else if (this.actType == 5) {
        getMedicationFeedbackReportList(
          page.currentPage,
          page.pageSize,
          Object.assign(params, this.query)
        ).then((res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      } else if (this.actType == 4) {
        getClinicalResearchReportlist(
          page.currentPage,
          page.pageSize,
          Object.assign(params, this.query)
        ).then((res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      } else if (this.actType == 6) {
        getProfessionalReviewReportList(
          page.currentPage,
          page.pageSize,
          Object.assign(params, this.query)
        ).then((res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    },
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    //去企业详情
    toOrderEntrustedView() {
      this.$router.push({
        path: `/orderentrusted/detail/${this.form.orderEntrustedId}`,
      });
    },
    //去计划详情
    toActivityPlan() {
      if (this.form.actType == 1) {
        this.$router.push({
          path: `/activityPlan/meetingView/${this.form.taskPlanId}`,
        });
      } else if (this.form.actType == 2) {
        this.$router.push({
          path: `/activityPlan/lectureView/${this.form.taskPlanId}`,
        });
      } else if (this.form.actType == 4) {
        this.$router.push({
          path: `/activityPlan/clinicalResearchView/${this.form.taskPlanId}`,
        });
      } else if (this.form.actType == 5) {
        this.$router.push({
          path: `/activityPlan/medicationFeedbackView/${this.form.taskPlanId}`,
        });
      } else if (this.form.actType == 8) {
        this.$router.push({
          path: `/activityPlan/caseCollectionView/${this.form.taskPlanId}`,
        });
      } else if (this.form.actType == 6) {
        this.$router.push({
          path: `/activityPlan/professionalReviewView/${this.form.taskPlanId}`,
        });
      }
    },
    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },

    // 获取详情
    getDetail() {
      getDetail(this.id).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            data.data.orderStatus =
              data.data.orderStatus == 1
                ? "已接单"
                : data.data.orderStatus == 2
                ? "待验收"
                : data.data.orderStatus == 3
                ? "待结算"
                : data.data.orderStatus == 4
                ? "已完成"
                : "已作废";
            data.data.packageAcceptanceAmount = parseFloat(
              data.data.taskUnitPrice * data.data.planNum
            );
            this.form = data.data;
            this.actType = this.form.actType;
            console.log(this.actType);
            if (this.actType == 6) {
              this.crudOption.column[6].hide = true;
              this.crudOption.column[7].hide = true;
              this.option.column.splice(4, 2);
            } else {
              this.crudOption.column[6].hide = false;
              this.crudOption.column[7].hide = false;
            }

            this.onLoad(this.page);
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
  },
};
</script>

<style></style>
