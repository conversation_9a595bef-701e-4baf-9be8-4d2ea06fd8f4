<template>
  <div>
    <basic-container class="el-card__body"
      ><el-page-header @back="goBack" :content="$route.name"></el-page-header>
      <div class="button">
        <el-button
          type="primary"
          v-if="permission.handleApply_handleApplyInfo"
          @click="handleApplyInfo()"
          >刷新状态</el-button
        >
        <el-button
          type="primary"
          v-if="form.rtnflg == 'S' && permission.handleApply_returnHandleApply"
          @click="returnHandleApply()"
          >支付退回</el-button
        >
      </div>
    </basic-container>
    <basic-container>
      <!--企业信息-->
      <avue-form
        ref="form"
        :option="option"
        v-model="form"
        v-if="!validatenull(form.accnam)"
        @submit="submit"
      >
        <template slot="settlementCode">
          <div
            class="to-view"
            @click="toOrderDoctorSettlement(form.settlementId)"
            v-if="form.settlementCode"
          >
            <a readonly>
              {{ form.settlementCode }}
            </a>
          </div>
          <div v-else>无</div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
      <div style="height: 50px"></div>
      <!-- 代发明细 -->
      <div style="width: 95%; margin: 0 auto">
        <avue-crud
          ref="crud1"
          :option="handleApplyInfoOption"
          :data="handleApplyInfoList"
          @refresh-change="refreshChange"
          @current-change="currentChange"
          @size-change="sizeChange"
        >
        </avue-crud>
      </div>
      <div style="height: 50px"></div>
      <!-- 代发状态记录 -->
      <div style="width: 95%; margin: 0 auto">
        <avue-crud
          ref="crud2"
          :option="handleStatusRecordOption"
          :data="handleStatusRecordList"
          @refresh-change="refreshChange"
          @current-change="currentChange"
          @size-change="sizeChange"
        >
        </avue-crud>
      </div>
      <div style="height: 150px"></div>
    </basic-container>
  </div>
</template>

<script>
import {
  getDetail,
  handleApplyInfo,
  returnHandleApply,
} from "@/api/handleApply/handleApply";
//代发明细
import { getList as getHandleApplyInfoList } from "@/api/handleApplyInfo/handleApplyInfo";
import handleApplyInfoOption from "@/const/handleApplyInfo/handleApplyInfo";
// 代发状态记录
import { getList as getHandleStatusRecordList } from "@/api/handleStatusRecord/handleStatusRecord";
import handleStatusRecordOption from "@/const/handleStatusRecord/handleStatusRecord";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      id: "",
      activeName: "1",
      form: {},
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        column: [
          {
            label: "经办单号",
            prop: "yurref",
            type: "input",
          },
          {
            label: "经办类型",
            prop: "handleType",
            type: "select",
            dicData: [
              {
                label: "首经办",
                value: 1,
              },
              {
                label: "再经办",
                value: 2,
              },
            ],
          },
          {
            label: "订单结算单号",
            prop: "settlementCode",
            type: "input",
          },
          {
            label: "支付总金额",
            prop: "ttlamt",
            type: "input",
          },
          {
            label: "总笔数",
            prop: "ttlcnt",
            type: "input",
          },
          {
            label: "操作状态",
            prop: "state",
            type: "select",
            dicData: [
              {
                label: "制单",
                value: 1,
              },
              {
                label: "经办中",
                value: 2,
              },
              {
                label: "完成",
                value: 3,
              },
              {
                label: "作废",
                value: 4,
              },
            ],
          },
          {
            label: "请求状态",
            prop: "reqsta",
            type: "select",
            dicData: [
              {
                label: "数据接收中",
                value: "OPR",
              },
              {
                label: "等待审批",
                value: "AUT",
              },
              {
                label: "终审完毕",
                value: "NTE",
              },
              {
                label: "银行人工审批",
                value: "APW",
              },
              {
                label: "可疑",
                value: "WRF",
              },
              {
                label: "银行处理中",
                value: "BNK",
              },
              {
                label: "完成",
                value: "FIN",
              },
            ],
          },
          {
            label: "结果状态",
            prop: "rtnflg",
            type: "select",
            dicData: [
              {
                label: "成功",
                value: "S",
              },
              {
                label: "失败",
                value: "F",
              },
              {
                label: "撤销",
                value: "C",
              },
              {
                label: "企业过期不审批",
                value: "D",
              },
              {
                label: "企业审批否决",
                value: "R",
              },
              {
                label: "部分成功",
                value: "P",
              },
              {
                label: "支付退回",
                value: "T",
              },
              {
                label: "失败再经办",
                value: "Z",
              },
            ],
          },
          {
            label: "期望日期",
            prop: "eptdat",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "原经办单号",
            prop: "parentId",
            type: "input",
          },
          {
            label: "用途",
            prop: "nusage",
            type: "input",
          },
          {
            label: "审核状态",
            prop: "approvalStatus",
            type: "select",
            dicData: [
              {
                label: "待提交",
                value: -1,
              },
              {
                label: "待审批",
                value: 0,
              },
              {
                label: "通过",
                value: 1,
              },
              {
                label: "驳回",
                value: 2,
              },
            ],
          },
          {
            label: "审核时间",
            prop: "approvalTime",
            type: "input",
          },
          {
            label: "审核备注",
            prop: "approvalRemark",
            type: "input",
          },
        ],
      },
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      // 代发明细
      handleApplyInfoOption: handleApplyInfoOption,
      handleApplyInfoList: [],
      // 代发状态记录
      handleStatusRecordOption: handleStatusRecordOption,
      handleStatusRecordList: [],
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
    this.getHandleApplyInfoList(this.page);
    this.getHandleStatusRecordList(this.page);
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    //代发明细
    selectionClear() {
      this.selectionList = [];
      if (this.activeName == "2") {
        this.$refs.crud1.toggleSelection();
      } else {
        this.$refs.crud2.toggleSelection();
      }
    },
    refreshChange() {
      if (this.activeName == "2") {
        this.getHandleApplyInfoList(this.page);
      } else {
        this.getHandleStatusRecordList(this.page);
      }
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      if (this.activeName == "2") {
        this.getHandleApplyInfoList(this.page);
      } else {
        this.getHandleStatusRecordList(this.page);
      }
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      if (this.activeName == "2") {
        this.getHandleApplyInfoList(this.page);
      } else {
        this.getHandleStatusRecordList(this.page);
      }
    },
    getHandleApplyInfoList(page, params = {}) {
      this.loading = true;
      params.handleId = this.id;
      getHandleApplyInfoList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.handleApplyInfoList = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    //代发明细end
    // 代发状态记录
    getHandleStatusRecordList(page, params = {}) {
      this.loading = true;
      params.handleId = this.id;
      getHandleStatusRecordList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.handleStatusRecordList = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    // 代发状态记录end

    handleClick(tab) {
      this.page.pageSize = 10;
      this.page.currentPage = 1;
      this.page.total = 0;
      //代发明细
      if (tab.name == "2") {
        this.getHandleApplyInfoList(this.page);
      }
      //代发状态记录
      if (tab.name == "3") {
        this.getHandleStatusRecordList(this.page);
      }
    },

    toOrderDoctorSettlement(id) {
      this.$router.push({
        path: `/orderdoctosettlement/detail/${id}`,
      });
    },

    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    // 刷新状态
    handleApplyInfo() {
      handleApplyInfo(this.id).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    // 支付退回
    returnHandleApply() {
      this.$confirm("确定支付退回?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return returnHandleApply(this.id);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getDetail();
        });
    },
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}
.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
