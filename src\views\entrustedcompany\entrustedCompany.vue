<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="primary"
          v-if="permission.entrustedCompany_demanderRegistration"
          @click="toAdd()"
          >企业注册</el-button
        >
      </template>
      <template slot="auditStatus" slot-scope="{ row }">
        <el-tag v-if="row.auditStatus == 1" type="info" size="small"
          >未认证</el-tag
        >
        <el-tag v-if="row.auditStatus == 2" type="warning" size="small"
          >待审核</el-tag
        >
        <el-tag v-if="row.auditStatus == 3" type="success" size="small"
          >已认证</el-tag
        >
        <el-tag v-if="row.auditStatus == 4" type="danger" size="small"
          >已驳回</el-tag
        >
      </template>
      <template slot="esignStatus" slot-scope="{ row }">
        <el-tag v-if="row.esignStatus == 1" type="info" size="small"
          >未开通</el-tag
        >
        <el-tag v-if="row.esignStatus == 2" type="success" size="small"
          >已开通</el-tag
        >
        <el-tag v-if="row.esignStatus == 3" type="warning" size="small"
          >已过期</el-tag
        >
        <el-tag v-if="row.esignStatus == 4" type="danger" size="small"
          >已停用</el-tag
        >
      </template>
      <template slot="enable" slot-scope="{ row }">
        <el-switch
          v-if="permission.entrustedCompany_edit"
          :value="row.enable == 1 ? true : false"
          active-color="#67C23A"
          inactive-color="#cacdd4"
          @change="enable(row)"
        >
        </el-switch>
        <span v-else>{{ row.enable == 1 ? "启用" : "停用" }}</span>
      </template>
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button
          :size="size"
          type="text"
          v-if="permission.entrustedCompany_view"
          @click="toView(row)"
          >查看</el-button
        >

        <el-button
          :size="size"
          type="text"
          v-if="permission.entrustedCompany_edit && row.auditStatus != 2"
          @click="toEdit(row)"
          >编辑</el-button
        >
        <el-button
          :size="option.size"
          type="text"
          v-if="permission.entrustedCompany_delete && row.auditStatus == 4"
          @click="rowDel(row)"
          >删除</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  stopEsignAccount,
  enable,
} from "@/api/entrustedcompany/entrustedCompany";
import option from "@/const/entrustedcompany/entrustedCompany";
import { mapGetters } from "vuex";

export default {
  data() {
    var checkNum = (rule, value, callback) => {
      if (isNaN(value)) {
        callback(new Error("请输入数字值"));
      } else {
        callback();
      }
    };
    return {
      optionAudit: {
        submitText: "确定",
        span: 24,
        column: [
          {
            label: "平台税率",
            prop: "platformFee",
            // maxlength: 2,
            rules: [
              {
                required: true,
                message: "请输入平台税率",
                trigger: ["blur", "change"],
              },
              {
                validator: checkNum,
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },
      // 弹框标题
      title: "",
      // 是否展示弹框
      box: false,
      // 是否显示查询
      search: true,
      // 加载中
      loading: true,
      // 是否为查看模式
      view: false,
      // 查询信息
      query: {},
      // 分页信息
      page: {
        currentPage: 1,
        pageSize: 10,
        total: 40,
      },
      // 表单数据
      form: {},
      // 选择行
      selectionList: [],
      // 表单配置
      option: option,
      // 表单列表
      data: [],
    };
  },
  mounted() {
    this.init();
  },
  computed: {
    ...mapGetters(["permission"]),
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    init() {},
    searchHide() {
      this.search = !this.search;
    },
    searchChange(params, done) {
      this.query = params;
      //注册时间
      if (!this.validatenull(params.createTime)) {
        this.query.registerStart = params.createTime[0];
        this.query.registerEnd = params.createTime[1];
        delete this.query.createTime;
      }
      //审核时间
      if (!this.validatenull(params.auditTime)) {
        this.query.auditTimeStart = params.auditTime[0];
        this.query.auditTimeEnd = params.auditTime[1];
        delete this.query.auditTime;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleSubmit() {
      if (!this.form.id) {
        add(this.form).then(() => {
          this.box = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      } else {
        update(this.form).then(() => {
          this.box = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
      }
    },
    handleAdd() {
      this.title = "新增";
      this.form = {};
      this.box = true;
    },
    toAdd() {
      this.$router.push({
        path: `/entrustedcompany/add/`,
      });
    },
    toView(row) {
      this.$router.push({
        path: `/entrustedcompany/detail/${row.id}/1`,
      });
    },

    toEdit(row) {
      this.$router.push({
        path: `/entrustedcompany/edit/${row.id}`,
      });
    },
    handleEdit(row) {
      this.title = "编辑";
      this.box = true;
      getDetail(row.id).then((res) => {
        this.form = res.data.data;
      });
    },
    handleView(row) {
      this.title = "查看";
      this.view = true;
      this.box = true;
      getDetail(row.id).then((res) => {
        this.form = res.data.data;
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.selectionClear();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 停用或启用印章
    stopEsignAccount(status) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList.length > 1) {
        this.$message.error("只能选择一条数据");
        return;
      }
      stopEsignAccount(this.ids, status).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.onLoad(this.page);
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    // 启用或停用
    enable(row) {
      this.$confirm(`本操作将更新此数据状态，是否继续?`, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return enable(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    beforeClose(done) {
      done();
      this.form = {};
      this.view = false;
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      // this.$refs["crud"].clearSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-pagination {
  margin-top: 20px;
}
</style>
