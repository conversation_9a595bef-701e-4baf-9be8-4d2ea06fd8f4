<template>
  <div style="overflow-y: auto; height: 100vh">
    <basic-container>
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="1">
          <avue-form
            v-if="!validatenull(form)"
            ref="form"
            :option="option"
            v-model="form"
            @submit="submit"
          >
            <!-- 问卷 -->
            <template slot-scope="{}" slot="researchData">
              <div
                class="researchData questionnaire"
                v-for="(item, index) in researchData.attrs"
                :key="index"
              >
                <div v-if="item.attrType == 1">
                  <div class="researchData-title">
                    <el-tag>单选</el-tag>
                    <div style="margin-left: 15px">
                      {{ index + 1 }}.{{ item.attrName }}
                    </div>
                  </div>
                  <el-radio-group v-model="item.radio">
                    <el-radio
                      :disabled="true"
                      v-for="(item2, index2) in item.options"
                      :label="index2"
                      :key="item2"
                      >{{ item2 }}</el-radio
                    >
                  </el-radio-group>
                </div>
                <div v-if="item.attrType == 2">
                  <div class="researchData-title">
                    <el-tag>多选</el-tag>
                    <div style="margin-left: 15px">
                      {{ index + 1 }}.{{ item.attrName }}
                    </div>
                  </div>
                  <el-checkbox-group v-model="item.checkList">
                    <el-checkbox
                      :disabled="true"
                      v-for="item3 in item.options"
                      :key="item3"
                      :label="item3"
                    ></el-checkbox>
                  </el-checkbox-group>
                </div>
                <div v-if="item.attrType == 3">
                  <div class="researchData-title">
                    <el-tag>问答题</el-tag>
                    <div style="margin-left: 15px">
                      {{ index + 1 }}.{{ item.attrName }}
                    </div>
                  </div>
                  <el-input
                    :disabled="true"
                    type="textarea"
                    :rows="2"
                    v-model="item.attrNode"
                  >
                  </el-input>
                </div>
              </div>
            </template>
          </avue-form>
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>
        <el-tab-pane label="活动会员" name="2">
          <avue-crud
            :option="doctorOption"
            :data="doctorList"
            @refresh-change="getDetail"
            ref="doctor"
          >
            <template slot-scope="{ row }" slot="doctorName">
              <div class="to-view" @click="toViewDoctor()">
                <a readonly>
                  {{ row.doctorName }}
                </a>
              </div>
            </template>
            <template slot="doctorAgreement" slot-scope="{ row }">
              <div style="cursor: pointer" v-if="row.doctorAgreement">
                <el-tag @click="previewCoursework(row.doctorAgreement)"
                  >预览合同</el-tag
                >
              </div>
              <div v-else>无</div>
            </template>
            <template slot="agreementStatus" slot-scope="{ row }">
              <el-tag v-if="row.agreementStatus == 0" size="small"
                >待签署</el-tag
              >
              <el-tag
                v-if="row.agreementStatus == 1"
                type="success"
                size="small"
                >同意</el-tag
              >
              <el-tag v-if="row.agreementStatus == 2" type="danger" size="small"
                >拒绝</el-tag
              >
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/clinicalresearchreport/clinicalResearchReport";
import { mapGetters } from "vuex";
import { Base64 } from "js-base64";
export default {
  props: {
    sfeId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      activeName: "1",
      radio: 3,
      id: "",
      form: {},
      researchData: [],
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "基本信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "计划订单编号",
                prop: "code",
                type: "input",
              },
              {
                label: "编码",
                prop: "codeNumber",
                type: "input",
              },
              {
                label: "客户名称",
                prop: "entrustedCompanyName",
                type: "input",
              },

              {
                label: "业务人员名称",
                prop: "baseEmployeeName",
                type: "input",
              },
              {
                label: "代表部门名称",
                prop: "baseDepartmentName",
                type: "input",
              },
            ],
          },
          {
            label: "问卷信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                labelPosition: "top",
                label: "",
                prop: "researchData",
                type: "input",
              },
            ],
          },

          {
            label: "初审结果",
            arrow: true,
            prop: "group3",
            column: [
              {
                label: "提交时间",
                prop: "submitTime",
                type: "input",
              },
              {
                label: "初审时间",
                prop: "approvalDate",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "初审状态",
                prop: "approvalStatus",
                type: "select",
                dicData: [
                  {
                    label: "待审核",
                    value: 0,
                  },
                  {
                    label: "通过",
                    value: 1,
                  },
                  {
                    label: "驳回",
                    value: 2,
                  },
                ],
              },
              {
                label: "初审说明",
                prop: "approvalRemark",
                type: "input",
              },
              {
                label: "初审人",
                prop: "approvalOfficer",
                type: "input",
              },
            ],
          },
          {
            label: "复审结果",
            arrow: true,
            prop: "group3",
            column: [
              {
                label: "复审时间",
                prop: "confirmDate",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "复审状态",
                prop: "confirmStatus",
                type: "select",
                dicData: [
                  {
                    label: "待验收",
                    value: 0,
                  },
                  {
                    label: "通过",
                    value: 1,
                  },
                  {
                    label: "驳回",
                    value: 2,
                  },
                ],
              },
              {
                label: "复审说明",
                prop: "confirmResult",
                type: "input",
              },
              {
                label: "复审人",
                prop: "confirmer",
                type: "input",
              },
            ],
          },
        ],
      },
      doctorList: [],
      doctorOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "部门",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职称",
            prop: "professional",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "计划数量",
            prop: "planSearchNum",
            type: "input",
          },
          {
            label: "签署状态",
            prop: "agreementStatus",
            type: "select",
            dicData: [
              {
                label: "同意",
                value: 1,
              },
              {
                label: "拒绝",
                value: 2,
              },
              {
                label: "作废",
                value: 3,
              },
            ],
          },
          {
            label: "签署时间",
            prop: "agreementTime",
            type: "input",
          },
          {
            label: "合同协议",
            prop: "doctorAgreement",
            type: "input",
          },
        ],
      },
    };
  },
  created() {
    this.id = this.sfeId;
    console.log(this.sfeId);
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    //去医师详情
    toViewDoctor() {
      this.$router.push({
        path: `/detailSfe/${this.form.doctorId}/1`,
      });
    },
    previewCoursework(i) {
      if (i) {
        window.open(
          "http://1.94.42.192:8886/onlinePreview?url=" +
            encodeURIComponent(Base64.encode(i))
        );
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          this.doctorList = [];
          let obj = {
            doctorName: _this.form.doctorName, //姓名
            hospitalName: _this.form.hospitalName, //单位
            departmentName: _this.form.departmentName, //部门
            professional: _this.form.professional, //职称
            duty: _this.form.duty, //职务
            planSearchNum: _this.form.planSearchNum, //计划数量
            agreementStatus: _this.form.agreementStatus, //签署状态
            agreementTime: _this.form.agreementTime, //签署时间
            doctorAgreement: _this.form.doctorAgreement, //合同协议
          };
          this.doctorList.push(obj);
          let researchData = JSON.parse(res.data.data.researchData);
          this.researchData = researchData;
          this.researchData.attrs.forEach((item) => {
            if (item.attrType == 1) {
              let options = item.attrValue
                .split(",")
                .filter((value) => value != "");
              item.options = options;
              options.forEach((item2, index) => {
                if (item2 == item.attrNode) item.radio = index;
              });
            }
            if (item.attrType == 2) {
              let options = item.attrValue
                .split(",")
                .filter((value) => value != "");
              item.options = options;
              let checkList = [];
              if (item.attrNode) {
                options.forEach((item2) => {
                  item.attrNode
                    .split(",")
                    .filter((value) => value != "")
                    .forEach((item3) => {
                      if (item2 == item3) {
                        checkList.push(item3);
                      }
                    });
                });
              }

              item.checkList = checkList;
            }
          });
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    submit() {},
  },
};
</script>

<style scoped>
.researchData {
  margin-left: 120px;
}
.researchData-title {
  margin: 20px 0;
  display: flex;
  align-items: center;
}

.el-radio {
  line-height: 3 !important;
}
</style>
