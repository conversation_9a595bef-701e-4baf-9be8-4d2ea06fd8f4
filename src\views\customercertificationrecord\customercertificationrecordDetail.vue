<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      <div class="button" v-if="permission.customerCertificationRecord_audit">
        <el-button
          type="success"
          v-if="form1.auditStatus == 1"
          @click="onAudit(2, form1)"
          >通过</el-button
        >
        <el-button
          type="danger"
          v-if="form1.auditStatus == 1"
          @click="onAudit(3, form1)"
          >驳回</el-button
        >
      </div>
    </basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="企业信息" name="1">
          <!--企业信息-->
          <avue-form
            ref="form1"
            v-if="form1.name != ''"
            :option="option1"
            v-model="form1"
            @submit="submit"
          />
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>
        <el-tab-pane label="认证信息" name="2">
          <el-row>
            <el-col :span="8">
              <span class="first-title">身份证信息</span>
              <el-row style="margin-top: 20px" :gutter="20">
                <el-col :span="11">
                  <div>
                    <el-image
                      :src="form1.atta1"
                      :preview-src-list="[form1.atta1]"
                      style="width: 148px; height: 148px"
                      fit="cover"
                    >
                      <template #error>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                          <div>暂无图片</div>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="prompt">身份证正面</div>
                </el-col>
                <el-col :span="11">
                  <div>
                    <el-image
                      style="width: 148px; height: 148px"
                      :src="form1.atta2"
                      fit="cover"
                      :preview-src-list="[form1.atta2]"
                    >
                      <template #error>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                          <div>暂无图片</div>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="prompt">身份证反面</div>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="16">
              <span class="first-title">企业信息</span>
              <el-row :gutter="20" style="margin-top: 20px">
                <el-col :span="5">
                  <div>
                    <el-image
                      style="width: 148px; height: 148px"
                      :src="form1.atta3"
                      :preview-src-list="[form1.atta3]"
                      fit="cover"
                    >
                      <template #error>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                          <div class>暂无图片</div>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="prompt">营业执照</div>
                </el-col>
                <el-col :span="5">
                  <div>
                    <el-image
                      style="width: 148px; height: 148px"
                      :src="form1.atta4"
                      :preview-src-list="[form1.atta4]"
                      fit="cover"
                    >
                      <template #error>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                          <div>暂无图片</div>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="prompt">开户许可证</div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import {
  getDetail,
  audit,
} from "@/api/customercertificationrecord/customerCertificationRecord";
// import { getPersonalServiceProviders } from "@/api/csc/personalserviceprovider";
import { mapGetters } from "vuex";
export default {
  data() {
    var checkNum = (rule, value, callback) => {
      if (isNaN(value)) {
        callback(new Error("请输入数字值"));
      } else {
        callback();
      }
    };
    return {
      optionAudit2: {
        submitText: "完成",
        span: 24,
        column: [
          {
            label: "费率",
            prop: "platFeeRate",
            rules: [
              {
                required: true,
                message: "请输入费率",
                trigger: ["blur", "change"],
              },
              {
                validator: checkNum,
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "审核内容",
            prop: "auditContent",
            type: "textarea",
            maxlength: 200,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入审核内容",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },
      id: "",
      activeName: "1",
      loading: false,
      form1: {
        name: "",
        atta1:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta2:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta3:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta4:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta5:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta6:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
        atta7:
          "https://bhcdn.yaodaitong.cn/upload/20221026/4066db841c92402067c7c1ab967a195e.png",
      },
      option1: {
        disabled: true,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: "170",
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",

            column: [
              {
                label: "全称",
                prop: "name",
              },
              {
                label: "简称",
                prop: "shortName",
              },
              {
                label: "企业类型",
                prop: "regType",
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=reg_type",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },
              {
                label: "统一社会信用代码",
                prop: "socialCreditCode",
              },
              {
                label: "法定代表人",
                prop: "lawPerson",
              },
              {
                label: "法人身份证号",
                prop: "lawPersonIdNum",
                maxlength: 18,
              },
              {
                label: "成立时间",
                prop: "regDate",
                type: "datetime",
                format: "yyyy年MM月dd日",
                valueFormat: "yyyy-MM-dd",
              },
              {
                label: "注册资本/万元",
                prop: "regCapital",
              },

              {
                label: "企业邮箱",
                prop: "mail",
              },

              {
                label: "注册地址",
                prop: "regAddress",
              },
              {
                label: "企业电话",
                prop: "telphone",
                maxlength: 11,
              },
            ],
          },
          {
            label: "业务信息",
            prop: "detailInfo",
            column: [
              {
                label: "经营状态",
                prop: "operatStatus",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=operat_status",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },
              {
                label: "行业性质",
                prop: "industryNature",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=industry_nature",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "string",
              },
              {
                label: "纳税规模",
                prop: "taxScale",
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=tax_scale",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },
              {
                label: "经营范围",
                prop: "bizScope",
                type: "textarea",
              },
            ],
          },
          {
            label: "开票信息",
            prop: "dutyInfo",
            column: [
              {
                label: "开户行",
                prop: "bankName",
              },
              {
                label: "银行账号",
                prop: "bankAccount",
              },
            ],
          },
          {
            label: "联系信息",
            prop: "dutyInfoss",
            column: [
              {
                label: "省份",
                prop: "province",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["city"],
                dicUrl: "/api/blade-system/region/select",
                span: 6,
              },
              {
                label: "城市",
                prop: "city",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["district"],
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 6,
              },
              {
                label: "地区",
                prop: "district",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 6,
              },
              {
                label: "详细地址",
                prop: "address",
                type: "textarea",
              },
            ],
          },
          {
            label: "联系人",
            prop: "comInfo",
            column: [
              {
                label: "联系人/授权代表",
                prop: "linkMan",
              },
              {
                label: "联系方式",
                prop: "linkTel",
                maxlength: 11,
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    goBack() {
      this.$router.$avueRouter.closeTag();
      window.sessionStorage.setItem("customerType", 2);
      this.$router.go(-1);
    },
    onAudit(auditStatus, row) {
      let _this = this;
      if (auditStatus == 2) {
        this.$confirm("确定将该条数据审核通过?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let data = {
              auditStatus: auditStatus,
              id: row.recordId,
              auditContent: "同意",
            };
            return audit(data);
          })
          .then(() => {
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.goBack();
          });
      } else {
        _this.$DialogForm.show({
          title: "驳回审核",
          width: "30%",
          menuPosition: "right",
          option: this.optionAudit2,
          beforeClose: (done) => {
            // this.$message.success("关闭前方法");
            setTimeout(() => {
              done();
            }, 100);
          },
          callback: (res) => {
            res.data.auditStatus = auditStatus;
            res.data.id = row.recordId;
            audit(res.data).then(
              () => {
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
                res.close();
                this.goBack();
              },
              (error) => {
                res.done();
                console.log(error);
              }
            );
          },
        });
      }
    },
    handleClick() {},
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form1 = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    submit() {},
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}

.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
