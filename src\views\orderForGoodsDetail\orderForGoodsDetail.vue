<template>
  <div>
    <basic-container>
      <avue-crud
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :permission="permissionList"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
      >
        <template slot="orderEntrustedCode" slot-scope="{ row }">
          <div
            class="to-view"
            @click="toOrderentrusted(row)"
            v-if="row.orderEntrustedCode"
          >
            <a readonly>
              {{ row.orderEntrustedCode }}
            </a>
          </div>
        </template>
        <template slot="code" slot-scope="{ row }">
          <div class="to-view" @click="toOrderDoctor(row)" v-if="row.code">
            <a readonly>
              {{ row.code }}
            </a>
          </div>
        </template>
        <template slot="doctorName" slot-scope="{ row }">
          <div
            class="to-view"
            @click="toAuthenticationDoctor(row)"
            v-if="row.doctorName"
          >
            <a readonly>
              {{ row.doctorName }}
            </a>
          </div>
        </template>
        <template slot="entrustedCompanyName" slot-scope="{ row }">
          <div
            class="to-view"
            @click="toEntrustedcompany(row)"
            v-if="row.entrustedCompanyName"
          >
            <a readonly>
              {{ row.entrustedCompanyName }}
            </a>
          </div>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import { getList } from "@/api/orderForGoodsDetail/orderForGoodsDetail";
import option from "@/const/orderForGoodsDetail/orderForGoodsDetail";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: false,
        delBtn: false,
        editBtn: false,
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {},
  methods: {
    toOrderDoctor(row) {
      this.$router.push({
        path: `/orderdoctor/detail/${row.id}`,
      });
    },
    toEntrustedcompany(row) {
      this.$router.push({
        path: `/entrustedcompany/detail/${row.entrustedCompanyId}/1`,
      });
    },
    toOrderentrusted(row) {
      this.$router.push({
        path: `/orderentrusted/detail/${row.orderEntrustedId}`,
      });
    },
    toAuthenticationDoctor(row) {
      this.$router.push({
        path: `/authenticationDoctor/detail/${row.doctorId}/1`,
      });
    },
    beforeOpen(done) {
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      if (!this.validatenull(params.receivingOrderData)) {
        this.query.receivingStartData = params.receivingOrderData[0];
        this.query.receivingEndData = params.receivingOrderData[1];
        delete this.query.receivingOrderData;
      }
      //订单下达时间
      if (!this.validatenull(params.realityStartDate)) {
        this.query.realityStartDateStart = params.realityStartDate[0];
        this.query.realityStartDateEnd = params.realityStartDate[1];
        delete this.query.realityStartDate;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;

      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
