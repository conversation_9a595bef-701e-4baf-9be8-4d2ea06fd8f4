export default {
  title: "代发明细",
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  selection: true,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  menu: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "序号",
      prop: "trxseq",
      type: "input",
    },
    {
      label: "姓名",
      prop: "accnam",
      type: "input",
    },
    {
      label: "身份证",
      prop: "idCardNumber",
      type: "input",
    },
    {
      label: "卡号",
      prop: "accnbr",
      type: "input",
    },
    {
      label: "金额",
      prop: "trsamt",
      type: "input",
    },

    {
      label: "状态",
      prop: "stscod",
      type: "select",
      dicData: [
        {
          label: "成功",
          value: "S",
        },
        {
          label: "登记",
          value: "A",
        },
        {
          label: "失败",
          value: "E",
        },
      ],
    },
    {
      label: "失败代码",
      prop: "errcod",
      type: "input",
    },
    {
      label: "失败原因",
      prop: "errtxt",
      type: "input",
    },
    {
      label: "代发经办申请单id",
      prop: "handleId",
      type: "input",
      hide: true,
    },
    {
      label: "他行户口开户行",
      prop: "eacbnk",
      type: "input",
      hide: true,
    },
    {
      label: "他行户口开户地",
      prop: "eaccty",
      type: "input",
      hide: true,
    },

    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
