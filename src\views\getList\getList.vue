<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    ></avue-crud>
  </basic-container>
</template>

<script>
import { getList} from '@/api/getList/getList';
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        menu:false,
        addBtn:false,
        height: 'auto',
        labelWidth: 150,
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '姓名',
            prop: 'serviceProviderName',
            type: 'input',
          },
          {
            label: '身份证号码',
            prop: 'idCardNumber',
            type: 'input',
          },
          {
            label: "实付金额",
            prop: "settlementAmount",
            type: "input",
          },
          {
            label: '代征发票金额',
            prop: 'invoiceAmount',
            type: 'input',
          },
          {
            label: '创建时间',
            prop: 'createTime',
            type: 'date',
            // format: 'yyyy-MM-dd',
            // valueFormat: 'yyyy-MM-dd'
          },

        ]
      },
      data: [],
      id: ''
    };
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.tenderInfo_add, false),
        viewBtn: this.vaildData(this.permission.tenderInfo_view, false),
        delBtn: this.vaildData(this.permission.tenderInfo_delete, false),
        editBtn: this.vaildData(this.permission.tenderInfo_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(',');
    }
  },
  created() {
    this.id = this.$route.params.id;
    this.onLoad(this.page);
  },
  methods: {

    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },

    refreshChange() {
      this.onLoad(this.page);
    },
    onLoad(page) {
      this.loading = true;
      getList(page.currentPage, page.pageSize,  this.id).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style></style>
