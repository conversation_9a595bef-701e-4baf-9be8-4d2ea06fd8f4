export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "用户id",
      prop: "userId",
      type: "input",
    },
    {
      label: "活动业务场景类型",
      prop: "activityType",
      type: "select",
      dicData: [
        {
          label: "推荐注册",
          value: 0,
        },
        {
          label: "会议活动",
          value: 1,
        },
        {
          label: "拜访活动",
          value: 2,
        },
        {
          label: "其他活动",
          value: 3,
        },
      ],
    },
    {
      label: "跳指定页面路径",
      prop: "page",
      type: "input",
    },
    {
      label: "代表ID",
      prop: "employeeId",
      type: "input",
    },
    {
      label: "租户ID",
      prop: "thirdTenantId",
      type: "input",
    },
    {
      label: "会员档案ID",
      prop: "doctorArchivesId",
      type: "input",
    },
    {
      label: "生效状态",
      prop: "validStatus",
      type: "select",
      dicData: [
        {
          label: "已作废",
          value: 0,
        },
        {
          label: "已生效",
          value: 1,
        },
      ],
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
