export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  editBtn: false,
  delBtn: false,
  addBtn: true,
  selection: true,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  menuWidth: 140,
  searchLabelWidth: "120",
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "计划订单编号",
      prop: "code",
      type: "input",
      width: "140",
      search: true,
      searchOrder: 6,
      overHidden: true,
      fixed: true,
    },
    {
      label: "活动名称",
      prop: "name",
      type: "input",
      width: "140",
      search: true,
      searchOrder: 5,
      overHidden: true,
      fixed: true,
    },
    {
      label: "计划开始时间",
      prop: "invitationStartDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "计划结束时间",
      prop: "invitationEndDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "发布人员",
      prop: "baseEmployeeName",
      type: "input",
      search: true,
      searchOrder: 4,
      width: "100",
      overHidden: true,
    },
    // {
    //   label: "产品名",
    //   prop: "baseProductName",
    //   type: "input",
    //   search: true,
    //   searchOrder: 3,
    //   width: "120",
    //   overHidden: true,
    // },
    // {
    //   label: "产品线",
    //   prop: "baseProductLine",
    //   type: "input",
    //   width: "100",
    //   overHidden: true,
    // },
    {
      label: "计划数量",
      prop: "planSearchNum",
      type: "input",
      width: "100",
    },
    {
      label: "计划金额",
      prop: "budgetAmount",
      type: "input",
      width: "100",
      overHidden: true,
    },
    {
      label: "发布时间",
      prop: "releaseTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      width: "120",
      overHidden: true,
      search: true,
      searchRange: true,
      searchOrder: 2,
    },
    {
      label: "委托客户",
      prop: "entrustedCompanyName",
      type: "input",
      width: "120",
      overHidden: true,
    },
    {
      label: "发布企业",
      prop: "fabuqiye",
      type: "input",
      value: "连云港亿辰信息科技有限责任公司",
      width: "120",
      overHidden: true,
    },
    {
      label: "业务部门",
      prop: "baseDepartment",
      search: true,
      type: "tree",
      hide: true,
      dicData: [],
      multiple: true,
      checkStrictly: true,
      leafOnly: false,
      addDisabled: false,
      props: {
        label: "title",
        value: "id",

        overHidden: true,
      },

      overHidden: true,
    },
    {
      label: "计划状态",
      prop: "planStatus",
      type: "select",
      search: true,
      searchOrder: 6,
      dicData: [
        {
          label: "计划中",
          value: 0,
        },
        {
          label: "进行中",
          value: 1,
        },
        {
          label: "已结束",
          value: 2,
        },
      ],
      width: "100",
      overHidden: true,
      fixed: "right",
    },
    {
      label: "计划结束备注",
      prop: "planEndedMark",
      type: "select",
      search: true,
      searchOrder: 6,
      hide: true,
      dicData: [
        {
          label: "活动执行完成",
          value: 1,
        },
        {
          label: "活动超期关闭",
          value: 2,
        },
        {
          label: "客户拒签合同",
          value: 3,
        },
        {
          label: "平台主动关闭活动",
          value: 4,
        },
        {
          label: "推送平台主动关闭",
          value: 5,
        },
      ],
      width: "100",
      overHidden: true,
      fixed: "right",
    },
  ],
};
