function truncateString(str, maxLength) {
  if (str.length > maxLength) {
    return str.slice(0, maxLength) + "...";
  } else {
    return str;
  }
}
function assignment(data) {
  document.getElementById("meeting-title").innerHTML = this.truncateString(
    data.meetingName,
    20
  );
  document.getElementById("meeting-date-left-date").innerHTML = data.date;
  document.getElementById("meeting-date-right-date").innerHTML = data.date;
  document.getElementById("meeting-date-left-time").innerHTML = data.startTime;
  document.getElementById("meeting-date-right-time").innerHTML = data.endTime;
  document.getElementById("meeting-date-time").innerHTML = data.time;
}
function isOnline() {
  const hostname = window.location.hostname;
  const onlineHosts = ["www.yuancycle.com"]; // 线上环境的域名
  const testHosts = ["test.yuancycle.com"];
  const offlineHosts = ["localhost", "*************"]; // 线下环境的域名

  // 检查是否在线上环境
  if (onlineHosts.includes(hostname)) {
    return 1;
  }
  // 检查是否在测试环境
  if (testHosts.includes(hostname)) {
    return 2;
  }
  // 检查是否在线下环境
  if (offlineHosts.includes(hostname)) {
    return 3;
  }

  // 默认情况下，假定是线上环境
  return 1;
}
function getMeetingUrl() {
  let _this = this;
  this.meetingId = this.getUrlParameters().id;
  let url = "";
  // 使用函数
  if (this.isOnline() == 1) {
    url = `https://www.yuancycle.com/api/blade-act/demo/meetingTencent/getMeetingUrl?id=${this.meetingId}`;
  } else if (this.isOnline() == 2) {
    url = `https://test.yuancycle.com/api/blade-act/demo/meetingTencent/getMeetingUrl?id=${this.meetingId}`;
  } else {
    url = `http://************/blade-act/demo/meetingTencent/getMeetingUrl?id=${this.meetingId}`;
  }
  let http = new XMLHttpRequest();
  http.open("get", url);
  http.send();
  http.onreadystatechange = function () {
    if (http.readyState == 4) {
      if (http.status == 200) {
        let data = JSON.parse(http.responseText);
        if (data.meetingStatus == 0) {
          document.getElementById("wrap__ptVFv-container-text").innerHTML =
            "会议未开始";
          document.getElementById("wrap__ptVFv").style.display = "block";
          _this.assignment(data);
        } else if (data.meetingStatus == 1) {
          window.location.replace(data.meetingUrl);
          clearInterval(intervalId);
        } else if (data.meetingStatus == 2) {
          document.getElementById("wrap__ptVFv-container-text").innerHTML =
            "会议已取消";
          document.getElementById("wrap__ptVFv").style.display = "block";
          document.getElementById("join-button").style.display = "none";
          _this.assignment(data);
          clearInterval(intervalId);
        }
      } else {
        let data = {
          meetingName: "会议不存在",
          date: "无",
          startTime: "无",
          endTime: "无",
          time: "无",
        };
        _this.assignment(data);
        document.getElementById("wrap__ptVFv").style.display = "block";
        document.getElementById("wrap__ptVFv-container-text").innerHTML =
          "会议不存在";
        document.getElementById("join-button").style.display = "none";
        clearInterval(intervalId);
      }
    }
  };
}

getMeetingUrl();
// 定时调用接口
var intervalId = setInterval(getMeetingUrl, 60000);

function getUrlParameters() {
  var params = {},
    url = window.location.href,
    query = url.split("?")[1];

  if (query) {
    var queryArr = query.split("&");
    for (var i = 0; i < queryArr.length; i++) {
      var pair = queryArr[i].split("=");
      params[pair[0]] = decodeURIComponent(pair[1]);
    }
  }

  return params;
}
function joinMeeting() {
  document.getElementById("toast").innerHTML = "会议未开始";
  document.getElementById("toast").style.display = "block";
  setTimeout(() => {
    document.getElementById("toast").style.display = "none";
  }, 3000);
}
