export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  addBtn: false,
  labelWidth: 150,
  searchMenuSpan: 6,
  border: true,
  index: true,
  editBtn: false,
  viewBtn: false,
  delBtn: false,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "编号",
      prop: "code",
      type: "input",
      search: true,
      editDisabled: true,
      addDisplay: false, //新增时是否显示
    },
    {
      label: "协议名称",
      prop: "name",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "协议开始日期",
      prop: "serviceStartDate",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "协议结束日期",
      prop: "serviceEndtDate",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "自然人名称",
      prop: "serviceProviderName",
      type: "input",
      searchLabelWidth: "100",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "签约人",
      prop: "signName",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "签约时间",
      prop: "signTime",
      type: "date",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "签约状态",
      prop: "status",
      type: "select",
      dicUrl:
        "/api/blade-system/dict-biz/dictionary?code=protocols_sign_status",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
    },
  ],
};
