export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "服务项目类型id",
      prop: "projectTypeId",
      type: "input",
    },
    {
      label: "项目说明",
      prop: "coopNote",
      type: "input",
      overHidden: true,
    },
    {
      label: "服务项目类型名称",
      prop: "name",
      type: "input",
    },
    {
      label: "对应服务费项目id",
      prop: "chargeAccountId",
      type: "input",
      hide: true,
    },
    {
      label: "对应服务费项目名称",
      prop: "chargeAccountName",
      type: "input",
    },
    {
      label: "服务项目说明",
      prop: "note",
      type: "input",
      overHidden: true,
    },
    {
      label: "项目费用单位",
      prop: "expenceUnit",
      type: "input",
    },
    {
      label: "项目费用单价",
      prop: "expencePrice",
      type: "input",
    },
    {
      label: "项目类型编号",
      prop: "code",
      type: "input",
    },
    {
      label: "Wyn的报告ID",
      prop: "wynSprReportId",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "启用标识",
      prop: "status",
      type: "select",
      dicData: [
        {
          label: "否",
          value: 0,
        },
        {
          label: "是",
          value: 1,
        },
      ],
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
