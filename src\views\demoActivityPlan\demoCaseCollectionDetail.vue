<template>
  <div>
    <basic-container>
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <!--病例征集活动计划-->
          <avue-form
            ref="form"
            :option="option"
            v-model="form"
            @submit="submit"
          >
          </avue-form>
        </el-tab-pane>
        <el-tab-pane label="邀请会员" name="2">
          <avue-crud
            :option="optionDoctor"
            :table-loading="loading"
            :data="data"
            :page.sync="page"
            :permission="permissionList"
            v-model="formDoctor"
            ref="crudDoctor"
            @search-change="searchChange"
            @search-reset="searchReset"
            @selection-change="selectionChange"
            @current-change="currentChange"
            @size-change="sizeChange"
            @refresh-change="refreshChange"
          >
            <template slot="agreementStatus" slot-scope="{ row }">
              <el-tag v-if="row.agreementStatus == 0" size="small"
                >待签署</el-tag
              >
              <el-tag
                v-if="row.agreementStatus == 1"
                type="success"
                size="small"
                >同意</el-tag
              >
              <el-tag v-if="row.agreementStatus == 2" type="danger" size="small"
                >拒绝</el-tag
              >
            </template>
            <template slot="doctorAgreement" slot-scope="{ row }">
              <div style="cursor: pointer" v-if="row.doctorAgreement">
                <el-tag @click="previewPDF(row.doctorAgreement)"
                  >预览合同</el-tag
                >
              </div>
              <div v-else>无</div>
            </template>
            <template slot-scope="{ row }" slot="doctorName">
              <div class="to-view" @click="toViewDoctor(row)">
                <a readonly>
                  {{ row.doctorName }}
                </a>
              </div>
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import { getDoctorList } from "@/api/demoActivityPlan/demoActivityPlan";
import { getDetail } from "@/api/demoCaseCollection/demoCaseCollection";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      inputVisible: false,
      id: "",
      //发起代表
      selectOption: {}, //选中的对象
      options: [],
      selectLoading: false,
      activeName: "1",
      //基础信息
      option: {
        disabled: true,
        labelWidth: "170",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "活动编码",
            prop: "code",
            type: "input",
            placeholder: " ",
          },
          {
            label: "活动名称",
            prop: "name",
            type: "input",
            placeholder: " ",
          },
          {
            label: "计划开始时间",
            prop: "invitationStartDate",
            type: "date",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            placeholder: " ",
          },
          {
            label: "计划结束时间",
            prop: "invitationEndDate",
            type: "date",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            placeholder: " ",
          },
          {
            label: "临床项目类型",
            prop: "projectTypeName",
            type: "input",
            placeholder: " ",
          },
          {
            label: "产品名",
            prop: "baseProductName",
            type: "input",
            placeholder: " ",
          },
          {
            label: "产品线",
            prop: "baseProductLine",
            type: "input",
            placeholder: " ",
          },
          {
            label: "病例征集模板",
            prop: "templateName",
            type: "input",
            dicData: [],
          },
          {
            label: "计划收集份数",
            prop: "planSearchNum",
            type: "input",
            placeholder: " ",
          },
          {
            label: "计划预算金额",
            prop: "budgetAmount",
            type: "input",
            placeholder: " ",
          },
          {
            label: "任务单价",
            prop: "taskUnitPrice",
            type: "input",
          },
          {
            label: "委托客户",
            prop: "entrustedCompanyName",
          },
          {
            label: "客户负责人",
            prop: "baseEmployeeName",
            type: "input",
          },
          {
            label: "负责人部门",
            prop: "baseDepartmentName",
            type: "input",
          },
          {
            label: "活动计划状态",
            prop: "planStatus",
            type: "select",

            dicData: [
              {
                label: "计划中",
                value: 0,
              },
              {
                label: "进行中",
                value: 1,
              },
              {
                label: "已结束",
                value: 2,
              },
            ],
            placeholder: " ",
          },
          {
            label: "计划结束备注",
            prop: "planEndedMark",
            type: "select",
            dicData: [
              {
                label: "活动执行完成",
                value: 1,
              },
              {
                label: "活动超期关闭",
                value: 2,
              },
              {
                label: "客户拒签合同",
                value: 3,
              },
              {
                label: "平台主动关闭活动",
                value: 4,
              },
              {
                label: "推送平台主动关闭",
                value: 5,
              },
            ],
            placeholder: " ",
          },
        ],
      },
      form: {
        projectTypeName: "病例信息征集",
        entrustedCompanyName: "测试需求方",
        planSearchNum: "0",
      },
      //合作项目列表
      projectPlanNameList: [],
      //代表id
      baseEmployeeId: "",

      //邀请会员
      formDoctor: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      loading: false,
      optionDoctor: {
        menu: false,
        searchShowBtn: false,
        columnBtn: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        addBtnIcon: " ",
        cancelBtnIcon: " ",
        saveBtnIcon: " ",
        updateBtnIcon: " ",
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        cellBtn: false,
        cancelBtn: false,
        addBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "部门",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职称",
            prop: "professional",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "计划收集份数",
            prop: "planSearchNum",
            type: "input",
          },
          {
            label: "签署状态",
            prop: "agreementStatus",
            type: "select",
            dicData: [
              {
                label: "待签署",
                value: 0,
              },
              {
                label: "同意",
                value: 1,
              },
              {
                label: "拒绝",
                value: 2,
              },
            ],
          },
          {
            label: "签署时间",
            prop: "agreementTime",
            type: "input",
          },
          {
            label: "合同协议",
            prop: "doctorAgreement",
            type: "input",
          },
        ],
      },
      data: [],
      selectionList: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.inviteDoctor_add, false),
        viewBtn: this.vaildData(this.permission.inviteDoctor_view, false),
        delBtn: this.vaildData(this.permission.inviteDoctor_delete, false),
        editBtn: this.vaildData(this.permission.inviteDoctor_edit, false),
      };
    },
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  methods: {
    toView(row) {
      this.$router.push({
        path: `/authenticationDoctor/detail/${
          row.doctorId ? row.doctorId : 0
        }/1`,
      });
    },

    rowStyle({ row }) {
      if (this.doctorIds.includes(row.doctorId)) {
        return {
          backgroundColor: "#eee",
        };
      }
    },
    //end
    //邀请会员

    //搜索选择项更改
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },

    //选中
    selectionChange(list) {
      this.selectionList = list;
    },
    //取消选中
    selectionClear() {
      this.selectionList = [];
      this.$refs.crudDoctor.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.caseCollectionId = this.id;
      getDoctorList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
        this.$nextTick(() => {
          if (!this.$refs.crudDoctor.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.crudDoctor.refreshTable();
            this.$refs.crudDoctor.doLayout();
          }
        });
      });
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },

    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      window.sessionStorage.setItem("active", 1);
      this.$router.go(-1);
    },

    previewPDF(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },

    submit() {},
    //去会员详情
    toViewDoctor(row) {
      this.$router.push({
        path: `/authenticationDoctor/detail/${row.doctorId}/1`,
      });
    },

    handleClick(tab) {
      if (tab.name == "2") {
        this.onLoad(this.page);
      }
    },
  },
};
</script>

<style></style>
