import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/meeting/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getPage = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/meeting/page",
    method: "post",
    data: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/demo/meeting/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/demo/meeting/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/demo/meeting/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/demo/meeting/submit",
    method: "post",
    data: row,
  });
};
//开始-结束-驳回计划
export const stopMeeting = (id, status) => {
  return request({
    url: "/api/blade-act/demo/meeting/stopMeeting",
    method: "get",
    params: {
      id,
      status,
    },
  });
};

//生成会议二维码
export const createMeetingQRCode = (meetingId, businessType, meetingType, businessClassType) => {
  return request({
    url: "/api/blade-act/demo/meeting/createMeetingQRCode",
    method: "get",
    params: {
      meetingId,
      businessType,
      meetingType,
      businessClassType
    },
  });
};

//会议结束提交审核
export const submitAudit = (id) => {
  return request({
    url: "/api/blade-act/demo/meeting/submitAudit",
    method: "get",
    params: {
      id,
    },
  });
};
//提交审核通用接口
export const submitRecheck = (row) => {
  return request({
    url: "/api/blade-act/demo/meeting/submitRecheck",
    method: "post",
    data: row,
  });
};
//获取个人订单数据
export const meetingOrderList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/meeting/meetingOrderList",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
//重发合同
export const startPlanAgain = (id) => {
  return request({
    url: "/api/blade-act/demo/meeting/startPlanAgain",
    method: "get",
    params: {
      id,
    },
  });
};
