import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/professionalReview/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/demo/professionalReview/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/demo/professionalReview/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/demo/professionalReview/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/demo/professionalReview/submit",
    method: "post",
    data: row,
  });
};

//开始结束计划
export const updateStatus = (id, status) => {
  return request({
    url: "/api/blade-act/demo/professionalReview/updateStatus",
    method: "get",
    params: {
      id,
      status,
    },
  });
};

//重发合同
export const startPlanAgain = (id) => {
  return request({
    url: "/api/blade-act/demo/professionalReview/startPlanAgain",
    method: "get",
    params: {
      id,
    },
  });
};

//获取已经邀请会员
export const getDoctorList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/professionalReviewDoctor/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

//删除已经邀请会员
export const deleteDoctor = (id) => {
  return request({
    url: "/api/blade-act/demo/professionalReviewDoctor/deleteDoctor",
    method: "post",
    params: {
      id,
    },
  });
};
//获取已经选择会员id
export const getExistDoctorIds = (professionalReviewId) => {
  return request({
    url: "/api/blade-act/demo/professionalReviewDoctor/getExistDoctorIds",
    method: "get",
    params: {
      professionalReviewId,
    },
  });
};

//修改计划收集份数
export const savePlanSearchNum = (id, planSearchNum) => {
  return request({
    url: "/api/blade-act/demo/professionalReviewDoctor/savePlanSearchNum",
    method: "post",
    params: {
      id,
      planSearchNum,
    },
  });
};

export const saveList = (data) => {
  return request({
    url: "/api/blade-act/demo/professionalReviewDoctor/saveList",
    method: "post",
    data,
  });
};
