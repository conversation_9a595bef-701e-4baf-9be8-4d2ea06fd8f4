<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
    </basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <avue-form
            ref="form"
            :option="option"
            v-model="form"
            @submit="submit"
            @reset-change="handleReset"
          >
            <!-- 涉及产品名称 -->
            <template slot-scope="{ disabled, size }" slot="baseProductId">
              <el-select
                v-model="form.baseProductId"
                @change="productNameChange"
                @focus="productNameFocus"
                filterable
                remote
                reserve-keyword
                placeholder="请选择产品名"
                :remote-method="remoteMethodProduct"
                :loading="productSelectLoading"
              >
                <el-option
                  v-for="item in productOptions"
                  :key="item.value"
                  :label="item.productName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </template>

            <!-- 业务人员  -->
            <template slot-scope="{ disabled, size }" slot="baseEmployeeId">
              <el-select
                v-model="form.baseEmployeeId"
                @change="baseEmployeeChange"
                @focus="baseEmployeeFocus"
                filterable
                remote
                reserve-keyword
                placeholder="请选择业务人员"
                :remote-method="remoteMethodBaseEmployee"
                :loading="selectLoading"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </template>
          </avue-form>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import {getByEntrustedCompanyId, saveOrUpdate,} from "@/api/demoActivityPlan/demoActivityPlan";
import {getProductList} from "@/api/demoActivityserviceproject/demoActivityServiceProject";
//邀请讲者
import { getList as getMeetingDoctorList } from "@/api/demoMeetingdoctor/demoMeetingDoctor";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      //涉及产品名称
      productSelectOption: {}, //选中的对象
      productOptions: [],
      productSelectLoading: false,
      //业务人员
      selectOption: {}, //选中的对象
      options: [],
      selectLoading: false,
      activeName: "1",
      id: "",
      form: {},
      option: {
        submitIcon: " ",
        emptyIcon: " ",
        disabled: false,
        labelWidth: "150",
        column: [
          {
            label: "活动编码",
            prop: "code",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入活动编码",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "会议名称",
            prop: "name",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入会议名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "会议类型",
            prop: "type",
            type: "input",
            rules: [
              {
                required: true,
                message: "请选择会议类型",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "举办形式",
            prop: "format",
            type: "select",
            dicData: [
              {
                label: "线上",
                value: 1,
              },
              {
                label: "线下",
                value: 2,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择举办形式",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "计划开始时间",
            prop: "invitationStartDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            rules: [
              {
                required: true,
                message: "请选择计划开始时间",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "计划结束时间",
            prop: "invitationEndDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            rules: [
              {
                required: true,
                message: "请选择计划结束时间",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "会议目的",
            prop: "purpose",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入会议目的",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "会议地址",
            prop: "address",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入会议地址",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "会议简介",
            prop: "description",
            type: "input",
          },
          {
            label: "产品名",
            prop: "baseProductId",
            type: "select",
            props: {
              label: "key",
              value: "value",
            },
            dicData: [],
            rules: [
              {
                required: true,
                message: "请选择产品名",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "企业名称",
            prop: "entrustedCompanyId",
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            change: ({ value }) => {
              if (value) {
                this.entrustedCompanyId = value;
                this.initOptions();
              }
            },
            dicUrl: "/api/blade-csc/entrustedCompany/getList",
            rules: [
              {
                required: true,
                message: "请选择企业名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "业务人员",
            prop: "baseEmployeeId",
            type: "select",
            labelTip: "请先选择企业名称",
            rules: [
              {
                required: true,
                message: "请选择业务人员",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "业务部门",
            prop: "baseDepartmentName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请选择代表部门",
                trigger: ["blur", "change"],
              },
            ],
            disabled: true,
          },
        ],
      },
      //企业名称id
      entrustedCompanyId: "",
    };
  },
  created() {
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    //获取业务人员
    initOptions() {
      if (!this.entrustedCompanyId) {
        this.$message({
          type: "error",
          message: "请先选择企业名称!",
        });
        return;
      }
      this.selectLoading = true;
      let query = {
        entrustedCompanyId: this.entrustedCompanyId,
      };
      getByEntrustedCompanyId(query).then((res) => {
        const data = res.data;
        this.options = data.data;
        this.selectLoading = false;
        this.entrustedCompanyChange();
      });
    },
    //业务人员搜索
    remoteMethodBaseEmployee(name) {
      if (name !== "") {
        if (!this.entrustedCompanyId) {
          this.$message({
            type: "error",
            message: "请先选择企业名称!",
          });
          return;
        }
        this.productSelectLoading = true;
        let query = {
          name: name,
          entrustedCompanyId: this.entrustedCompanyId,
        };
        getByEntrustedCompanyId(query).then((res) => {
          const data = res.data;
          this.options = data.data;
          this.selectLoading = false;
        });
      } else {
        this.initOptions();
      }
    },
    baseEmployeeFocus() {
      if (this.options.length == 0) {
        this.initOptions();
      }
    },
    //需求方改变后判断是否还有该业务人员
    entrustedCompanyChange() {
      let serviceProjectList = this.options.filter((item) => {
        return item.id == this.form.baseEmployeeId;
      });
      //代表为空
      if (serviceProjectList.length == 0) {
        this.form.baseEmployeeId = ""; //代表id
        this.form.baseEmployeeName = ""; //代表名字
        this.form.baseEmployeeNumber = ""; //代表工号
        this.form.baseDepartmentName = ""; //部门名
        this.form.baseDepartmentId = ""; //部门id
      }
    },
    //代表姓名更改
    baseEmployeeChange(value) {
      let obj = this.options.filter((item) => {
        return item.id == value;
      });
      this.selectOption = obj[0];
      this.form.baseDepartmentName = this.selectOption.entrustedDeptShort;
    },
    //业务人员end
    //初始化数据
    initProductOptions() {
      this.productSelectLoading = true;
      getProductList().then((res) => {
        const data = res.data;
        this.productOptions = data.data;
        this.productSelectLoading = false;
      });
    },
    //涉及产品名称搜索
    remoteMethodProduct(query) {
      if (query !== "") {
        this.productSelectLoading = true;
        getProductList(query).then((res) => {
          const data = res.data;
          this.productOptions = data.data;
          this.productSelectLoading = false;
        });
      } else {
        this.initProductOptions();
      }
    },
    //获取焦点
    productNameFocus() {
      if (this.productOptions.length == 0) {
        this.initProductOptions();
      }
    },
    //涉及产品名称更改
    productNameChange(value) {
      let obj = this.productOptions.filter((item) => {
        return item.id == value;
      });
      this.productSelectOption = obj[0];
    },
    //涉及产品名称end
    goBack() {
      this.$router.$avueRouter.closeTag();
      window.sessionStorage.setItem("active", 4);
      this.$router.go(-1);
    },
    //清空
    handleReset() {},
    submit(form, done) {
      this.$refs.form.validate((valid) => {
        if (valid) {

          const params = {
            activityPlanType: 1,
            meetingDTO: {
              invitationStartDate: form.invitationStartDate ,
              invitationEndDate: form.invitationEndDate,
              entrustedCompanyName: this.form.$entrustedCompanyId, //委托客户
              baseProductLine: this.productSelectOption.productLine, //产品线
              baseProductName: this.productSelectOption.productName, //产品名
              sfeProductId: this.productSelectOption.systemSfeId, //sfe产品id
              baseProductId: this.productSelectOption.id, //产品d
              baseEmployeeId: this.selectOption.id, //代表id
              baseEmployeeName: this.selectOption.name, //代表名字
              baseEmployeeNumber: this.selectOption.jobNumber, //代表工号
              baseDepartmentId: this.selectOption.entrustedDeptId, //部门id
              baseDepartmentName: form.baseDepartmentName,
              entrustedCompanyId: form.entrustedCompanyId,
              code: form.code,
              name: form.name,
              type: form.type,
              purpose: form.purpose,
              address: form.address,
              description: form.description,
              format: form.format
            } }

          saveOrUpdate(params).then(
            () => {
              this.$message({
                type: "success",
                message: "保存成功!",
              });
              done();
              this.goBack();
            },
            (error) => {
              done();
              console.log(error);
            }
          );
        }
      });
    },
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}

.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
