<template>
  <div>
    <basic-container>
      <avue-form
        ref="form"
        :option="option1"
        v-model="form"
        @submit="saveFeedback"
      />
    </basic-container>
  </div>
</template>

<script>
import { add } from "@/api/feedback/feedback";
// import { getPersonalServiceProviders } from "@/api/csc/personalserviceprovider";
import { mapGetters } from "vuex";
export default {
  data() {
    // 手机号验证
    const validateLinkTel = (rule, value, callback) => {
      let reg = /^(1\d{10})$/;
      if (value === "") {
        callback(new Error("请输入手机号"));
      } else {
        if (!reg.test(value)) {
          callback(new Error("请输入合法的手机号"));
        } else {
          callback();
        }
      }
    };
    return {
      form: {},
      option1: {
        labelWidth: "170",
        submitBtn: true,
        emptyBtn: true,
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",

            column: [
              {
                label: "意见反馈类型",
                prop: "type",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=feedback_type",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
                rules: [
                  {
                    required: true,
                    message: "请选择意见反馈类型",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "联系人姓名",
                prop: "name",
                type: "input",
                rules: [
                  {
                    required: true,
                    message: "请输入联系人姓名",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "联系电话",
                prop: "phone",
                type: "input",
                rules: [
                  {
                    required: true,
                    message: "请输入联系电话",
                    trigger: ["blur", "change"],
                  },
                  {
                    validator: validateLinkTel,
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "内容",
                prop: "content",
                type: "textarea",
                rules: [
                  {
                    required: true,
                    message: "请输入内容",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "反馈材料",
                prop: "feedbackFileUrl",
                type: "upload",
                span: 24,
                listType: "picture-card",
                accept: "image/png, image/jpeg, video/mp4",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    console.log(this.userInfo);
    this.form.phone = this.userInfo.phone ? this.userInfo.phone : "";
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
  },
  methods: {
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    saveFeedback(form, done) {
      console.log(form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          add(form).then(
            (res) => {
              console.log(res);
              this.goBack();
              done();
            },
            () => {
              done();
            }
          );
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
</style>
