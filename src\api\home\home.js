import request from "@/router/axios";
//单项统计（全量数据）
export const getStatisticsSingleItem = () => {
  return request({
    url: "/api/blade-desk/desk/getStatisticsSingleItem",
    method: "get",
  });
};

//按月度分别统计 纳税税目应缴纳额
export const getMonthlyStatisticalTaxitems = () => {
  return request({
    url: "/api/blade-desk/desk/getMonthlyStatisticalTaxitems",
    method: "get",
  });
};

//按月度统计 个人订单总额和纳税总额、企业订单额(折线图)
export const getMonthlyStatisticalLineCharts = () => {
  return request({
    url: "/api/blade-desk/desk/getMonthlyStatisticalLineCharts",
    method: "get",
  });
};

//按服务项目的饼图统计
export const getiServiceItems = () => {
  return request({
    url: "/api/blade-desk/desk/getiServiceItems",
    method: "get",
  });
};
//按会员所在地域汇总
export const getRegionalSummary = () => {
  return request({
    url: "/api/blade-desk/desk/getRegionalSummary",
    method: "get",
  });
};
