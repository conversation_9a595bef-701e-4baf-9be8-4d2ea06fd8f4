<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      :searchslot="true"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ disabled, size }" slot="actStartTimeSearch">
        <el-date-picker
          v-model="startTime"
          type="daterange"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </template>
      <template slot-scope="{ disabled, size }" slot="actEndTimeSearch">
        <el-date-picker
          v-model="endTime"
          type="daterange"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </template>
      <template slot="submitStatus" slot-scope="{ row }">
        <el-tag v-if="row.submitStatus == 0" size="small">待确认</el-tag>
        <el-tag v-if="row.submitStatus == 1" type="success" size="small"
        >已确认</el-tag
        >
        <el-tag v-if="row.submitStatus == 2" type="danger" size="small"
        >已驳回</el-tag
        >
        <el-tag v-if="row.submitStatus == 3" size="small">生成中</el-tag>
        <!--<el-tag v-if="row.submitStatus == 0" type="info" size="small"-->
          <!--&gt;制单</el-tag-->
        <!--&gt;-->
        <!--<el-tag v-if="row.submitStatus == 1" type="warning" size="small"-->
          <!--&gt;待验收</el-tag-->
        <!--&gt;-->
        <!--<el-tag v-if="row.submitStatus == 2" type="success" size="small"-->
          <!--&gt;验收通过</el-tag-->
        <!--&gt;-->
        <!--<el-tag v-if="row.submitStatus == 3" type="danger" size="small"-->
          <!--&gt;验收驳回</el-tag-->
        <!--&gt;-->
      </template>
      <template slot-scope="{ row, size }" slot="menu">
        <el-button
          :size="size"
          v-if="
            permission.resultOrder_delete &&
            (row.submitStatus == 0 || row.submitStatus == 3)
          "
          type="text"
          @click="rowDel(row)"
          >删除</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, add, update, remove } from "@/api/resultorder/resultOrder";
import option from "@/const/resultorder/resultOrder";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      startTime: [], //活动开始日期
      endTime: [], //活动结束日期
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      sponsorHandleStatus: false,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.resultOrder_add, false),
        viewBtn: this.vaildData(this.permission.resultOrder_view, false),
        delBtn: this.vaildData(this.permission.resultOrder_delete, false),
        editBtn: this.vaildData(this.permission.resultOrder_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    beforeOpen(done, type) {
      if (type == "add") {
        this.$router.push({
          path: `/resultorder/add`,
        });
      } else if (type == "view") {
        this.$router.push({
          path: `/resultorder/detail/${this.form.id}`,
        });
        return;
      } else if (type == "edit") {
        this.$router.push({
          path: `/resultorder/edit/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.startTime = [];
      this.endTime = [];
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      delete params.actStartTime;
      delete params.actEndTime;
      if (!this.validatenull(this.startTime)) {
        params.startTimeStart = this.startTime[0];
        params.startTimeEnd = this.startTime[1];
      }
      if (!this.validatenull(this.endTime)) {
        params.endTimeStart = this.endTime[0] + " 23:59:59";
        params.endTimeEnd = this.endTime[1] + " 23:59:59";
      }
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);

      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
