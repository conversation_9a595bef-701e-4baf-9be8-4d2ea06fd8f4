<template>
  <div style="overflow-y: auto; height: 100vh">
    <basic-container>
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="1">
          <avue-form
            ref="form"
            v-if="!validatenull(form)"
            :option="option"
            v-model="form"
            @submit="submit"
          >
            <template slot-scope="{}" slot="codeNumber">
              <el-input
                v-model="form.codeNumber"
                disabled
                placeholder="请输入编码"
              ></el-input>
            </template>
            <template slot-scope="{}" slot="files1">
              <el-row :gutter="20">
                <el-col v-for="(i, index) in form.files1" :key="i.id" :span="4">
                  <el-card class="box-card2">
                    <el-popover
                      placement="top-start"
                      width="200"
                      trigger="hover"
                      :content="form.files1[index].originalName"
                    >
                      <div style="display: flex" slot="reference">
                        <div class="names" style="flex: 1">名称:</div>
                        <div
                          class="names"
                          style="
                            flex: 4;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                          "
                        >
                          {{ i.originalName }}
                        </div>
                      </div>
                    </el-popover>

                    <div
                      v-if="i.type == 'image'"
                      style="
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      <el-image
                        style="margin: 20px 0"
                        class="imgs"
                        :src="i.url"
                        :preview-src-list="[i.url]"
                      >
                      </el-image>
                    </div>
                    <div v-if="i.type == 'video'" class="w-center">
                      <span
                        @click="toopen(i.url)"
                        class="el-icon-video-camera"
                      ></span>
                    </div>
                    <div v-if="i.type == 'file'" class="w-center">
                      <span
                        @click="toopen(i.url)"
                        class="el-icon-notebook-2"
                      ></span>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </template>
            <template slot-scope="{}" slot="files2">
              <el-row :gutter="20">
                <el-col v-for="(i, index) in form.files2" :key="i.id" :span="4">
                  <el-card class="box-card2">
                    <el-popover
                      placement="top-start"
                      width="200"
                      trigger="hover"
                      :content="form.files2[index].originalName"
                    >
                      <div style="display: flex" slot="reference">
                        <div class="names" style="flex: 1">名称:</div>
                        <div
                          class="names"
                          style="
                            flex: 4;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                          "
                        >
                          {{ i.originalName }}
                        </div>
                      </div>
                    </el-popover>

                    <div
                      v-if="i.type == 'image'"
                      style="
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      <el-image
                        style="margin: 20px 0"
                        class="imgs"
                        :src="i.url"
                        :preview-src-list="[i.url]"
                      >
                      </el-image>
                    </div>
                    <div v-if="i.type == 'video'" class="w-center">
                      <span
                        @click="toopen(i.url)"
                        class="el-icon-video-camera"
                      ></span>
                    </div>
                    <div v-if="i.type == 'file'" class="w-center">
                      <span
                        @click="toopen(i.url)"
                        class="el-icon-notebook-2"
                      ></span>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </template>
          </avue-form>
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>
        <el-tab-pane label="活动会员" name="2">
          <avue-crud
            :option="doctorOption"
            :data="doctorList"
            @refresh-change="getDetail"
            ref="doctor"
          >
            <template slot-scope="{ row }" slot="doctorName">
              <div class="to-view" @click="toViewDoctor()">
                <a readonly>
                  {{ row.doctorName }}
                </a>
              </div>
            </template>
            <template slot="doctorAgreement" slot-scope="{ row }">
              <div style="cursor: pointer" v-if="row.doctorAgreement">
                <el-tag @click="previewCoursework(row.doctorAgreement)"
                  >预览合同</el-tag
                >
              </div>
              <div v-else>无</div>
            </template>
            <template slot="agreementStatus" slot-scope="{ row }">
              <el-tag v-if="row.agreementStatus == 0" size="small"
                >待签署</el-tag
              >
              <el-tag
                v-if="row.agreementStatus == 1"
                type="success"
                size="small"
                >同意</el-tag
              >
              <el-tag v-if="row.agreementStatus == 2" type="danger" size="small"
                >拒绝</el-tag
              >
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/caseCollectionReport/caseCollectionReport";
import { mapGetters } from "vuex";
import { Base64 } from "js-base64";
export default {
  props: {
    sfeId: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      activeName: "1",
      id: "",
      form: {},
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "基本信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "计划订单编号",
                prop: "code",
                type: "input",
              },
              {
                label: "编码",
                prop: "codeNumber",
                type: "input",
              },
              {
                label: "客户名称",
                prop: "entrustedCompanyName",
                type: "input",
              },
              {
                label: "业务人员",
                prop: "baseEmployeeName",
                type: "input",
              },
              {
                label: "业务部门",
                prop: "baseDepartmentName",
                type: "input",
              },
            ],
          },
          {
            label: "病例征集信息",
            arrow: true,
            prop: "template",
            column: [
              {
                label: "病例征集模版名称",
                prop: "templateName",
                bind: "template.templateName",
                type: "input",
              },
            ],
          },
          {
            label: "病例分析信息",
            arrow: true,
            prop: "group3",
            column: [
              {
                label: "患者特异分析",
                prop: "specificAnalysis",
                type: "input",
              },
              {
                label: "联合用药建议",
                prop: "medicationAnalysis",
                type: "input",
              },
              {
                label: "对产品的建议",
                prop: "productSuggest",
                type: "input",
              },
              {
                label: "常规检查异常说明",
                prop: "routineInspection",
                type: "input",
              },
              {
                label: "检验检测异常说明",
                prop: "inspectionDetection",
                type: "input",
              },
            ],
          },
          {
            label: "初审结果",
            arrow: true,
            prop: "group4",
            column: [
              {
                label: "提交时间",
                prop: "submitTime",
                type: "input",
              },
              {
                label: "初审时间",
                prop: "approvalDate",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "初审状态",
                prop: "approvalStatus",
                type: "select",
                dicData: [
                  {
                    label: "待审核",
                    value: 0,
                  },
                  {
                    label: "通过",
                    value: 1,
                  },
                  {
                    label: "驳回",
                    value: 2,
                  },
                ],
              },
              {
                label: "初审说明",
                prop: "approvalRemark",
                type: "input",
              },
              {
                label: "初审人",
                prop: "approvalOfficer",
                type: "input",
              },
            ],
          },
          {
            label: "复审结果",
            arrow: true,
            prop: "group5",
            column: [
              {
                label: "复审时间",
                prop: "confirmDate",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "复审状态",
                prop: "confirmStatus",
                type: "select",
                dicData: [
                  {
                    label: "待验收",
                    value: 0,
                  },
                  {
                    label: "通过",
                    value: 1,
                  },
                  {
                    label: "驳回",
                    value: 2,
                  },
                ],
              },
              {
                label: "复审说明",
                prop: "confirmResult",
                type: "input",
              },
              {
                label: "复审人",
                prop: "confirmer",
                type: "input",
              },
            ],
          },
        ],
      },
      doctorList: [],
      doctorOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "部门",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职称",
            prop: "professional",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "计划数量",
            prop: "planSearchNum",
            type: "input",
          },
          {
            label: "签署状态",
            prop: "agreementStatus",
            type: "select",
            dicData: [
              {
                label: "同意",
                value: 1,
              },
              {
                label: "拒绝",
                value: 2,
              },
              {
                label: "作废",
                value: 3,
              },
            ],
          },
          {
            label: "签署时间",
            prop: "agreementTime",
            type: "input",
          },
          {
            label: "合同协议",
            prop: "doctorAgreement",
            type: "input",
          },
        ],
      },
    };
  },
  created() {
    this.id = this.sfeId;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    //去会员详情
    toViewDoctor() {
      this.$router.push({
        path: `/detailSfe/${this.form.doctorId}/1`,
      });
    },
    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("报告为空无法预览");
      }
    },

    // 打开新页面
    toopen(i) {
      window.open(i, "_blank");
    },

    previewCoursework(i) {
      if (i) {
        window.open(
          "http://1.94.42.192:8886/onlinePreview?url=" +
            encodeURIComponent(Base64.encode(i))
        );
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    handleClick() {},
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          this.doctorList = [];
          let obj = {
            doctorName: _this.form.doctorName, //姓名
            hospitalName: _this.form.hospitalName, //单位
            departmentName: _this.form.departmentName, //部门
            professional: _this.form.professional, //职称
            duty: _this.form.duty, //职务
            planSearchNum: _this.form.planSearchNum, //计划数量
            agreementStatus: _this.form.agreementStatus, //签署状态
            agreementTime: _this.form.agreementTime, //签署时间
            doctorAgreement: _this.form.doctorAgreement, //合同协议
          };
          this.doctorList.push(obj);
          this.option.group[2];
          if (this.form.submitType == 0 || this.form.submitType == 2) {
            this.option.group[1].column.length = 1;
            this.form.template.attrs.map((item) => {
              //文本
              if (item.attrType == 3) {
                this.form.template[item.attrCode] = item.attrNode;
                let obj = {
                  label: item.attrName,
                  prop: item.attrCode,
                  type: "input",
                  bind: "template." + item.attrCode,
                };
                this.option.group[1].column.push(obj);
                //多选
              } else if (item.attrType == 2) {
                let dicData = [];
                item.attrValue = item.attrValue
                  .split(",")
                  .filter((attrValue) => attrValue != ""); //问题
                item.attrValue.map((item2) => {
                  let obj = {
                    label: item2,
                    value: item2,
                  };
                  dicData.push(obj);
                });
                //选项
                if (item.attrNode) {
                  this.form.template[item.attrCode] = item.attrNode;
                }
                let obj = {
                  label: item.attrName,
                  prop: item.attrCode,
                  type: "checkbox",
                  dicData,
                  bind: "template." + item.attrCode,
                  className: "questionnaire",
                };
                this.option.group[1].column.push(obj);
              } else if (item.attrType == 1) {
                let dicData = [];
                item.attrValue = item.attrValue
                  .split(",")
                  .filter((attrValue) => attrValue != ""); //问题
                item.attrValue.map((item2) => {
                  let obj = {
                    label: item2,
                    value: item2,
                  };
                  dicData.push(obj);
                });
                //选项
                if (item.attrNode) {
                  this.form.template[item.attrCode] = item.attrNode;
                }
                let obj = {
                  label: item.attrName,
                  prop: item.attrCode,
                  type: "radio",
                  dicData,
                  bind: "template." + item.attrCode,
                  className: "questionnaire",
                };
                this.option.group[1].column.push(obj);
              } else if (item.attrType == 4) {
                //选项
                if (item.attrNode) {
                  let imgList = JSON.parse(item.attrNode);
                  let list = [];
                  imgList.forEach((imgUrl) => {
                    list += imgUrl.url + ",";
                  });
                  this.form.template[item.attrCode] = list;
                }
                let obj = {
                  label: item.attrName,
                  prop: item.attrCode,
                  listType: "picture-card",
                  type: "upload",
                  limit: 5,
                  bind: "template." + item.attrCode,
                  className: "questionnaire",
                };
                this.option.group[1].column.push(obj);
              }
            });
          } else {
            this.option.group[1].column = [];
            this.option.group[1].column.push({
              span: 24,
              label: "病例CRF图片",
              prop: "files1",
              type: "input",
            });
            this.option.group[1].column.push({
              span: 24,
              label: "产品处方凭证",
              prop: "files2",
              type: "input",
            });
          }
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    submit() {},
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}
</style>
