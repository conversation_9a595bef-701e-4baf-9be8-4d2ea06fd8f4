<template>
  <basic-container>
    <avue-crud
      :option="option"
      :search.sync="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          v-if="permission.taxTraceability_export"
          type="primary"
          @click="handleExport(1)"
          >导 出
        </el-button>
      </template>
      <template slot="menuLeft">
        <el-button
          v-if="permission.taxTraceability_export_all"
          type="primary"
          @click="handleExport()"
          >导出全部
        </el-button>
      </template>
      
      <template slot="name" slot-scope="{ row }">
        <div
          class="to-view"
          @click="toAuthenticationDoctor(row)"
          v-if="row.name"
        >
          <a readonly>
            {{ row.name }}
          </a>
        </div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import { getList } from "@/api/authenticationDoctor/authenticationDoctor";
import { option1 } from "@/const/taxTraceability/taxTraceability";
import { mapGetters } from "vuex";
import NProgress from "nprogress";
import func from "@/util/func";

export default {
  data() {
    return {
      customerType: "1",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option1,
      search: {},
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.taxTraceability_add, false),
        viewBtn: this.vaildData(this.permission.taxTraceability_view, false),
        delBtn: this.vaildData(this.permission.taxTraceability_delete, false),
        editBtn: this.vaildData(this.permission.taxTraceability_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    toAuthenticationDoctor(row) {
      this.$router.push({
        path: `/authenticationDoctor/detail/${row.id}/1`,
      });
    },
    beforeOpen(done) {
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      //注册时间
      if (!this.validatenull(params.createTime)) {
        this.query.registerStart = params.createTime[0];
        this.query.registerEnd = params.createTime[1];
        delete this.query.createTime;
      }
      //审核时间
      if (!this.validatenull(params.approvalDate)) {
        this.query.approvalDateStart = params.approvalDate[0];
        this.query.approvalDateEnd = params.approvalDate[1];
        delete this.query.approvalDate;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;

      params.approvalResult = 1;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleExport(approvalResult) {
      const name = func.toStr(this.search.name);
      const idCardNumber = func.toStr(this.search.idCardNumber);
      const phone = func.toStr(this.search.phone);
      //注册时间
      let registerStart = "";
      let registerEnd = "";
      if (!this.validatenull(this.search.createTime)) {
        registerStart = this.search.createTime[0];
        registerEnd = this.search.createTime[1];
      }
      //审核时间
      let approvalDateStart = "";
      let approvalDateEnd = "";
      if (!this.validatenull(this.search.approvalDate)) {
        approvalDateStart = this.search.approvalDate[0];
        approvalDateEnd = this.search.approvalDate[1];
      }
      let values = {
        name,
        idCardNumber,
        phone,
        registerStart,
        registerEnd,
        approvalDateStart,
        approvalDateEnd,
        approvalResult,
      };
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(
          `/api/blade-authenticationDoctor/authenticationDoctor/export-authenticationDoctor?${
            this.website.tokenHeader
          }=${getToken()}`,
          values
        ).then((res) => {
          let fileName =`自然人会员明细`;
          if(approvalResult == 1){
            fileName = `自然人会员已认证明细${dateNow()}.xlsx`
          }else {
            fileName = `自然人会员全部明细${dateNow()}.xlsx`
          }
          downloadXls(res.data, fileName);
          NProgress.done();
        });
      });
    },
  },
};
</script>

<style></style>
