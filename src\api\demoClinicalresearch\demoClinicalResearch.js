import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearch/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearch/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearch/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearch/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearch/submit",
    method: "post",
    data: row,
  });
};
//开始-结束临床调研计划
export const stopClinicalResearch = (id, status) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearch/updateStatus",
    method: "get",
    params: {
      id,
      status,
    },
  });
};

//保存会员
export const saveList = (data) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchDoctor/saveList",
    method: "post",
    data,
  });
};
//获取已经邀请会员
export const getDoctorList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchDoctor/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
//删除已经邀请会员
export const deleteDoctor = (id) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchDoctor/deleteDoctor",
    method: "post",
    params: {
      id,
    },
  });
};
//获取已经选择会员id
export const getExistDoctorIds = (clinicalResearchId) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchDoctor/getExistDoctorIds",
    method: "get",
    params: {
      clinicalResearchId,
    },
  });
};

//修改计划收集份数
export const savePlanSearchNum = (id, planSearchNum) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearchDoctor/savePlanSearchNum",
    method: "post",
    params: {
      id,
      planSearchNum,
    },
  });
};

//重发合同
export const startPlanAgain = (id) => {
  return request({
    url: "/api/blade-act/demo/clinicalResearch/startPlanAgain",
    method: "get",
    params: {
      id,
    },
  });
};
