<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      <div class="button">
        <el-button
          type="success"
          v-if="
            permission.customerCertificationRecord_audit &&
            form1.approvalStatus == 1
          "
          @click="onAudit(2, form1)"
          >通过</el-button
        >
        <el-button
          type="danger"
          v-if="
            permission.customerCertificationRecord_audit &&
            form1.approvalStatus == 1
          "
          @click="onAudit(3, form1)"
          >驳回</el-button
        >
        <el-button
          type="primary"
          v-if="
            permission.customerCertificationRecord_retryFaceSms &&
            form1.approvalStatus == 1
          "
          @click="retryFaceSms()"
          >重发人脸认证短信</el-button
        >
      </div>
    </basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <!--基本信息-->
          <avue-form
            ref="form1"
            v-if="form1.name != ''"
            :option="baseOption"
            v-model="form1"
            @submit="submit"
          >
            <template slot="baseInfoHeader" slot-scope="{ column }">
              <div
                style="
                  position: absolute;
                  font-size: 16px;
                  font-weight: 500;
                  color: rgba(0, 0, 0, 0.85);
                "
              >
                {{ column.label }}
                <i @click.stop="openDialog" class="el-icon-question"></i>
              </div>
            </template>
          </avue-form>
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>

        <el-tab-pane label="认证信息" name="2">
          <!--认证信息-->
          <avue-form
            ref="form1"
            :option="authenticationOption"
            v-model="form1"
            @submit="submit"
            :upload-preview="uploadPreview"
          >
            <template slot="group1Header" slot-scope="{ column }">
              <div
                style="
                  position: absolute;
                  font-size: 16px;
                  font-weight: 500;
                  color: rgba(0, 0, 0, 0.85);
                "
              >
                {{ column.label }}
                <i @click.stop="openDialog" class="el-icon-question"></i>
              </div>
            </template>

            <template slot-scope="{}" slot="faceInfoLabel">
              <span style="position: relative">人脸识别信息&nbsp;&nbsp;
               <i class="el-icon-refresh refreshBtn" style="font-size: 28px;cursor: pointer;z-index: 99" @click="refreshFaceInfo()"></i>
              </span>
              <el-tooltip
                class="item faceTooltip"
                effect="dark"
                :content="
                  form1.faceVerify == 0
                    ? '待认证'
                    : form1.faceVerify == 1
                    ? '认证一致'
                    : '认证不一致'
                "
                placement="top-start"
              >
                <i v-if="form1.faceVerify == 2" class="el-icon-error" style="color: #F56C6C"></i>
                <i v-else class="el-icon-warning"></i>
              </el-tooltip>
            </template>
          </avue-form>
        </el-tab-pane>
      </el-tabs>
      <el-drawer
        title="已认证数据"
        :visible.sync="dialogVisible"
        direction="rtl"
        :before-close="handleClose"
        append-to-body
      >
        <div>
          <avue-form ref="form" :option="dialogOption" v-model="dialogForm" />
        </div>
      </el-drawer>

      <!-- <el-dialog
        title="已认证数据"
        append-to-body
        :visible.sync="dialogVisible"
        width="60%"
      >
      </el-dialog> -->
      <!-- 审核驳回 -->
      <el-dialog
        title="驳回"
        @close="auditForm = {}"
        append-to-body
        :close-on-click-modal="false"
        :visible.sync="auditDialogVisible"
        width="30%"
      >
        <avue-form
          v-if="auditDialogVisible"
          ref="auditForm"
          :option="auditOption"
          v-model="auditForm"
        >
          <template slot-scope="{ disabled, size }" slot="quickReplyList">
            <div class="quickReplyList">
              <el-tag
                style="cursor: pointer"
                :key="tag.id"
                v-for="tag in quickReplyList"
                :disable-transitions="false"
                @click="clickQuickReply(tag)"
              >
                {{ tag.content }}
              </el-tag>
            </div>
          </template>
        </avue-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取 消</el-button>
          <el-button
            :loading="auditLoading"
            type="primary"
            @click="auditClick()"
            >确定</el-button
          >
        </span>
      </el-dialog>
    </basic-container>
    <image-viewer z-index="2000" :initial-index="previewIndex"  v-if="previewSrcList.length > 0" :on-close="closeViewer" :url-list="previewSrcList"/>
  </div>
</template>

<script>
import ImageViewer from 'element-ui/packages/image/src/image-viewer'
import pdf from "vue-pdf";
import {audit, retryFaceSms,} from "@/api/customercertificationrecord/customerCertificationRecord";
import {
  detailByNameOrPhone,
  getCertificationDetail,
  refreshFaceVerify,
} from "@/api/authenticationDoctor/authenticationDoctor";
import {mapGetters} from "vuex";
import {projectQuickReplyList} from "@/api/sysprojectQuickReply/projectQuickReply";

export default {
  components: {
    pdf,
    ImageViewer
  },
  data() {
    return {
      previewSrcList: [],
      previewIndex: 0,
      //已认证数据
      dialogVisible: false,
      dialogForm: {},
      dialogOption: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        border: true,
        index: true,
        dialogClickModal: false,
        column: [
          {
            label: "姓名",
            prop: "name",
          },
          {
            label: "性别",
            prop: "sex",
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=sex",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "手机号码",
            prop: "phone",
          },
          {
            label: "身份证号",
            prop: "idCardNumber",
          },
          {
            label: "医院名称",
            prop: "hospitalName",
          },
          {
            label: "所在科室",
            prop: "departmentName",
          },
          {
            label: "职称",
            prop: "professional",
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=professional",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "职务",
            prop: "duty",
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=duty",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
          },

          {
            label: "所属省",
            prop: "province",
            type: "select",
            props: {
              label: "name",
              value: "code",
            },
            cascader: ["city"],
            dicUrl: "/api/blade-system/region/select",
            span: 12,
          },
          {
            label: "所属市",
            prop: "city",
            type: "select",
            props: {
              label: "name",
              value: "code",
            },
            cascader: ["district"],
            dicFlag: false,
            dicUrl: "/api/blade-system/region/select?code={{key}}",
            span: 12,
          },
        ],
      },

      id: "",
      numPages: 1,
      activeName: "1",
      loading: false,
      form1: { name: "" },
      //审核弹窗
      quickReplyList: [],
      auditLoading: false,
      auditDialogVisible: false,
      auditForm: {},
      auditOption: {
        submitText: "完成",
        span: 24,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "验收说明",
            prop: "auditContent",
            type: "textarea",
            maxlength: 500,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入验收说明",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "快捷回复",
            prop: "quickReplyList",
            type: "input",
          },
        ],
      },

      //end
      baseOption: {
        disabled: true,
        labelWidth: "170",
        submitBtn: false,
        emptyBtn: false,
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",
            column: [
              {
                label: "姓名",
                prop: "name",
              },
              {
                label: "医院名称",
                prop: "hospitalName",
              },
              {
                label: "所在科室",
                prop: "departmentName",
              },
              {
                label: "证件号码",
                prop: "idCardNumber",
              },
              {
                label: "手机号码",
                prop: "phone",
              },
              {
                label: "微信ID",
                prop: "wechatId",
              },
              {
                label: "职称",
                prop: "professional",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=professional",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },
              {
                label: "职务",
                prop: "duty",
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=duty",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },

              {
                label: "性别",
                prop: "sex",
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=sex",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },
              {
                label: "民族",
                prop: "nationality",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=nationality",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },
              {
                label: "籍贯",
                prop: "nativePlace",
                row: true,
              },
              {
                label: "所属省",
                prop: "province",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["city"],
                dicUrl: "/api/blade-system/region/select",
                span: 6,
              },
              {
                label: "所属市",
                prop: "city",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["district"],
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 6,
              },
              {
                label: "所属区",
                prop: "district",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 6,
              },
              {
                label: "联系地址",
                prop: "address",
              },
              {
                label: "毕业院校",
                prop: "graduateSchool",
              },
              {
                label: "最高学历",
                prop: "education",
              },
              {
                label: "主治专长",
                prop: "speciality",
                type: "textarea",
              },
            ],
          },
        ],
      },

      authenticationOption: {
        disabled: true,
        labelWidth: "170",
        submitBtn: false,
        emptyBtn: false,
        group: [
          {
            icon: "el-icon-info",
            label: "身份认证信息",
            prop: "group1",
            column: [
              {
                label: "证件类型",
                prop: "credentialType",
                type: "select",
                control: (val) => {
                  console.log(val);
                  switch (val) {
                    case 1:
                      return {
                        idCardAddress: { display: true }, //身份证地址
                        idCardPositive: { display: true }, //身份证正面
                        idCardNegative: { display: true }, //身份证反面
                        passportPersonal: { display: false }, //海外护照个人资料页
                        passportSign: { display: false }, //海外护照盖章页
                        residencePermit: { display: false }, //港澳台通行证
                      };
                    case 2:
                      return {
                        idCardAddress: { display: false }, //身份证地址
                        idCardPositive: { display: false }, //身份证正面
                        idCardNegative: { display: false }, //身份证反面
                        passportPersonal: { display: false }, //海外护照个人资料页
                        passportSign: { display: false }, //海外护照盖章页
                        residencePermit: { display: true }, //港澳台通行证
                      };
                    case 3:
                      return {
                        idCardAddress: { display: false }, //身份证地址
                        idCardPositive: { display: false }, //身份证正面
                        idCardNegative: { display: false }, //身份证反面
                        passportPersonal: { display: false }, //海外护照个人资料页
                        passportSign: { display: false }, //海外护照盖章页
                        residencePermit: { display: true }, //港澳台通行证
                      };
                    case 4:
                      return {
                        idCardAddress: { display: false }, //身份证地址
                        idCardPositive: { display: false }, //身份证正面
                        idCardNegative: { display: false }, //身份证反面
                        passportPersonal: { display: true }, //海外护照个人资料页
                        passportSign: { display: true }, //海外护照盖章页
                        residencePermit: { display: false }, //港澳台通行证
                      };
                  }
                },
                dataType: "number",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=credential_type",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                row: true,
              },
              {
                label: "证件姓名",
                prop: "name",
                type: "input",
                row: true,
              },
              {
                label: "证件号码",
                prop: "idCardNumber",
                type: "input",
                row: true,
              },
              {
                label: "身份证地址",
                prop: "idCardAddress",
                type: "input",
                row: true,
              },
              {
                label: "开户银行",
                prop: "bankName",
                type: "input",
                row: true,
              },
              {
                label: "银行账号",
                prop: "bankAccount",
                type: "input",
                row: true,
              },

              {
                span: 6,
                label: "港澳台通行证或居住证",
                prop: "residencePermit",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "身份证正面",
                prop: "idCardPositive",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "身份证反面",
                prop: "idCardNegative",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "海外护照个人资料页",
                prop: "passportPersonal",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "海外护照盖章页",
                prop: "passportSign",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "银行卡图片",
                prop: "bankImg",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "人脸识别信息",
                prop: "faceInfo",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "身份证签发日期",
                prop: "idCardStartDate",
                type: "input",
                row: true,
              },
              {
                span: 6,
                label: "身份证到期日期",
                prop: "idCardEndDate",
                type: "input",
                row: true,
              },
            ],
          },
          {
            label: "会员资格信息",
            prop: "info",
            column: [
              {
                label: "医师资格证/执业医师证",
                prop: "doctorCertificateData",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                span: 24,
              },
            ],
          },
          {
            label: "会员结算方式",
            prop: "info",
            column: [
              {
                label: "结算方式",
                prop: "settleType",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=settle_type",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.activeName = "1";
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    uploadPreview(image,option) {
      const { url, prop } = {...image, ...option}
        if (prop === 'doctorCertificateData') {
            if (this.form1.doctorCertificateData) {
              this.previewSrcList = this.form1.doctorCertificateData.split(',')
              this.previewIndex = this.form1.doctorCertificateData.split(',').indexOf(url);
            }
        } else {
            this.previewSrcList= [this.form1.idCardPositive,this.form1.idCardNegative,this.form1.bankImg, this.form1.faceInfo].filter(src=> src)
            this.previewIndex = this.previewSrcList.indexOf(url);
        }
    },
    closeViewer() {
      this.previewSrcList = []
    },
    openDialog() {
      detailByNameOrPhone(this.form1.name, this.form1.phone).then((res) => {
        if (res.data.success) {
          this.dialogForm = res.data.data;
          this.dialogVisible = true;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    handleClose(done) {
      done();
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      window.sessionStorage.setItem("customerType", 3);
      this.$router.go(-1);
    },
    handleClick(tab) {
      console.log(tab);
    },

    getDetail() {
      let _this = this;
      getCertificationDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form1 = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    //重新发送短信
    retryFaceSms() {
      retryFaceSms(this.form1.id).then(
        () => {
          this.getDetail();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          console.log(error);
        }
      );
    },
    onAudit(auditStatus, row) {
      let _this = this;
      if (auditStatus == 2) {
        this.$confirm("确定将该条数据审核通过?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let data = {
              auditStatus: auditStatus,
              id: row.recordId,
              auditContent: "同意",
            };
            return audit(data);
          })
          .then(() => {
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.goBack();
          });
      } else {
        projectQuickReplyList({ moduleCode: 'customerCertificationRecord' }).then(({ data: res }) => {
          this.quickReplyList = res.data;
          this.auditForm.auditStatus = auditStatus;
          this.auditForm.id = _this.id;
          this.auditDialogVisible = true;
        });
      }
    },
    clickQuickReply(tag) {
      this.auditForm.auditContent = tag.content;
    },
    auditClick() {
      this.$refs.auditForm.validate((valid) => {
        if (valid) {
          this.auditLoading = true;
          audit(this.auditForm).then(
            () => {
              this.auditDialogVisible = false;
              this.auditLoading = false;
              // this.getDetail();
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              this.goBack();
            },
            (error) => {
              console.log(error);
              this.auditLoading = false;
            }
          );
        }
      });
    },
    submit() {},
    refreshFaceInfo() {
      const id= this.$route.params.id;
      refreshFaceVerify(id).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getDetail();
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    }
  },
};
</script>

<style>
.avue-crud__menu {
  min-height: 0 !important;
}

.el-card__body {
  position: relative;
}

.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}

h4 {
  margin: 5px 0 !important;
}

.first-title {
  font-size: 16px !important;
  font-weight: 600;
  color: #333;
}

.prompt {
  width: 148px;
  margin: 5px 0;
  font-size: 14px;
  text-align: center;
}

.image-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fbfdff;
  border: 1px dashed #c0ccda;
  color: #666;
  border-radius: 6px;
  box-sizing: border-box;
  width: 148px;
  height: 148px;
  cursor: pointer;
  line-height: 146px;
  vertical-align: top;
}

.image-slot div {
  font-size: 14px;
  margin: 4px 0;
}
.image-container {
  position: relative;
  display: inline-block;
}

.refreshBtn {
  position: absolute;
  right: -220px;
}
</style>
