module.exports = {
  //路径前缀
  publicPath: "/",
  lintOnSave: true,
  productionSourceMap: false,
  chainWebpack: (config) => {
    //忽略的打包文件
    config.externals({
      vue: "Vue",
      "vue-router": "VueRouter",
      vuex: "Vuex",
      axios: "axios",
      "element-ui": "ELEMENT",
    });
    const entry = config.entry("app");
    entry.add("babel-polyfill").end();
    entry.add("classlist-polyfill").end();
    entry.add("@/mock").end();
  },
  css: {
    extract: { ignoreOrder: true },
  },
  //开发模式反向代理配置，生产模式请使用Nginx部署并配置反向代理
  devServer: {
    port: 1888,
    proxy: {
      "/api": {
        //本地服务接口地址
        target: 'https://test.yuancycle.com/api',
        // target: "http://192.168.3.47", //杜洪志
        ws: true,
        pathRewrite: {
          "^/api": "/",
        },
      },
    },
  },
};
