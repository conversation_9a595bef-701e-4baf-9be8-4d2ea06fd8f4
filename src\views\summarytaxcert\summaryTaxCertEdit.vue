<template>
  <div>
    <basic-container>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div>
          <el-page-header
            @back="goBack"
            :content="$route.name"
          ></el-page-header>
        </div>
      </div>
    </basic-container>
    <basic-container
      ><avue-form
        ref="form"
        v-if="form.code != ''"
        :option="option"
        v-model="form"
        @submit="submit"
      ></avue-form>
      <template v-else> <el-skeleton :rows="10" animated /> </template
    ></basic-container>
  </div>
</template>

<script>
import { getDetail, update } from "@/api/summarytaxcert/summaryTaxCert";
export default {
  data() {
    return {
      id: "", //数据id
      form: {
        invoiceFileLink: "",
      },
      checkDate: "",
      query: {},
      loading: false,
      times: null,
      isEfficient: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option4: {
        submitText: "完成",
        span: 24,
        column: [
          {
            label: "驳回意见",
            prop: "name",
            type: "textarea",
            rules: [
              {
                required: true,
                message: "请输入驳回意见",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      //基础信息
      option: {
        submitIcon: " ",
        emptyIcon: " ",
        emptyBtn: false,
        height: "auto",
        calcHeight: 30,
        // submitBtn: false,
        tip: false,
        labelWidth: "150",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "详情信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "编号",
                prop: "code",
                type: "input",
                disabled: true,
              },
              {
                label: "名称",
                prop: "name",
                search: true,
                type: "input",
                disabled: true,
              },
              {
                label: "金额合计大写",
                prop: "totalAmountUppercase",
                type: "input",
                disabled: true,
              },
              {
                label: "金额合计小写",
                prop: "totalAmountLowercase",
                type: "input",
                disabled: true,
              },
              {
                label: "状态",
                prop: "taxCertStatus",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=tax_cert_status",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                disabled: true,
                dataType: "number",
                slot: true,
                width: 140,
              },
              {
                label: "完税证明附件",
                prop: "taxCertFile",
                type: "upload",
                listType: "picture-card",
                accept: "image/png, image/jpeg",
                dataType: "string",
                limit: 3,
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
            ],
          },
        ],
      },
      data: [],
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail(); //获取详情
  },
  methods: {
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    // 获取数据详情
    getDetail() {
      getDetail(this.id).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            this.form = data.data;
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },

    submit(form, done) {
      update(form).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            this.$message({
              type: "success",
              message: "操作成功",
            });
            done();
            this.goBack();
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
            done();
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
  },
};
</script>

<style></style>
