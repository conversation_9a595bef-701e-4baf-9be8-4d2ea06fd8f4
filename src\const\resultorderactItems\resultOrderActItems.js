export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "服务活动成果总清单id",
      prop: "resultOrderId",
      type: "input",
    },
    {
      label: "注册会员id",
      prop: "doctorId",
      type: "input",
    },
    {
      label: "会员姓名",
      prop: "doctorName",
      type: "input",
      span: 24,
      search: true,
      rules: [
        {
          required: true,
          message: "请输入会员姓名",
          trigger: "blur",
        },
      ],
    },
    {
      label: "活动主键id",
      prop: "actReportId",
      type: "input",
    },
    {
      label: "活动类型",
      prop: "actType",
      type: "input",
    },
    {
      label: "活动费用发票应税科目",
      prop: "actTaxItems",
      type: "input",
    },
    {
      label: "活动标题-标题或宣传产品名称",
      prop: "actName",
      type: "input",
    },
    {
      label: "活动时间-活动验收时间或者活动结束时间",
      prop: "actTime",
      type: "input",
    },
    {
      label: "活动费用-活动单价或活动指定的单次费用",
      prop: "actFee",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
