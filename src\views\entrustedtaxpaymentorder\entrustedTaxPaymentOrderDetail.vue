<template>
  <div>
    <basic-container class="el-card__body">
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
      <div class="button">
        <el-button
          v-if="
            permission.entrustedTaxPaymentOrder_aloneHandle &&
            baseForm.transferButton == 0
          "
          type="primary"
          :loading="aloneHandleLoading"
          @click="aloneHandle()"
          >代征税费支付</el-button
        >
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(baseForm)"
        ref="baseForm"
        :option="baseOption"
        v-model="baseForm"
        @submit="submit"
      >
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
      <div style="height: 50px"></div>
      <!--支付转账经办单-->
      <avue-crud
        :option="option"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :permission="permissionList"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-del="rowDel"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
      >
        <template slot-scope="{ type, size, row, index }" slot="menu">
          <el-button
            v-if="permission.entrustedTaxPaymentOrder_payQuery"
            type="text"
            @click="payQuery(row.yurRef)"
            >更新转账结果</el-button
          >
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {
  getDetail as getBaseDetail,
  aloneHandle,
} from "@/api/entrustedtaxpaymentorder/entrustedTaxPaymentOrder";
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  payQuery,
} from "@/api/transferAccount/transferAccount";
import option from "@/const/transferAccount/transferAccount";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      activeName: "1",
      id: "",
      //基础信息
      baseOption: {
        disabled: true,
        labelWidth: "150",
        submitBtn: false,
        emptyBtn: false,
        title: "支付转账经办单",
        group: [
          {
            label: "基本信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "编码",
                prop: "code",
                type: "input",
              },

              {
                label: "客户名称",
                prop: "enterpriseName",
                type: "input",
                search: true,
                searchLabelWidth: "100",
                rules: [
                  {
                    required: true,
                    message: "请输入客户名称",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "企业结算单编号",
                prop: "settlementCode",
                type: "input",
              },
              // {
              //   label: "企业结算单名称",
              //   prop: "settlementName",
              //   type: "input",
              // },
              {
                label: "合计税率",
                prop: "taxFeeRate",
                type: "input",
                rules: [
                  {
                    required: true,
                    message: "请输入合计税率",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "服务开始日期",
                prop: "actStartTime",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
                search: false,
                searchRange: true,
              },
              {
                label: "服务结束日期",
                prop: "actEndTime",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
                search: false,
                searchRange: true,
              },
              {
                label: "纳税金额",
                prop: "taxFeeAmount",
                type: "input",
                rules: [
                  {
                    required: true,
                    message: "请输入纳税金额",
                    trigger: "blur",
                  },
                ],
              },
              {
                label: "支付状态",
                prop: "payStatus",
                type: "select",
                dicData: [
                  {
                    label: "未支付",
                    value: 1,
                  },
                  {
                    label: "已支付",
                    value: 2,
                  },
                  {
                    label: "支付失败",
                    value: 3,
                  },
                ],
              },
            ],
          },
        ],
      },
      baseForm: {},
      //支付转账经办单
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      aloneHandleLoading: false,
    };
  },
  computed: {
    //支付转账经办单
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.transferAccount_add, false),
        viewBtn: this.vaildData(this.permission.transferAccount_view, false),
        delBtn: this.vaildData(this.permission.transferAccount_delete, false),
        editBtn: this.vaildData(this.permission.transferAccount_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionDoctorList.forEach((ele) => {
        ids.push(ele.doctorId);
      });
      return ids.join(",");
    },
  },
  created() {
    this.id = this.$route.params.id;
    this.init();
  },
  methods: {
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    //代征税费支付
    aloneHandle() {
      this.aloneHandleLoading = true;
      aloneHandle(this.id).then(
        () => {
          this.aloneHandleLoading = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getDetail();
          this.onLoad(this.page);
        },
        () => {
          this.aloneHandleLoading = false;
        }
      );
    },

    init() {
      this.getBaseDetail();
    },
    getBaseDetail() {
      getBaseDetail(this.id).then((res) => {
        let data = res.data;
        this.baseForm = data.data.data;
      });
    },
    submit() {},

    //支付转账经办单
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    payQuery(yurRef) {
      payQuery(yurRef).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.onLoad(this.page);
      });
    },
    beforeOpen(done, type) {
      if (type == "eidt") {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.entrustedTaxPaymentOrderId = this.id;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        if (!this.$refs.crud.gridShow) {
          // myTable是表格的ref属性值
          this.$refs.crud.doLayout();
        }
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}
.sub-button {
  display: flex;
  justify-content: center;
}
.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
