import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-userBlacklist/userBlacklist/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getExistDoctorIds = (current, size, params) => {
  return request({
    url: '/api/blade-userBlacklist/userBlacklist/getExistDoctorIds',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const submitList = (data) => {
  return request({
    url: '/api/blade-userBlacklist/userBlacklist/submitList',
    method: 'post',
    data
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-userBlacklist/userBlacklist/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-userBlacklist/userBlacklist/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-userBlacklist/userBlacklist/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-userBlacklist/userBlacklist/submit',
    method: 'post',
    data: row
  })
}

