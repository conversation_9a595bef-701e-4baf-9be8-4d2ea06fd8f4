import request from '@/router/axios';

export const getListPage = (current, size, params) => {
  return request({
    url: '/api/blade-contentManagement/contentManagement/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-contentManagement/contentManagement/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const getApprovalDetail = (contentId) => {
  return request({
    url: '/api/blade-contentAudit/contentAudit/detail',
    method: 'get',
    params: {
      contentId
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-contentManagement/contentManagement/remove',
    method: 'post',
    data: ids
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-contentManagement/contentManagement/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-contentManagement/contentManagement/submit',
    method: 'post',
    data: row
  })
}

export const audit = (row) => {
  return request({
    url: '/api/blade-contentManagement/contentManagement/audit',
    method: 'post',
    data: row
  })
}

export const updateStatus = (row) => {
  return request({
    url: '/api/blade-contentManagement/contentManagement/updateStatus',
    method: 'post',
    data: row
  })
}

