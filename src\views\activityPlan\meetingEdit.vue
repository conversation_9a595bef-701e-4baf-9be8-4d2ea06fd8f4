<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
    </basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <avue-form
            ref="form"
            v-if="!validatenull(form)"
            :option="option"
            v-model="form"
            @submit="submit"
            @reset-change="handleReset"
          >
          </avue-form>
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>
        <el-tab-pane label="邀请讲者" name="2">
          <!-- 邀请讲者 -->
          <avue-crud
            :page.sync="meetingDoctorPage"
            :option="meetingDoctorOption"
            :data="meetingDoctorList"
            @current-change="currentChangeMeetingDoctor"
            @size-change="sizeChangeMeetingDoctor"
            @refresh-change="refreshChangeMeetingDoctor"
          >
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import { getDetail, update } from "@/api/meeting/meeting";
//邀请讲者
import { getList as getMeetingDoctorList } from "@/api/meetingdoctor/meetingDoctor";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      activeName: "1",
      id: "",
      form: {},
      option: {
        submitIcon: " ",
        emptyIcon: " ",
        disabled: false,
        labelWidth: "150",
        column: [
          {
            label: "计划编号",
            prop: "code",
            type: "input",
          },
          {
            label: "会议名称",
            prop: "name",
            type: "input",
          },
          {
            label: "会议类型",
            prop: "type",
            type: "input",
          },
          {
            label: "举办形式",
            prop: "format",
            type: "select",
            dicData: [
              {
                label: "线上",
                value: 1,
              },
              {
                label: "线下",
                value: 2,
              },
            ],
          },

          {
            label: "计划开始时间",
            prop: "invitationStartDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "计划结束时间",
            prop: "invitationEndDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "会议目的",
            prop: "purpose",
            type: "input",
          },
          {
            label: "会议地址",
            prop: "address",
            type: "input",
          },
          {
            label: "计划预算金额",
            prop: "budgetAmount",
            type: "input",
          },
          {
            label: "会议简介",
            prop: "description",
            type: "input",
          },
          {
            label: "委托客户",
            prop: "entrustedCompanyName",
            type: "input",
          },
          {
            label: "客户负责人",
            prop: "baseEmployeeName",
            type: "input",
          },
          {
            label: "负责人部门",
            prop: "baseDepartmentName",
            type: "input",
          },
          {
            label: "产品名称",
            prop: "baseProductName",
            type: "input",
          },
          {
            label: "产品线",
            prop: "baseProductLine",
            type: "input",
          },
          {
            label: "活动状态",
            prop: "meetingStatus",
            type: "select",
            dicData: [
              {
                label: "计划中",
                value: 0,
              },
              {
                label: "进行中",
                value: 1,
              },
              {
                label: "已结束",
                value: 2,
              },
            ],
          },
        ],
      },
      //邀请讲者信息
      meetingDoctorPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      meetingDoctorOption: {
        searchShowBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        columnBtn: false,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "部门",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "联系电话",
            prop: "phone",
            type: "input",
          },
          {
            label: "讲者级别",
            prop: "doctorLevel",
            type: "input",
          },
          {
            label: "讲者角色",
            prop: "identityRole",
            type: "select",
            dicData: [
              {
                label: "主讲者",
                value: 1,
              },
              {
                label: "主席或主持人",
                value: 2,
              },
              {
                label: "点评人",
                value: 3,
              },
            ],
          },
          {
            label: "讲课费用",
            prop: "doctorFee",
            type: "input",
          },
          {
            label: "讲课主题",
            prop: "topic",
            type: "input",
          },
          {
            label: "讲课课件",
            prop: "courseware",
            type: "input",
          },
        ],
      },
      meetingDoctorList: [],
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    handleClick(tab) {
      if (tab.name == "2") {
        this.getMeetingDoctorList(this.meetingDoctorPage);
      }
    },
    // 邀请讲者
    getMeetingDoctorList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingDoctorList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingDoctorPage.total = data.total;
        this.meetingDoctorList = data.records;
        this.loading = false;
      });
    },
    refreshChangeMeetingDoctor() {
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    //分页
    currentChangeMeetingDoctor(currentPage) {
      this.meetingDoctorPage.currentPage = currentPage;
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    sizeChangeMeetingDoctor(pageSize) {
      this.meetingDoctorPage.pageSize = pageSize;
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    //end

    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("报告为空无法预览");
      }
    },
    //打开弹窗
    openDialog(confirmStatus) {
      this.confirmStatus = confirmStatus;
      this.dialogVisible = true;
    },
    //去会员详情
    toViewDoctor() {
      this.$router.push({
        path: `/authenticationDoctor/detail/${this.form.doctorId}/1`,
      });
    },
    // 打开新页面
    toopen(i) {
      window.open(i, "_blank");
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      window.sessionStorage.setItem("active", 4);
      this.$router.go(-1);
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    //清空
    handleReset() {},
    submit(form, done) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          update(form).then(
            () => {
              this.$message({
                type: "success",
                message: "保存成功!",
              });
              done();
              this.goBack();
            },
            (error) => {
              done();
              console.log(error);
            }
          );
        }
      });
    },
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}

.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
