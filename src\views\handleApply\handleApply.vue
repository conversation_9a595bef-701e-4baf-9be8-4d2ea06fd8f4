<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :search.sync="search"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- 结果状态 -->
      <template slot="rtnflg" slot-scope="{ row }">
        <div
          v-if="['S', 'F', 'C', 'D', 'R', 'P', 'T', 'Z'].includes(row.rtnflg)"
        >
          <el-tag v-if="row.rtnflg == 'S'" type="success" size="small">
            成功</el-tag
          >
          <el-tag v-else type="danger" size="small">{{
            row.rtnflg == "F"
              ? "失败"
              : row.rtnflg == "C"
              ? "撤销"
              : row.rtnflg == "D"
              ? "企业过期不审批"
              : row.rtnflg == "R"
              ? "企业审批否决"
              : row.rtnflg == "P"
              ? "部分成功"
              : row.rtnflg == "T"
              ? "支付退回"
              : row.rtnflg == "Z"
              ? "失败再经办"
              : ""
          }}</el-tag>
        </div>
        <div v-else></div>
      </template>
      <template slot-scope="{ type, size, row, index }" slot="settlementCode">
        <div
          class="to-view"
          @click="toOrderDoctorSettlement(row.settlementId)"
          v-if="row.settlementCode"
        >
          <a readonly>
            {{ row.settlementCode }}
          </a>
        </div>
        <div v-else>无</div>
      </template>
      <template slot-scope="{ type, size, row, index }" slot="approvalStatus">
        <el-tag v-if="row.approvalStatus == -1" size="small" type="info">待提交</el-tag>
        <el-tag v-if="row.approvalStatus == 0" size="small">待审批</el-tag>
        <el-tag v-if="row.approvalStatus == 1" type="success" size="small"
        >通过</el-tag>
        <el-tag v-if="row.approvalStatus == 2" type="danger" size="small"
        >驳回</el-tag>
      </template>
      <template slot="menuLeft">
        <el-button
          v-if="permission.handleApply_sponsorHandle"
          type="primary"
          @click="sponsorHandle()"
          :disabled="sponsorHandleDisabled"
          >{{ sponsorHandleDisabled ? '处理中...' : '发起经办' }}</el-button
        >
        <el-button
          v-if="permission.handleApply_sponsorHandle"
          type="primary"
          @click="sponsorHandleNoApproval()"
          :disabled="sponsorHandleDisabled"
        >{{ sponsorHandleDisabled ? '处理中...' : '经办并打款' }}</el-button
        >
        <el-button
          v-if="permission.handleApply_applyApproval"
          type="primary"
          @click="approveApproval"
        >经办审核</el-button
        >
        <el-button
          v-if="permission.handleApply_showFailApplyInfo"
          type="primary"
          @click="showFailApplyInfo"
          >失败再经办</el-button
        >
        <el-button
          type="primary"
          v-if="permission.handleApply_handleImport"
          @click="handleImport"
          >缓动导入
        </el-button>
        <el-button
          type="primary"
          v-if="permission.handleApply_thaw"
          @click="thaws"
          >解冻
        </el-button>
        <el-button
          v-if="permission.handleApply_export"
          type="primary"
          plain
          icon="el-icon-download"
          @click="handleExport"
        >导出</el-button
        >
      </template>
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button
          v-if="permission.handleApply_delete && row.state == 1"
          type="text"
          @click="rowDel(row)"
          >删除</el-button
        >
        <el-button
          v-if="permission.handleApply_thaw && row.state == 5"
          type="text"
          @click="thaw(row)"
          >解冻</el-button
        >
      </template>
    </avue-crud>
    <el-dialog
      title="查看并刷新"
      append-to-body
      :visible.sync="viewShow"
      width="55%"
    >
      <avue-form ref="form" :option="viewOption" v-model="form"> </avue-form>
    </el-dialog>
    <el-dialog
      title="再经办"
      append-to-body
      :visible.sync="showFailApply"
      width="55%"
    >
      <avue-crud
        :option="failApplyOption"
        :table-loading="loading"
        :data="failApplyData"
        :page.sync="failApplyPage"
        v-model="failApplyForm"
        ref="failApplyCrud"
        @current-change="currentChange2"
        @size-change="sizeChange2"
        @refresh-change="refreshChange2"
      >
        <!-- <template slot="menuLeft">
          <el-button
            v-if="permission.handleApply_sponsorFailHandle"
            type="primary"
            @click="sponsorFailHandle()"
            >再经办</el-button
          >
        </template> -->
        <template slot-scope="{ type, size, row, index }" slot="menu">
          <el-button
            v-if="permission.handleApply_sponsorFailHandle"
            :disabled="row.disabledStatus == true"
            type="text"
            @click="sponsorFailHandle(row, index)"
            >提交</el-button
          >
        </template>
      </avue-crud>
    </el-dialog>
    <el-dialog
      title="缓动导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-before="uploadBefore"
        :upload-after="uploadAfter"
      >
        <template #excelTemplate>
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
      title="经办审核"
      :visible.sync="approveVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      width="340px"
    >
      <el-form ref="approveForm" :model="approveForm">
        <el-form-item prop="agree">
          <el-switch
            v-model="approveForm.agree"
            active-text="同意"
            inactive-text="驳回"
          />
        </el-form-item>
        <el-form-item label="备注"  prop="remark" :rules="[{ required: !approveForm.agree, message: '请输入备注', trigger: 'change' }]">
          <el-input
            v-model="approveForm.remark"
            resize="none"
            type="textarea"
            placeholder="请输入备注"
            :autosize="{ minRows: 2, maxRows: 3}"
          />
        </el-form-item>
      </el-form>
      <span slot="footer">
          <el-button @click="approveVisible = false">取消</el-button>
          <el-button type="primary" @click="handleApproval">确认</el-button>
        </span>
    </el-dialog>
    <el-dialog
      title="审核进度"
      :visible.sync="progressVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      :show-close="false"
      width="700px">
      <el-progress :text-inside="true" :stroke-width="18" :percentage="progressPercent"></el-progress>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  add,
  getDetail,
  getFailApplyInfo,
  getList,
  remove,
  sponsorFailHandle,
  sponsorHandle,
  sponsorHandleApproval,
  sponsorHandleNoApproval,
  thaw,
  update,
} from "@/api/handleApply/handleApply";
import option from "@/const/handleApply/handleApply";
import {mapGetters} from "vuex";
import {exportBlob} from "@/api/common";
import {getToken} from "@/util/auth";
import {downloadXls} from "@/util/util";
import NProgress from "nprogress";
import {dateNow} from "@/util/date";

export default {
  data() {
    return {
      //在经办
      showFailApply: false,
      failApplyOption: {
        calcHeight: 30,
        tip: false,
        columnBtn: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        addBtn: false,
        menu: true,
        editBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "结算单编号",
            prop: "settlementCode",
            type: "input",
          },
          {
            label: "收方户名",
            prop: "accnam",
            type: "input",
          },

          {
            label: "收方账户",
            prop: "accnbr",
            type: "input",
          },
          {
            label: "身份证号",
            prop: "idCardNumber",
            type: "input",
          },

          {
            label: "交易金额",
            prop: "trsamt",
            type: "input",
          },
        ],
      },
      failApplyForm: {},
      failApplyQuery: {},
      failApplyPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      failApplyData: [],
      //在经办end
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      search: {},
      selectionList: [],
      option: option,
      data: [],
      // 查看
      viewShow: false,
      viewOption: {},
      sponsorHandleStatus: false,
      sponsorHandleDisabled: false, // 添加按钮禁用状态
      // 导入
      thawStatus: false,
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "导入",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "数据上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
            },
            data: {
              isCovered: 1,
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            // action: `${this.id}`,
            action: "/api/blade-handleApply/handleApply/freeze-handleApply",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      excelBox: false,
      approveVisible: false,
      isApproving: false,
      progressVisible: false, // 控制进度条弹窗显示
      progressPercent: 0,     // 当前进度百分比
      progressTimer: null,    // 定时器引用
      approveForm: { agree: true, remark: null },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.handleApply_add, false),
        viewBtn: this.vaildData(this.permission.handleApply_view, false),
        delBtn: this.vaildData(this.permission.handleApply_delete, false),
        editBtn: this.vaildData(this.permission.handleApply_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    if (
      !this.validatenull(window.sessionStorage.getItem("handleApplySearch"))
    ) {
      this.search = JSON.parse(
        window.sessionStorage.getItem("handleApplySearch")
      );
      this.query = JSON.parse(
        window.sessionStorage.getItem("handleApplySearch")
      );
      window.sessionStorage.removeItem("handleApplySearch");
    }
  },
  //组件销毁
  beforeDestroy() {
    console.log("销毁");
    console.log(this.query);
    if (!this.validatenull(this.query)) {
      window.sessionStorage.setItem(
        "handleApplySearch",
        JSON.stringify(this.query)
      );
    }
  },
  methods: {
    handleImport() {
      this.excelBox = true;
    },
    uploadBefore(file, done) {
      this.excelBox = false;
      this.$message({
        type: "success",
        message: "操作成功，请稍候查看数据。",
      });
      done();
    },
    uploadAfter(res, done, loading, column) {
      this.$alert(res.data.msg, '提示', {
        confirmButtonText: '确定',
      });
      window.console.log(column);
      this.excelBox = false;
      this.refreshChange();
      done();
    },
    handleTemplate() {
      exportBlob(
        `/api/blade-handleApply/handleApply/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, `缓动导入模板.xlsx`);
      });
    },

    thaw(row) {
      this.$confirm("确定将选择数据解冻?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return thaw(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    thaws() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      let status = true;
      console.log(this.selectionList);
      this.selectionList.map((item) => {
        if (item.state != 5) {
          status = false;
        }
      });
      if (!status) {
        this.$message.warning("请选择操作状态为冻结的数据");
        return;
      }
      if (this.thawStatus) {
        this.$message.warning("解冻中请稍后");
        return;
      }
      this.thawStatus = true;
      thaw(this.ids).then(
        (res) => {
          this.thawStatus = false;
          if (res.data.success) {
            this.$message({
              type: "success",
              message: res.data.msg,
            });
            this.onLoad(this.page);
          }
        },
        () => {
          this.thawStatus = false;
        }
      );
    },
    toOrderDoctorSettlement(id) {
      this.$router.push({
        path: `/orderdoctosettlement/detail/${id}`,
      });
    },
    sponsorHandle() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      // if (this.selectionList.length > 1) {
      //   this.$message.warning("只能选择一条数据");
      //   return;
      // }
      if (this.sponsorHandleStatus) {
        this.$message.warning("发起经办中请稍后");
        return;
      }

      // 添加确认对话框
      this.$confirm('确认发起经办操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false // 防止误触遮罩关闭
      }).then(() => {
        // 禁用按钮，防止重复提交
        this.sponsorHandleDisabled = true;
        this.sponsorHandleStatus = true;

        sponsorHandle(this.ids).then(
          (res) => {
            this.sponsorHandleStatus = false;
            if (res.data.success) {
              this.$message({
                type: "success",
                message: res.data.msg,
              });
              // 刷新数据
              this.onLoad(this.page);
            } else {
              // 如果请求失败，重新启用按钮
              this.sponsorHandleDisabled = false;
              this.$message({
                type: "error",
                message: res.data.msg || "操作失败",
              });
            }
          },
          (error) => {
            this.sponsorHandleStatus = false;
            // 如果请求出错，重新启用按钮
            this.sponsorHandleDisabled = false;
            this.$message({
              type: "error",
              message: "操作失败：" + (error.message || "未知错误"),
            });
          }
        );
      }).catch(() => {
        // 用户取消操作
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      });
    },
    sponsorHandleNoApproval() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.sponsorHandleStatus) {
        this.$message.warning("发起经办中请稍后");
        return;
      }

      // 添加确认对话框
      this.$confirm('确认发起经办并打款操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false // 防止误触遮罩关闭
      }).then(() => {
        // 禁用按钮，防止重复提交
        this.sponsorHandleDisabled = true;
        this.sponsorHandleStatus = true;

        sponsorHandleNoApproval(this.ids).then(
          (res) => {
            this.sponsorHandleStatus = false;
            if (res.data.success) {
              this.$message({
                type: "success",
                message: res.data.msg,
              });
              // 刷新数据
              this.onLoad(this.page);
            } else {
              // 如果请求失败，重新启用按钮
              this.sponsorHandleDisabled = false;
              this.$message({
                type: "error",
                message: res.data.msg || "操作失败",
              });
            }
          },
          (error) => {
            this.sponsorHandleStatus = false;
            // 如果请求出错，重新启用按钮
            this.sponsorHandleDisabled = false;
            this.$message({
              type: "error",
              message: "操作失败：" + (error.message || "未知错误"),
            });
          }
        );
      }).catch(() => {
        // 用户取消操作
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      });
    },
    showFailApplyInfo() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList.length > 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      if (this.selectionList[0].handleType != 1) {
        this.$message.warning("请选择主代发经办单操作再经办");
        return;
      }
      this.showFailApply = true;
      this.getFailApplyInfo(this.failApplyPage, {});
    },

    //
    sponsorFailHandle(row) {
      this.$set(row, "disabledStatus", true);
      sponsorFailHandle([row], this.ids).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.onLoad(this.page);
        this.showFailApply = false;
      });
    },

    getFailApplyInfo(page, params = {}) {
      params.settlementId = this.selectionList[0].settlementId;
      getFailApplyInfo(
        page.currentPage,
        page.pageSize,
        Object.assign(params)
      ).then(({ data: res }) => {
        console.log(res);
        const data = res.data;
        this.failApplyPage.total = data.total;
        this.failApplyData = data.records;
        this.loading = false;
        this.selectionClear2();
      });
    },
    selectionClear2() {
      this.$refs.failApplyCrud.toggleSelection();
    },
    //更改页码
    currentChange2(currentPage) {
      this.failApplyPage.currentPage = currentPage;
      this.getFailApplyInfo(this.failApplyPage, {});
    },
    //更改条数
    sizeChange2(pageSize) {
      this.failApplyPage.pageSize = pageSize;
      this.getFailApplyInfo(this.failApplyPage, {});
    },
    //刷新
    refreshChange2() {
      this.getFailApplyInfo(this.failApplyPage, {});
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },

    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          done();
        });
      } else if (type == "view") {
        this.$router.push({
          path: `/handleApply/detail/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      //审核时间
      if (!this.validatenull(params.eptdat)) {
        this.query.eptdatStart = params.eptdat[0];
        this.query.eptdatEnd = params.eptdat[1];
        delete this.query.eptdat;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();

        // 数据刷新后，重新启用"发起经办"按钮
        this.sponsorHandleDisabled = false;
      });
    },
    approveApproval() {
      if(this.selectionList.length > 0) {
        const hasOtherStatus = this.selectionList.find(item => item.approvalStatus != 0)
        if(hasOtherStatus) {
          this.$message.warning("只能选择待审核状态的数据");
        } else {
          this.approveVisible = true
        }
      } else {
        this.$message.warning("请选择至少一条数据");
      }
    },
    handleApproval() {
      if (this.isApproving) {
        this.$message.info('请勿重复提交');
        return;
      }

      this.isApproving = true;

      this.startProgressSimulation(); // 显示并启动进度条

      this.$refs.approveForm.validate().then(valid => {
        if(valid) {
          const params = {
            handleApplyIds: this.selectionList.map(row => row.id),
            approvalStatus: this.approveForm.agree ? 1 : 2,
            approvalRemark: this.approveForm.remark
          }
          sponsorHandleApproval(params).then(res => {
            this.onLoad(this.page);
            this.approveVisible = false
            this.$alert(res.data.msg, '提示', {
              confirmButtonText: '确定',
            });
          }).finally(() => {
            this.stopProgressSimulation(); // 结束进度条
            this.isApproving = false;
          });
        }
      }).finally(() => {
        setTimeout(() => {
          this.isApproving = false;
        }, 1500); // 1.5秒后允许再次提交
      });
    },
    handleExport() {
      let downloadUrl = `/api/blade-handleApply/handleApply/export-orderDoctorPaymentFinance?${this.website.tokenHeader}=${getToken()}`;
      const {
      } = this.query;
      let values = {
      };
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then(res => {
          downloadXls(res.data, `自然人代发经办单数据${dateNow()}.xlsx`);
          NProgress.done();
        })
      });
    },

    startProgressSimulation() {
      this.progressPercent = 0;
      this.progressVisible = true;

      if (this.progressTimer) clearInterval(this.progressTimer);

      this.progressTimer = setInterval(() => {
        if (this.progressPercent < 95) {
          // 每次增加一个随机整数 1~10
          this.progressPercent = Math.min(
            Math.floor(this.progressPercent) + Math.floor(Math.random() * 10),
            95
          );
        }
      }, 300);
    },

    stopProgressSimulation() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
        this.progressTimer = null;
      }
      this.progressPercent = 100;

      setTimeout(() => {
        this.progressVisible = false;
      }, 200); // 停留一下再隐藏
    }

},
};
</script>

<style></style>
