<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
    </basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <avue-form
            ref="form"
            :option="option"
            v-model="form"
            @submit="submit"
            @reset-change="handleReset"
          >
            <!-- 涉及产品名称 -->
            <template slot-scope="{ disabled, size }" slot="baseProductId">
              <el-select
                v-model="form.baseProductId"
                @change="productNameChange"
                @focus="productNameFocus"
                filterable
                remote
                reserve-keyword
                placeholder="请选择产品名"
                :remote-method="remoteMethodProduct"
                :loading="productSelectLoading"
              >
                <el-option
                  v-for="item in productOptions"
                  :key="item.value"
                  :label="item.productName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </template>

            <!-- 业务人员  -->
            <template slot-scope="{ disabled, size }" slot="baseEmployeeId">
              <el-select
                v-model="form.baseEmployeeId"
                @change="baseEmployeeChange"
                @focus="baseEmployeeFocus"
                filterable
                remote
                reserve-keyword
                placeholder="请选择业务人员"
                :remote-method="remoteMethodBaseEmployee"
                :loading="selectLoading"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </template>
          </avue-form>
        </el-tab-pane>
        <el-tab-pane label="邀请讲者" name="2">
          <!-- 邀请讲者 -->
          <avue-crud
            ref="crudDoctor"
            :page.sync="meetingDoctorPage"
            :option="meetingDoctorOption"
            :data="meetingDoctorList"
            :before-open="beforeOpen"
            @row-update="rowUpdate"
            @row-del="rowDel"
            @current-change="currentChangeMeetingDoctor"
            @size-change="sizeChangeMeetingDoctor"
            @refresh-change="refreshChangeMeetingDoctor"
          >
            <template slot-scope="scope" slot="menuLeft">
              <el-button
                v-if="permission.inviteDoctor_add && form.planStatus == 0"
                type="primary"
                @click="$refs.crudDoctor.rowAdd()"
              >新增</el-button
              >
            </template>
            <template slot-scope="{ row }" slot="menu">
              <el-button
                type="text"
                @click="toViewDoctor(row)"
              >查看讲者信息</el-button
              >
              <el-button
                v-if="permission.inviteDoctor_edit && form.planStatus == 0"
                type="text"
                @click="rowCell(row, index)"
              >{{ row.$cellEdit ? "保存" : "修改" }}</el-button
              >
              <el-button
                v-if="row.$cellEdit"
                type="text"
                @click="rowCancel(row, index)"
              >取消</el-button
              >
              <el-button
                type="text"
                v-if="permission.inviteDoctor_delete && form.planStatus == 0"
                @click="$refs.crudDoctor.rowDel(row, index)"
              >删除</el-button
              >
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
      <el-dialog
        title="请选择会员"
        append-to-body
        @close="closeDialog"
        :visible.sync="showDialog"
        width="75%"
      >
        <avue-crud
          :row-style="rowStyle"
          :table-loading="dialogLoading"
          :data="dialogData"
          :option="dialogOption"
          :page.sync="dialogPage"
          ref="dialogCrud"
          @search-change="dialogSearchChange"
          @search-reset="dialogSearchReset"
          @current-change="dialogCurrentChange"
          @size-change="dialogSizeChange"
          @refresh-change="dialogRefreshChange"
          @selection-change="dialogSelectionChange"
        >
        </avue-crud>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showDialog = false">取 消</el-button>
          <el-button type="primary" @click="saveList()">确 定</el-button>
        </span>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import {getByEntrustedCompanyId, getPlanDoctor, saveOrUpdate,} from "@/api/demoActivityPlan/demoActivityPlan";
import {getProductList} from "@/api/demoActivityserviceproject/demoActivityServiceProject";
import { getDetail } from "@/api/demoMeeting/demoMeeting";
//邀请讲者
import { getList as getMeetingDoctorList, getExistDoctorIds, saveList, deleteDoctor, updateByDoctor } from "@/api/demoMeetingdoctor/demoMeetingDoctor";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      //涉及产品名称
      productSelectOption: {}, //选中的对象
      productOptions: [],
      productSelectLoading: false,
      //业务人员
      selectOption: {}, //选中的对象
      options: [],
      selectLoading: false,
      activeName: "1",
      id: "",
      form: {},
      option: {
        submitIcon: " ",
        emptyIcon: " ",
        disabled: false,
        labelWidth: "150",
        column: [
          {
            label: "活动编码",
            prop: "code",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入活动编码",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "会议名称",
            prop: "name",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入会议名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "会议类型",
            prop: "type",
            type: "input",
            rules: [
              {
                required: true,
                message: "请选择会议类型",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "举办形式",
            prop: "format",
            type: "select",
            dicData: [
              {
                label: "线上",
                value: 1,
              },
              {
                label: "线下",
                value: 2,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择举办形式",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "计划开始时间",
            prop: "invitationStartDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            rules: [
              {
                required: true,
                message: "请选择计划开始时间",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "计划结束时间",
            prop: "invitationEndDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            rules: [
              {
                required: true,
                message: "请选择计划结束时间",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "会议目的",
            prop: "purpose",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入会议目的",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "会议地址",
            prop: "address",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入会议地址",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "会议简介",
            prop: "description",
            type: "input",
          },
          {
            label: "产品名",
            prop: "baseProductId",
            type: "select",
            props: {
              label: "key",
              value: "value",
            },
            dicData: [],
            rules: [
              {
                required: true,
                message: "请选择产品名",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "企业名称",
            prop: "entrustedCompanyId",
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            change: ({ value }) => {
              if (value) {
                this.entrustedCompanyId = value;
                this.initOptions();
              }
            },
            dicUrl: "/api/blade-csc/entrustedCompany/getList",
            rules: [
              {
                required: true,
                message: "请选择企业名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "业务人员",
            prop: "baseEmployeeId",
            type: "select",
            labelTip: "请先选择企业名称",
            rules: [
              {
                required: true,
                message: "请选择业务人员",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "业务部门",
            prop: "baseDepartmentName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请选择代表部门",
                trigger: ["blur", "change"],
              },
            ],
            disabled: true,
          },
        ],
      },
      //企业名称id
      entrustedCompanyId: "",
      //邀请讲者信息
      meetingDoctorPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      meetingDoctorOption: {
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        addBtnIcon: " ",
        cancelBtnIcon: " ",
        saveBtnIcon: " ",
        updateBtnIcon: " ",
        addBtn: false,
        searchShowBtn: false,
        columnBtn: false,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        cellBtn: false,
        cancelBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "部门",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "联系电话",
            prop: "phone",
            type: "input",
          },
          {
            label: "讲者级别",
            prop: "doctorLevel",
            type: "input",
          },
          {
            label: "讲者角色",
            prop: "identityRole",
            type: "select",
            dicData: [
              {
                label: "主讲者",
                value: 1,
              },
              {
                label: "主席或主持人",
                value: 2,
              },
              {
                label: "点评人",
                value: 3,
              },
            ],
            cell: true,
          },
          {
            label: "讲课课件",
            prop: "coursework",
            type: "input",
            cell: true,
          },
        ],
      },
      meetingDoctorList: [],
      //弹窗
      dialogLoading: false,
      dialogSelectionList: [],
      doctorIds: [],
      showDialog: false,
      dialogData: [],
      dialogPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      dialogQuery: {},
      dialogOption: {
        selectable: (row) => {
          return !this.doctorIds.includes(row.doctorId);
        },
        height: "37vh",
        rowKey: "doctorId",
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        reserveSelection: true,
        border: true,
        index: true,
        menu: false,
        viewBtn: false,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "会员名称",
            prop: "doctorName",
            type: "input",
            search: true,
          },
          {
            label: "医院名称/工作单位",
            prop: "hospitalName",
            type: "input",
            search: true,
            searchLabelWidth: 150,
          },
          {
            label: "所在科室",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职称",
            prop: "professional",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "手机号码",
            prop: "phone",
            type: "input",
          },
        ],
      },
    };
  },
  async created() {
    this.id = this.$route.params.id;
    await this.initProductOptions();
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    handleClick(tab) {
      if (tab.name == "2") {
        this.getMeetingDoctorList(this.meetingDoctorPage);
      }
    },
    rowUpdate(row, index, done, loading) {
      updateByDoctor(row).then(
        () => {
          this.getMeetingDoctorList(this.meetingDoctorPage);
          this.getDetail();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    //删除邀请会员
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return deleteDoctor(row.id);
        })
        .then(() => {
          this.getMeetingDoctorList(this.meetingDoctorPage);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 邀请讲者
    getMeetingDoctorList(page, params = {}) {
      params.meetingId = this.id;
      getMeetingDoctorList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.meetingDoctorPage.total = data.total;
        this.meetingDoctorList = data.records;
        this.loading = false;
      });
    },
    refreshChangeMeetingDoctor() {
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    //分页
    currentChangeMeetingDoctor(currentPage) {
      this.meetingDoctorPage.currentPage = currentPage;
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    sizeChangeMeetingDoctor(pageSize) {
      this.meetingDoctorPage.pageSize = pageSize;
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    //end

    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("报告为空无法预览");
      }
    },
    //清空搜索
    dialogSearchReset() {
      this.dialogQueryquery = {};
      this.getDialogList();
    },
    //搜索条件更改
    dialogSearchChange(params, done) {
      this.dialogQueryquery = params;
      this.dialogPage.currentPage = 1;
      this.getDialogList(params);
      done();
    },
    dialogCurrentChange(currentPage) {
      this.dialogPage.currentPage = currentPage;
      this.getDialogList();
    },
    dialogSizeChange(pageSize) {
      this.dialogPage.pageSize = pageSize;
      this.getDialogList();
    },
    //刷新
    dialogRefreshChange() {
      this.getDialogList();
    },
    //选中
    dialogSelectionChange(list) {
      this.dialogSelectionList = list;
    },
    closeDialog() {
      this.$refs.dialogCrud.toggleSelection();
    },
    //保存会员
    saveList() {
      if((this.dialogSelectionList.length + this.doctorIds.length) > 1) {
        this.$message({
          type: "error",
          message: "只能邀请一个会员!",
        });
        return;
      }
      this.dialogSelectionList.map((item) => {
        item.meetingId = this.id;
        return item;
      });
      saveList(this.dialogSelectionList).then(
        () => {
          this.showDialog = false;
          this.getMeetingDoctorList(this.meetingDoctorPage);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          console.log(error);
        }
      );
    },

    rowCell(row, index) {
      this.$refs.crudDoctor.rowCell(row, index);
    },
    rowCancel(row, index) {
      row.$cellEdit = false;
      this.$refs.crudDoctor.rowCancel(row, index);
      this.getMeetingDoctorList(this.meetingDoctorPage);
    },
    rowStyle({ row }) {
      if (this.doctorIds.includes(row.doctorId)) {
        return {
          backgroundColor: "#eee",
        };
      }
    },
    //会员弹窗选中
    toggleRowSelection(data) {
      //第一个参数为数据，第二个参数为是否勾选
      this.$refs.dialogCrud.toggleRowSelection(data, true);
    },
    //获取会员列表
    getDialogList(params = {}) {
      this.dialogLoading = true;
      params.cityCode = this.form.cityCode;
      getPlanDoctor(
        this.dialogPage.currentPage,
        this.dialogPage.pageSize,
        Object.assign(params)
      ).then((res) => {
        const data = res.data.data;
        this.dialogPage.total = data.total;
        this.dialogData = data.records;
        this.showDialog = true;
        this.dialogLoading = false;
        this.$nextTick(() => {
          if (!this.$refs.dialogCrud.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.dialogCrud.doLayout();
          }
        });
      });
    },
    //获取已经选中会员id
    getExistDoctorIds() {
      getExistDoctorIds(this.id).then((res) => {
        const data = res.data.data;
        this.doctorIds = data;
        this.getDialogList();
      });
    },
    //清空搜索
    //打开弹窗
    openDialog() {
      this.dialogPage.currentPage = 1;
      this.getExistDoctorIds();
    },
    //邀请会员
    beforeOpen(done, type) {
      if (type == "add") {
        this.openDialog();
      } else {
        done();
      }
    },
    //去会员详情
    toViewDoctor(row) {
      this.$router.push({
        path: `/authenticationDoctor/detail/${row.doctorId}/1`,
      });
    },
    // 打开新页面
    toopen(i) {
      window.open(i, "_blank");
    },
    //获取业务人员
    initOptions() {
      if (!this.entrustedCompanyId) {
        this.$message({
          type: "error",
          message: "请先选择企业名称!",
        });
        return;
      }
      this.selectLoading = true;
      let query = {
        entrustedCompanyId: this.entrustedCompanyId,
      };
      getByEntrustedCompanyId(query).then((res) => {
        const data = res.data;
        this.options = data.data;
        this.selectLoading = false;
        this.entrustedCompanyChange();
      });
    },
    //业务人员搜索
    remoteMethodBaseEmployee(name) {
      if (name !== "") {
        if (!this.entrustedCompanyId) {
          this.$message({
            type: "error",
            message: "请先选择企业名称!",
          });
          return;
        }
        this.productSelectLoading = true;
        let query = {
          name: name,
          entrustedCompanyId: this.entrustedCompanyId,
        };
        getByEntrustedCompanyId(query).then((res) => {
          const data = res.data;
          this.options = data.data;
          this.selectLoading = false;
        });
      } else {
        this.initOptions();
      }
    },
    baseEmployeeFocus() {
      if (this.options.length == 0) {
        this.initOptions();
      }
    },
    //需求方改变后判断是否还有该业务人员
    entrustedCompanyChange() {
      let serviceProjectList = this.options.filter((item) => {
        return item.id == this.form.baseEmployeeId;
      });
      //代表为空
      if (serviceProjectList.length == 0) {
        this.form.baseEmployeeId = ""; //代表id
        this.form.baseEmployeeName = ""; //代表名字
        this.form.baseEmployeeNumber = ""; //代表工号
        this.form.baseDepartmentName = ""; //部门名
        this.form.baseDepartmentId = ""; //部门id
      }
    },
    //代表姓名更改
    baseEmployeeChange(value) {
      let obj = this.options.filter((item) => {
        return item.id == value;
      });
      this.selectOption = obj[0];
    },
    //业务人员end
    //初始化数据
    async initProductOptions() {
      this.productSelectLoading = true;
      const res = await getProductList()
      const data = res.data;
      this.productOptions = data.data;
      this.productSelectLoading = false;
    },
    //涉及产品名称搜索
    remoteMethodProduct(query) {
      if (query !== "") {
        this.productSelectLoading = true;
        getProductList(query).then((res) => {
          const data = res.data;
          this.productOptions = data.data;
          this.productSelectLoading = false;
        });
      } else {
        this.initProductOptions();
      }
    },
    //获取焦点
    productNameFocus() {
      if (this.productOptions.length == 0) {
        this.initProductOptions();
      }
    },
    //涉及产品名称更改
    productNameChange(value) {
      let obj = this.productOptions.filter((item) => {
        return item.id == value;
      });
      this.productSelectOption = obj[0];
    },
    //涉及产品名称end
    goBack() {
      this.$router.$avueRouter.closeTag();
      window.sessionStorage.setItem("active", 4);
      this.$router.go(-1);
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          this.productNameChange(_this.form.baseProductId);
          this.selectOption = {
            id: _this.form.baseEmployeeId,
            name: _this.form.baseEmployeeName,
            jobNumber: _this.form.baseEmployeeNumber,
            entrustedDeptId: _this.form.baseDepartmentId,
          };
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    //清空
    handleReset() {},
    submit(form, done) {
      this.$refs.form.validate((valid) => {
        if (valid) {

          const params = {
            activityPlanType: 1,
            meetingDTO: {
              invitationStartDate: form.invitationStartDate ,
              invitationEndDate: form.invitationEndDate,
              entrustedCompanyName: this.form.$entrustedCompanyId, //委托客户
              baseProductLine: this.productSelectOption.productLine, //产品线
              baseProductName: this.productSelectOption.productName, //产品名
              sfeProductId: this.productSelectOption.systemSfeId, //sfe产品id
              baseProductId: this.productSelectOption.id, //产品d
              baseEmployeeId: this.selectOption.id, //代表id
              baseEmployeeName: this.selectOption.name, //代表名字
              baseEmployeeNumber: this.selectOption.jobNumber, //代表工号
              baseDepartmentId: this.selectOption.entrustedDeptId, //部门id
              baseDepartmentName: form.baseDepartmentName,
              entrustedCompanyId: form.entrustedCompanyId,
              code: form.code,
              name: form.name,
              type: form.type,
              id: this.id,
              purpose: form.purpose,
              address: form.address,
              description: form.description,
              format: form.format
            } }

          saveOrUpdate(params).then(
            () => {
              this.$message({
                type: "success",
                message: "保存成功!",
              });
              done();
              this.goBack();
            },
            (error) => {
              done();
              console.log(error);
            }
          );
        }
      });
    },
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}

.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
