<template>
  <div class="trend">
    <div ref="chart" class="trend-canvas"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import "echarts/lib/chart/line";
import "echarts/lib/component/tooltip";
import "echarts/lib/component/legend";
import { getMonthlyStatisticalTaxitems } from "@/api/home/<USER>";

import dayjs from "dayjs";
export default {
  name: "HomeTrend",
  data: () => {
    return {
      monthList: [],
      dateList: [],
      chartData: [
        {
          name: "增值税",
          values: [10, 20, 30, 40, 50, 10],
          color: "#1D90A2",
        },
        {
          name: "个人所得税",
          values: [20, 30, 40, 50, 60, 10],
          color: "#FFA91C",
        },
        {
          name: "附加税",
          values: [30, 40, 50, 60, 70, 10],
          color: "#E86262",
        },
      ],
    };
  },
  mounted() {
    this.getMonthlyStatisticalTaxitems();
  },
  methods: {
    getMonthlyStatisticalTaxitems() {
      getMonthlyStatisticalTaxitems().then((res) => {
        let num = 6 - res.data.data.length;
        let data = res.data.data.reverse();
        for (let i = 0; i < num; i++) {
          let previousSixMonths = this.getPreviousMonthsFromDate(
            data[0].data,
            1
          );
          data.unshift({
            data: dayjs(previousSixMonths).format("YYYYMM"),
            fjsTaxAmount: 0,
            grsTaxAmount: 0,
            zzsTaxAmount: 0,
          });
        }
        let fjsTaxAmountList = []; //附加税
        let grsTaxAmountList = []; //个人所得税
        let zzsTaxAmountList = []; //增值税
        data.map((item) => {
          fjsTaxAmountList.push(item.fjsTaxAmount);
          grsTaxAmountList.push(item.grsTaxAmount);
          zzsTaxAmountList.push(item.zzsTaxAmount);

          let month = Number(dayjs(item.data).format("M"));
          switch (month) {
            case 1:
              month = "一月";
              break;
            case 2:
              month = "二月";
              break;
            case 3:
              month = "三月";
              break;
            case 4:
              month = "四月";
              break;
            case 5:
              month = "五月";
              break;
            case 6:
              month = "六月";
              break;
            case 7:
              month = "七月";
              break;
            case 8:
              month = "八月";
              break;
            case 9:
              month = "九月";
              break;
            case 10:
              month = "十月";
              break;
            case 11:
              month = "十一月";
              break;
            case 12:
              month = "十二月";
              break;
            default:
              month = "未知";
          }
          item.data = dayjs(item.data).format("YYYY年M月");
          this.monthList.push(month);
          this.dateList.push(item.data);
        });
        this.chartData[0].values = zzsTaxAmountList;
        this.chartData[1].values = grsTaxAmountList;
        this.chartData[2].values = fjsTaxAmountList;
        this.renderChart();
      });
    },
    getPreviousMonthsFromDate(date, n) {
      var currentDate = dayjs(date);

      // 使用 subtract 方法计算目标日期
      var targetDate = currentDate.subtract(n, "month");

      // 返回目标日期
      return targetDate.toDate();
    },
    renderChart() {
      const chartDom = this.$refs.chart;
      const myChart = echarts.init(chartDom);

      const option = {
        grid: {
          left: 90,
          right: 50,
          top: 80,
          bottom: 50,
        },
        title: {
          text: "纳税税目汇总",
          top: 18,
          left: 22,
          textStyle: {
            color: "#000000",
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "axis",
          formatter: (params) => {
            let text = this.dateList[params[0].dataIndex] + "<br/>";
            params.map((item) => {
              text += item.seriesName + " : " + item.value + "<br/>";
            });
            return text;
          },
          textStyle: {
            align: "left", // 设置文字居左显示
          },
        },
        legend: {
          data: this.chartData.map((data) => data.name),
          top: 18,
          right: 25,
        },
        xAxis: {
          type: "category",
          data: this.monthList,
          axisLabel: {
            interval: 0, // 坐标轴刻度标签的显示间隔
            rotate: 40, // 标签倾斜的角度
            show: true, // 设置为true强制显示所有标签
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            formatter: "{value}元",
          },
        },
        series: this.chartData.map((data) => ({
          color: data.color,
          name: data.name,
          type: "bar",
          data: data.values,
        })),
      };

      myChart.setOption(option);
    },
  },

  beforeUnmount() {},
};
</script>

<style scoped lang="scss">
.trend {
  margin-top: 15px;
  width: 100%;
  height: 478px;
  border-radius: 8px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.trend-canvas {
  width: 100%;
  height: 100%;
}
.trend-title {
  width: 102px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  line-height: 22px;
  margin: 18px 0 20px 22px;
  margin-top: 18px;
  margin-bottom: 20px;
}
.item-title {
  margin-top: 15px;
  font-size: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
}
</style>
