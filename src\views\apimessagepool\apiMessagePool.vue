<template>
  <basic-container>
    <avue-crud
      :option="option"
      :search.sync="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ disabled, size }" slot="spendTimeSearch">
        <el-date-picker
          v-model="spendTime"
          type="daterange"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </template>

      <template slot="menuLeft">
        <el-button
          type="primary"
          @click="resend()"
          v-if="permission.apiMessagePool_resend"
          >重新推送</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  resend,
} from "@/api/apimessagepool/apiMessagePool";
import option from "@/option/apimessagepool/apiMessagePool";
import { mapGetters } from "vuex";
import "nprogress/nprogress.css";

export default {
  data() {
    return {
      spendTime: [], //消费时间
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.apiMessagePool_add, false),
        viewBtn: this.vaildData(this.permission.apiMessagePool_view, false),
        delBtn: this.vaildData(this.permission.apiMessagePool_delete, false),
        editBtn: this.vaildData(this.permission.apiMessagePool_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    resend() {
      if (!this.selectionList.length > 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      resend(this.ids).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          console.log(error);
        }
      );
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.spendTime = [];
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      delete params.spendTime;
      if (!this.validatenull(this.spendTime)) {
        params.spendTimeStart = this.spendTime[0];
        params.spendTimeEnd = this.spendTime[1];
      }
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page) {
      this.loading = true;

      getList(page.currentPage, page.pageSize, this.query).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
