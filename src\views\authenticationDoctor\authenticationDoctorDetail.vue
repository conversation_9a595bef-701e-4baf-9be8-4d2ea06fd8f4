<template>
  <div>
    <basic-container
      ><el-page-header @back="goBack" :content="$route.name"></el-page-header
    ></basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <!--基本信息-->
          <avue-form
            v-if="form1.name != ''"
            ref="form1"
            :option="baseOption"
            v-model="form1"
            @submit="submit"
          >
          </avue-form>
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>
        <el-tab-pane label="认证信息" name="2">
          <!--认证信息-->
          <avue-form
            ref="form1"
            :option="authenticationOption"
            v-model="form1"
            @submit="submit"
          >
            <template slot-scope="{}" slot="faceInfoLabel">
              <span>人脸识别信息&nbsp;&nbsp;</span>
              <el-tooltip
                class="item"
                effect="dark"
                :content="
                  form1.faceVerify == 0
                    ? '待认证'
                    : form1.faceVerify == 1
                    ? '认证一致'
                    : '认证不一致'
                "
                placement="top-start"
              >
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </template>
          </avue-form>
        </el-tab-pane>

        <el-tab-pane label="电子签章" name="4">
          <!--电子签章-->
          <div v-if="form1.esignStatus == 1">
            <el-row>
              <el-col
                :span="8"
                class="card-header-right"
                style="padding-right: 100px"
                ><el-empty
                  description=" "
                  image="/img/weikaitong.png"
                  :image-size="300"
                ></el-empty
              ></el-col>
              <el-col :span="12">
                <div class="first-title" style="margin-top: 70px">
                  什么是电子签章？
                </div>
                <el-row style="margin-top: 20px; font-size: 14px">
                  <el-col :span="18">
                    <span>
                      电子签章是电子合同上的印章展示效果，使用电子签章，委托双方之间通过电子信息的形式达成的一种协议，元圈平台电子签章所签署的电子合同具有和纸质合同同等的法律效力。
                    </span>
                  </el-col>
                </el-row>
                <el-row style="margin-top: 20px"
                  ><span style="font-weight: bold">电子签章的优势</span></el-row
                >
                <el-row style="margin-top: 10px"
                  ><span style="color: #f8b71f; font-size: 14px"
                    >节约成本、提高效率、便于存档、安全可靠</span
                  ></el-row
                >
                <el-row style="margin-top: 20px">
                  <el-button
                    type="primary"
                    v-if="permission.authenticationDoctor_signature"
                    @click="openSignature"
                    >立即开通账户</el-button
                  >
                  <span style="font-weight: bold" v-else
                    >请联系客服进行开通！ 客服电话：00000000</span
                  >
                </el-row>
              </el-col>
            </el-row>
          </div>
          <div style="margin-top: 20px" v-else>
            <div style="height: 50px; margin-top: 20px">
              <el-row type="flex" class="row-bg2" justify="space-around">
                <el-col :span="24">
                  电子签章账户状态：
                  <span class="first-title" v-show="form1.esignStatus == 1"
                    >未开通</span
                  >
                  <span class="first-title" v-show="form1.esignStatus == 2"
                    >已开通</span
                  >
                  <span class="first-title" v-show="form1.esignStatus == 3"
                    >已过期</span
                  >
                  <span class="first-title" v-show="form1.esignStatus == 4"
                    >已停用</span
                  >
                </el-col>
              </el-row>
            </div>
            <span class="first-title" style="margin-top: 30px"
              >电子签章展示</span
            >
            <div>
              <el-image
                style="width: 200px; height: 200px"
                :src="
                  form1.esignUrl
                    ? 'data:image/png;base64,' + form1.esignUrl
                    : ''
                "
                :preview-src-list="[
                  form1.esignUrl
                    ? 'data:image/png;base64,' + form1.esignUrl
                    : '',
                ]"
                fit="cover"
              >
                <template #error>
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                    <div class>公章</div>
                  </div>
                </template>
              </el-image>
              <div
                class="prompt"
                v-show="form1.esignUrl == null || form1.esignUrl == ''"
              >
                <el-button
                  style="text-align: center"
                  type="primary"
                  v-if="permission.authenticationDoctor_signature"
                  @click="applyNow()"
                  >创建印章</el-button
                >
              </div>
            </div>
            <div
              :span="12"
              v-if="form1.esignUrl != '' && form1.esignUrl != null"
            >
              <el-button
                type="primary"
                v-if="permission.authenticationDoctor_singStatus"
                @click="stopEsignAccount(2)"
                >启用电子签章</el-button
              >
              <el-button
                type="danger"
                v-if="permission.authenticationDoctor_singStatus"
                @click="stopEsignAccount(4)"
                >停用电子签章</el-button
              >
            </div>
          </div>
        </el-tab-pane>
        <!-- <el-tab-pane label="合作代表" name="5" v-if="form1.approvalResult == 1">
          <avue-crud
            :option="doctorEmployeeRelOption"
            :table-loading="loading"
            :data="data"
            :page.sync="page"
            :permission="permissionList"
            :before-open="beforeOpen"
            v-model="form"
            ref="crud"
            @row-update="rowUpdate"
            @row-save="rowSave"
            @row-del="rowDel"
            @search-change="searchChange"
            @search-reset="searchReset"
            @selection-change="selectionChange"
            @current-change="currentChange"
            @size-change="sizeChange"
            @refresh-change="refreshChange"
            @on-load="onLoad"
          >
            <template slot-scope="{ disabled, size }" slot="baseProductIdForm">
              <el-select
                v-model="form.baseProductId"
                @change="productNameChange"
                @focus="productNameFocus"
                filterable
                remote
                reserve-keyword
                placeholder="请选择产品名"
                :remote-method="remoteMethodProduct"
                :loading="productSelectLoading"
              >
                <el-option
                  v-for="item in productOptions"
                  :key="item.value"
                  :label="item.productName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </template>
            <template slot-scope="{ type, disabled }" slot="nameForm">
              <el-select
                :disabled="type == 'edit'"
                v-model="form.name"
                @change="nameChange"
                @focus="nameFocus"
                filterable
                remote
                reserve-keyword
                placeholder="请输入代表姓名"
                :remote-method="remoteMethod"
                :loading="selectLoading"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.name"
                  :value="item.id"
                >
                  <span style="float: left">{{ item.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{
                    item.jobNumber
                  }}</span>
                </el-option>
              </el-select>
            </template>
          </avue-crud>
        </el-tab-pane> -->
        <el-tab-pane label="会员协议" name="6">
          <!--会员协议-->
          <el-button
            type="primary"
            v-if="permission.authenticationDoctor_generate_protocol"
            @click="toGenerateProtocol()"
            >重新生成会员协议</el-button
          >
          <div v-if="form1.memberAgreementUrl" style="margin-top: 15px">
            <iframe
              :src="form1.memberAgreementUrl"
              width="100%"
              :height="iframeHeight"
              title="企业会员协议"
              frameBorder="no"
              border="0"
              marginWidth="0"
              marginHeight="0"
              scrolling="no"
              allowTransparency="yes"
            ></iframe>
          </div>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import {
  getDetail,
  regEsignAccount,
  regEsignSeal,
  stopEsignAccount,
  generateProtocol,
} from "@/api/authenticationDoctor/authenticationDoctor";
import { mapGetters } from "vuex";
import {
  getList,
  getDetail as getDoctorEmployeeRelDetail,
  add,
  update,
  remove,
} from "@/api/doctorEmployeeRel/doctorEmployeeRel";
import { getProductList } from "@/api/activityserviceproject/activityServiceProject";
import { getList as getDoctoremployeeList } from "@/api/entrustedemployee/entrustedEmployee";
export default {
  data() {
    return {
      iframeHeight: window.innerHeight - 180,
      //涉及产品名称
      productSelectOption: {}, //选中的对象
      productOptions: [],
      productSelectLoading: false,
      id: "",
      numPages: 1,
      activeName: "1",
      loading: false,
      form1: { name: "" },
      baseOption: {
        disabled: true,
        labelWidth: "170",
        submitBtn: false,
        emptyBtn: false,
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",
            column: [
              {
                label: "姓名",
                prop: "name",
              },
              {
                label: "医院名称/工作单位",
                prop: "hospitalName",
              },
              {
                label: "所在科室",
                prop: "departmentName",
              },
              {
                label: "证件号码",
                prop: "idCardNumber",
              },
              {
                label: "手机号码",
                prop: "phone",
              },
              {
                label: "微信ID",
                prop: "wechatId",
              },
              {
                label: "职称",
                prop: "professional",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=professional",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },
              {
                label: "职务",
                prop: "duty",
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=duty",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },

              {
                label: "性别",
                prop: "sex",
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=sex",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },
              {
                label: "民族",
                prop: "nationality",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=nationality",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },
              {
                label: "籍贯",
                prop: "nativePlace",
                row: true,
              },
              {
                label: "所属省",
                prop: "provinceCode",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["cityCode"],
                dicUrl: "/api/blade-system/region/select",
                span: 6,
              },
              {
                label: "所属市",
                prop: "cityCode",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["districtCode"],
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 6,
              },
              {
                label: "所属区",
                prop: "districtCode",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 6,
              },
              {
                label: "联系地址",
                prop: "address",
              },
              {
                label: "毕业院校",
                prop: "graduateSchool",
              },
              {
                label: "最高学历",
                prop: "education",
              },
              {
                label: "主治专长",
                prop: "speciality",
                type: "textarea",
              },
            ],
          },
        ],
      },
      authenticationOption: {
        disabled: true,
        labelWidth: "170",
        submitBtn: false,
        emptyBtn: false,
        group: [
          {
            label: "身份认证信息",
            prop: "info",
            column: [
              {
                label: "证件类型",
                prop: "credentialType",
                type: "select",
                control: (val) => {
                  switch (val) {
                    case 1:
                      return {
                        idCardAddress: { display: true }, //身份证地址
                        idCardPositive: { display: true }, //身份证正面
                        idCardNegative: { display: true }, //身份证反面
                        passportPersonal: { display: false }, //海外护照个人资料页
                        passportSign: { display: false }, //海外护照盖章页
                        residencePermit: { display: false }, //港澳台通行证
                      };
                    case 2:
                      return {
                        idCardAddress: { display: false }, //身份证地址
                        idCardPositive: { display: false }, //身份证正面
                        idCardNegative: { display: false }, //身份证反面
                        passportPersonal: { display: false }, //海外护照个人资料页
                        passportSign: { display: false }, //海外护照盖章页
                        residencePermit: { display: true }, //港澳台通行证
                      };
                    case 3:
                      return {
                        idCardAddress: { display: false }, //身份证地址
                        idCardPositive: { display: false }, //身份证正面
                        idCardNegative: { display: false }, //身份证反面
                        passportPersonal: { display: false }, //海外护照个人资料页
                        passportSign: { display: false }, //海外护照盖章页
                        residencePermit: { display: true }, //港澳台通行证
                      };
                    case 4:
                      return {
                        idCardAddress: { display: false }, //身份证地址
                        idCardPositive: { display: false }, //身份证正面
                        idCardNegative: { display: false }, //身份证反面
                        passportPersonal: { display: true }, //海外护照个人资料页
                        passportSign: { display: true }, //海外护照盖章页
                        residencePermit: { display: false }, //港澳台通行证
                      };
                  }
                },
                dataType: "number",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=credential_type",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                row: true,
              },
              {
                label: "证件姓名",
                prop: "name",
                type: "input",
                row: true,
              },
              {
                label: "证件号码",
                prop: "idCardNumber",
                type: "input",
                row: true,
              },
              {
                label: "身份证地址",
                prop: "idCardAddress",
                type: "input",
                row: true,
              },
              {
                label: "开户银行",
                prop: "bankName",
                type: "input",
                row: true,
              },
              {
                label: "银行账号",
                prop: "bankAccount",
                type: "input",
                row: true,
              },
              {
                label: "证件认证结果",
                prop: "idCardVerify",
                type: "select",
                dicData: [
                  {
                    label: "待审",
                    value: 0,
                  },
                  {
                    label: "一致",
                    value: 1,
                  },
                  {
                    label: "不一致",
                    value: 2,
                  },
                ],
                row: true,
              },
              {
                span: 6,
                label: "港澳台通行证或居住证",
                prop: "residencePermit",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "身份证正面",
                prop: "idCardPositive",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "身份证反面",
                prop: "idCardNegative",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "海外护照个人资料页",
                prop: "passportPersonal",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "海外护照盖章页",
                prop: "passportSign",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },

              {
                span: 6,
                label: "银行卡图片",
                prop: "bankImg",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              {
                span: 6,
                label: "人脸识别信息",
                prop: "faceInfo",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
            ],
          },
          {
            label: "会员资格信息",
            prop: "info",
            column: [
              {
                label: "医师资格证/执业医师证",
                prop: "doctorCertificateData",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                span: 24,
              },
            ],
          },
          {
            label: "会员结算方式",
            prop: "info",
            column: [
              {
                label: "结算方式",
                prop: "settleType",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=settle_type",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
              },
            ],
          },
        ],
      },
      agreementOption1: {
        disabled: true,
        labelWidth: "170",
        submitBtn: false,
        emptyBtn: false,
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",
            column: [
              {
                label: "签约平台名称",
                prop: "name",
              },
              {
                label: "签约平台公司名称",
                prop: "shortName",
              },
              {
                label: "签约平台信用代码",
                prop: "regType",
              },
              {
                label: "协议起草日期",
                prop: "socialCreditCode",
              },
              {
                label: "平台签约日期",
                prop: "lawPerson",
              },
              {
                label: "会员签约日期",
                prop: "lawPersonIdNum",
              },
              {
                label: "合同开始日期",
                prop: "regDate",
                type: "datetime",
                format: "yyyy年MM月dd日",
                valueFormat: "yyyy-MM-dd",
              },
              {
                label: "合同截止日期",
                prop: "regCapital",
              },

              {
                label: "合作结算周期",
                prop: "mail",
              },

              {
                label: "服务费用性质",
                prop: "webSite",
              },
              {
                label: "合同模板类型",
                prop: "regAddress",
              },
              {
                label: "关联总包合同ID",
                prop: "telphone",
              },

              {
                label: "关联总包合同编号",
                prop: "regCapital",
              },

              {
                label: "关联总包合同名称",
                prop: "mail",
              },

              {
                label: "关联总包企业名称",
                prop: "webSite",
              },
              {
                label: "关联总包合同开始日期",
                prop: "regAddress",
              },
              {
                label: "关联总包合同截止日期",
                prop: "telphone",
              },
            ],
          },
          {
            label: "协议内容",
            prop: "detailInfo",
            column: [
              {
                label: "协议内容",
                prop: "operatStatus",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=operat_status",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
              },
            ],
          },
        ],
      },
      //合作代表
      selectOption: {}, //选中的对象
      options: [],
      selectLoading: false,
      form: {},
      query: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],

      doctorEmployeeRelOption: {
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        addBtnIcon: " ",
        cancelBtnIcon: " ",
        saveBtnIcon: " ",
        updateBtnIcon: " ",
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        labelWidth: "150",
        column: [
          {
            label: "会员姓名",
            prop: "doctorName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "合作代表姓名",
            prop: "name",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入合作代表姓名",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "手机号码",
            prop: "phone",
            type: "input",
            disabled: true,
            rules: [
              {
                required: true,
                message: "请输入手机号码",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "所在部门",
            prop: "baseDepartmentName",
            disabled: true,
            type: "tree",
            dicUrl: "/api/blade-csc/entrustedDept/tree",
            hide: true,
            addDisabled: false,
            props: {
              label: "title",
              value: "id",
            },
            rules: [
              {
                required: true,
                message: "请输入所在部门",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "产品名称",
            prop: "baseProductId",
            type: "select",
            props: {
              label: "productName",
              value: "id",
            },
            dicUrl: "/api/blade-sys/baseProduct/getList",
            rules: [
              {
                required: true,
                message: "请选择产品名称",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },
      data: [],
      //end
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.activeName = this.$route.params.tabindex;
    this.getDetail();
    this.initOptions();
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.doctorEmployeeRel_add, false),
        viewBtn: this.vaildData(this.permission.doctorEmployeeRel_view, false),
        delBtn: this.vaildData(this.permission.doctorEmployeeRel_delete, false),
        editBtn: this.vaildData(this.permission.doctorEmployeeRel_edit, false),
      };
    },
  },
  methods: {
    //初始化数据
    initProductOptions() {
      this.productSelectLoading = true;
      getProductList().then((res) => {
        const data = res.data;
        this.productOptions = data.data;
        this.productSelectLoading = false;
      });
    },
    //涉及产品名称搜索
    remoteMethodProduct(query) {
      if (query !== "") {
        this.productSelectLoading = true;
        getProductList(query).then((res) => {
          const data = res.data;
          this.productOptions = data.data;
          this.productSelectLoading = false;
        });
      } else {
        this.initProductOptions();
      }
    },
    //获取焦点
    productNameFocus() {
      if (this.productOptions.length == 0) {
        this.initProductOptions();
      }
    },
    //涉及产品名称更改
    productNameChange(value) {
      let obj = this.productOptions.filter((item) => {
        return item.id == value;
      });

      this.productSelectOption = obj[0];
    },
    //涉及产品名称end
    initOptions() {
      this.selectLoading = true;
      let params = {};
      getDoctoremployeeList(1, 100, params).then((res) => {
        const data = res.data;
        this.options = data.data.records;
        this.selectLoading = false;
      });
    },
    //合作代表
    remoteMethod(query) {
      if (query !== "") {
        let params = {
          name: query,
        };
        this.selectLoading = true;
        getDoctoremployeeList(1, 100, params).then((res) => {
          const data = res.data;
          this.options = data.data.records;
          this.selectLoading = false;
        });
      } else {
        this.initOptions();
      }
    },
    //获取焦点
    nameFocus() {
      if (this.options.length == 0) {
        this.initOptions();
      }
    },
    nameChange(value) {
      let obj = this.options.filter((item) => {
        return item.id == value;
      });
      this.selectOption = obj[0];
      this.form.phone = this.selectOption.phone;
      this.form.baseDepartmentName = this.selectOption.entrustedDeptId;
      this.form.employeeId = this.selectOption.id;
    },
    rowSave(row, done, loading) {
      row.doctorId = this.id;
      row.name = this.selectOption.name;
      row.baseDepartmentName = this.selectOption.entrustedDept; //部门名
      row.baseDepartmentShortName = this.selectOption.entrustedDeptShort; //部门名(简称)
      row.baseDepartmentId = this.selectOption.entrustedDeptId; //部门id
      row.baseProductName = this.productSelectOption.productName; //产品名
      row.baseProductId = this.productSelectOption.id; //产品d
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      row.baseProductName = this.productSelectOption.productName; //产品名
      row.baseProductId = this.productSelectOption.id; //产品d
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDoctorEmployeeRelDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          this.productNameChange(this.form.baseProductId);
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
      this.$refs.crud.refreshTable();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      params.doctorId = this.id;
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    //end
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    handleClick(tab) {
      if (tab.name == "5") {
        this.onLoad(this.page);
        this.initProductOptions();
      }
    },

    //开通电子签章
    openSignature() {
      regEsignAccount(this.id).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getDetail();
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    //申请电子签章
    applyNow() {
      regEsignSeal(this.id).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getDetail();
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    // 停用或启用印章
    stopEsignAccount(status) {
      stopEsignAccount(this.id, status).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getDetail();
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form1 = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    // 重新生成会员协议
    toGenerateProtocol() {
      generateProtocol(this.id).then((res) => {
        if (res.data.success) {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.getDetail();
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    submit() {},
  },
};
</script>

<style>
.dangId {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-around;
}

.avue-crud__menu {
  min-height: 0 !important;
}

h4 {
  margin: 5px 0 !important;
}

.first-title {
  font-size: 16px !important;
  font-weight: 600;
  color: #333;
}

.prompt {
  width: 148px;
  margin: 5px 0;
  font-size: 14px;
  text-align: center;
}

.image-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fbfdff;
  border: 1px dashed #c0ccda;
  color: #666;
  border-radius: 6px;
  box-sizing: border-box;
  width: 148px;
  height: 148px;
  cursor: pointer;
  line-height: 146px;
  vertical-align: top;
}

.image-slot div {
  font-size: 14px;
  margin: 4px 0;
}
</style>
