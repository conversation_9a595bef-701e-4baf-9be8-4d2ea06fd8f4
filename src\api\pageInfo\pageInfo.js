import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-pageInfo/pageInfo/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-pageInfo/pageInfo/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-pageInfo/pageInfo/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-pageInfo/pageInfo/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-pageInfo/pageInfo/submit',
    method: 'post',
    data: row
  })
}

export const changeStatus = (id) => {
  return request({
    url: '/api/blade-pageInfo/pageInfo/changeStatus',
    method: 'post',
    data:{id}
  })
}

export const getIconList = (current, size, params) => {
  return request({
    url: '/api/blade-iconInfo/iconInfo/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}