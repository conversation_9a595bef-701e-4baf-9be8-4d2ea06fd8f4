<template>
  <basic-container>
    <el-tabs v-model="active" @tab-click="handleClick">
      <el-tab-pane
        label="病例征集"
        name="1"
        v-if="permission.serviceActivityPlan_caseCollection_point"
      >
        <avue-crud
          :option="optionCaseCollection"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :permission="permissionList"
          :before-open="beforeOpen"
          v-model="form"
          ref="crudCaseCollection"
          @row-del="rowDel"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoadCaseCollection"
        >
          <template slot="fabuqiye" slot-scope="{ row }">
            连云港亿辰信息科技有限责任公司
          </template>
          <!-- 活动计划状态 -->
          <template slot="planStatus" slot-scope="{ row }">
            <el-tag v-if="row.planStatus == 0" type="info" size="small"
            >计划中</el-tag
            >
            <el-tag v-if="row.planStatus == 1" type="success" size="small"
            >进行中</el-tag
            >
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                row.planEndedMark == 1
                  ? '活动执行完成'
                  : row.planEndedMark == 2
                  ? '活动超期关闭'
                  : row.planEndedMark == 3
                  ? '客户拒签合同'
                  : row.planEndedMark == 4
                  ?'平台主动关闭活动'
                  :'推送平台主动关闭'
              "
              placement="top"
            >
              <el-tag v-if="row.planStatus == 2" type="danger" size="small"
              >已结束</el-tag
              >
            </el-tooltip>
          </template>
          <template slot-scope="{ row }" slot="menuLeft"> </template>
          <template slot-scope="{ row }" slot="menu">
            <!--<el-button-->
            <!--type="text"-->
            <!--v-if="permission.serviceActivityPlan_edit_point && row.planStatus == 0"-->
            <!--@click="$refs.crudCaseCollection.rowEdit(row, index)"-->
            <!--&gt;编辑</el-button-->
            <!--&gt;-->
            <!-- <el-button
              type="text"
              v-if="
                permission.serviceActivityPlan_delete_point && row.planStatus == 0
              "
              @click="$refs.crudCaseCollection.rowDel(row, index)"
              >删除</el-button
            > -->
            <el-button
              v-if="
                permission.serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 0
              "
              type="text"
              @click="stopCaseCollection(row, 1)"
            >确认发布</el-button
            >
            <el-button
              v-if="
                permission.serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 1
              "
              type="text"
              @click="stopCaseCollection(row, 2)"
            >结束任务</el-button
            >
          </template>
        </avue-crud>
      </el-tab-pane>
      <el-tab-pane
        label="用药反馈"
        name="2"
        v-if="permission.serviceActivityPlan_medicationFeedback_point"
      >
        <avue-crud
          :option="optionMedicationFeedback"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :permission="permissionList"
          :before-open="beforeOpen"
          v-model="form"
          ref="crudMedicationFeedback"
          @row-del="rowDel"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoadMedicationFeedback"
        >
          <template slot="fabuqiye" slot-scope="{ row }">
            连云港亿辰信息科技有限责任公司
          </template>

          <template slot="planStatus" slot-scope="{ row }">
            <el-tag v-if="row.planStatus == 0" type="info" size="small"
            >计划中</el-tag
            >
            <el-tag v-if="row.planStatus == 1" type="success" size="small"
            >进行中</el-tag
            >
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                row.planEndedMark == 1
                  ? '活动执行完成'
                  : row.planEndedMark == 2
                  ? '活动超期关闭'
                  : row.planEndedMark == 3
                  ? '客户拒签合同'
                  : row.planEndedMark == 4
                  ? '平台主动关闭活动'
                  : '推送平台主动关闭'
              "
              placement="top"
            >
              <el-tag v-if="row.planStatus == 2" type="danger" size="small"
              >已结束</el-tag
              >
            </el-tooltip>
          </template>
          <template slot-scope="{ row }" slot="menu">
            <!--<el-button-->
            <!--type="text"-->
            <!--v-if="permission.serviceActivityPlan_edit_point && row.planStatus == 0"-->
            <!--@click="$refs.crudMedicationFeedback.rowEdit(row, index)"-->
            <!--&gt;编辑</el-button-->
            <!--&gt;-->
            <!--<el-button-->
            <!--type="text"-->
            <!--v-if="-->
            <!--permission.serviceActivityPlan_delete_point && row.planStatus == 0-->
            <!--"-->
            <!--@click="$refs.crudMedicationFeedback.rowDel(row, index)"-->
            <!--&gt;删除</el-button-->
            <!--&gt;-->
            <el-button
              v-if="
                permission.
serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 0
              "
              type="text"
              @click="stopMedicationFeedback(row, 1)"
            >确认发布</el-button
            >
            <el-button
              v-if="
                permission.
serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 1
              "
              type="text"
              @click="stopMedicationFeedback(row, 2)"
            >计划结束</el-button
            >
          </template>
        </avue-crud>
      </el-tab-pane>
      <el-tab-pane
        label="临床调研"
        name="3"
        v-if="permission.serviceActivityPlan_clinicalResearch_point"
      >
        <avue-crud
          :option="optionClinicalResearch"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :permission="permissionList"
          :before-open="beforeOpen"
          v-model="form"
          ref="crudClinicalResearch"
          @row-del="rowDel"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoadClinicalResearch"
        >
          <template slot="fabuqiye" slot-scope="{ row }">
            连云港亿辰信息科技有限责任公司
          </template>

          <template slot="planStatus" slot-scope="{ row }">
            <el-tag v-if="row.planStatus == 0" type="info" size="small"
            >计划中</el-tag
            >
            <el-tag v-if="row.planStatus == 1" type="success" size="small"
            >进行中</el-tag
            >
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                row.planEndedMark == 1
                  ? '活动执行完成'
                  : row.planEndedMark == 2
                  ? '活动超期关闭'
                  : row.planEndedMark == 3
                  ? '客户拒签合同'
                  : row.planEndedMark == 4
                  ? '平台主动关闭活动'
                  : '推送平台主动关闭'
              "
              placement="top"
            >
              <el-tag v-if="row.planStatus == 2" type="danger" size="small"
              >已结束</el-tag
              >
            </el-tooltip>
          </template>
          <template slot-scope="{ row }" slot="menu">
            <!--<el-button-->
            <!--type="text"-->
            <!--v-if="permission.serviceActivityPlan_edit_point && row.planStatus == 0"-->
            <!--@click="$refs.crudClinicalResearch.rowEdit(row, index)"-->
            <!--&gt;编辑</el-button-->
            <!--&gt;-->
            <!--<el-button-->
            <!--type="text"-->
            <!--v-if="-->
            <!--permission.serviceActivityPlan_delete_point && row.planStatus == 0-->
            <!--"-->
            <!--@click="$refs.crudClinicalResearch.rowDel(row, index)"-->
            <!--&gt;删除</el-button-->
            <!--&gt;-->
            <el-button
              v-if="
                permission.
serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 0
              "
              type="text"
              @click="stopClinicalResearch(row, 1)"
            >确认发布</el-button
            >
            <el-button
              v-if="
                permission.
serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 1
              "
              type="text"
              @click="stopClinicalResearch(row, 2)"
            >计划结束</el-button
            >
          </template>
        </avue-crud>
      </el-tab-pane>
      <el-tab-pane
        label="学术会议"
        name="4"
        v-if="permission.serviceActivityPlan_meeting_point"
      >
        <avue-crud
          :option="optionAcademicMeeting"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :permission="permissionList"
          :before-open="beforeOpen"
          v-model="form"
          ref="crudMeeting"
          @row-del="rowDel"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoadMeeting"
        >
          <template slot="fabuqiye" slot-scope="{ row }">
            连云港亿辰信息科技有限责任公司
          </template>
          <!-- 会议状态 -->
          <template slot="meetingStatus" slot-scope="{ row }">
            <el-tag v-if="row.meetingStatus == 1" size="small">计划中</el-tag>
            <el-tag v-if="row.meetingStatus == 2" type="success" size="small"
            >进行中</el-tag
            >
            <el-tag v-if="row.meetingStatus == 3" type="danger" size="small"
            >已结束</el-tag
            >
            <el-tag v-if="row.meetingStatus == 4" type="success" size="small"
            >已变更</el-tag
            >
            <el-tag v-if="row.meetingStatus == 5" type="danger" size="small"
            >已取消</el-tag
            >
            <el-tag v-if="row.meetingStatus == 6" type="danger" size="small"
            >已否决</el-tag
            >
          </template>
          <template slot="planStatus" slot-scope="{ row }">
            <el-tag v-if="row.planStatus == 0" type="info" size="small"
            >计划中</el-tag
            >
            <el-tag v-if="row.planStatus == 1" type="success" size="small"
            >进行中</el-tag
            >
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                row.planEndedMark == 1
                  ? '活动执行完成'
                  : row.planEndedMark == 2
                  ? '活动超期关闭'
                  : row.planEndedMark == 3
                  ? '客户拒签合同'
                  : row.planEndedMark == 4
                  ? '平台主动关闭活动'
                  : row.planEndedMark == 5
                  ? '推送平台主动关闭'
                  : '推送平台否决活动'
              "
              placement="top"
            >
              <el-tag v-if="row.planStatus == 2" type="danger" size="small"
              >已结束</el-tag
              >
            </el-tooltip>

            <el-tag v-if="row.planStatus == 3" type="danger" size="small"
            >已驳回</el-tag
            >
          </template>
          <template slot-scope="{ row }" slot="menu">
            <el-button
              type="text"
              v-if="
                permission.serviceActivityPlan_delete_point && row.planStatus == 0
              "
              @click="$refs.crudMeeting.rowDel(row, index)"
            >删除</el-button
            >
            <el-button
              v-if="
                permission.
serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 0
              "
              type="text"
              @click="stopMeeting(row, 1)"
            >确认发布</el-button
            >
            <el-button
              v-if="
                permission.
serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 1
              "
              type="text"
              @click="stopMeeting(row, 2)"
            >计划结束</el-button
            >
          </template>
        </avue-crud>
      </el-tab-pane>
      <el-tab-pane
        label="知识创作"
        name="5"
        v-if="permission.serviceActivityPlan_lecture_point"
      >
        <avue-crud
          :option="optionLecture"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :permission="permissionList"
          :before-open="beforeOpen"
          v-model="form"
          ref="crudLecture"
          @row-del="rowDel"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoadLecture"
        >
          <template slot="fabuqiye" slot-scope="{ row }">
            连云港亿辰信息科技有限责任公司
          </template>
          <template slot="planStatus" slot-scope="{ row }">
            <el-tag v-if="row.planStatus == 0" type="info" size="small"
            >计划中</el-tag
            >
            <el-tag v-if="row.planStatus == 1" type="success" size="small"
            >进行中</el-tag
            >
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                row.planEndedMark == 1
                  ? '活动执行完成'
                  : row.planEndedMark == 2
                  ? '活动超期关闭'
                  : row.planEndedMark == 3
                  ? '客户拒签合同'
                  : row.planEndedMark == 4
                  ? '平台主动关闭活动'
                  : '推送平台主动关闭'
              "
              placement="top"
            >
              <el-tag v-if="row.planStatus == 2" type="danger" size="small"
              >已结束</el-tag
              >
            </el-tooltip>
            <el-tag v-if="row.planStatus == 3" type="danger" size="small"
            >已驳回</el-tag
            >
          </template>
          <template slot-scope="{ row }" slot="menu">
            <el-button
              type="text"
              v-if="
                permission.serviceActivityPlan_delete_point && row.planStatus == 0
              "
              @click="$refs.crudLecture.rowDel(row, index)"
            >删除</el-button
            >
            <el-button
              v-if="
                permission.serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 0
              "
              type="text"
              @click="stopLecture(row, 1)"
            >确认发布</el-button
            >
            <el-button
              v-if="
                permission.serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 1
              "
              type="text"
              @click="stopLecture(row, 2)"
            >计划结束</el-button
            >
          </template>
        </avue-crud>
      </el-tab-pane>
      <el-tab-pane
        label="专业评审"
        name="6"
        v-if="permission.serviceActivityPlan_professionalReview_point"
      >
        <!-- professionalreview -->
        <avue-crud
          :option="optionProfessionalReview"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :permission="permissionList"
          :before-open="beforeOpen"
          v-model="form"
          ref="crudLecture"
          @row-del="rowDel"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoadProfessionalReview"
        >
          <template slot="fabuqiye" slot-scope="{ row }">
            连云港亿辰信息科技有限责任公司
          </template>
          <template slot="planStatus" slot-scope="{ row }">
            <el-tag v-if="row.planStatus == 0" type="info" size="small"
            >计划中</el-tag
            >
            <el-tag v-if="row.planStatus == 1" type="success" size="small"
            >进行中</el-tag
            >
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                row.planEndedMark == 1
                  ? '活动执行完成'
                  : row.planEndedMark == 2
                  ? '活动超期关闭'
                  : row.planEndedMark == 3
                  ? '客户拒签合同'
                  : row.planEndedMark == 4
                  ? '平台主动关闭活动'
                  : '推送平台主动关闭'
              "
              placement="top"
            >
              <el-tag v-if="row.planStatus == 2" type="danger" size="small"
              >已结束</el-tag
              >
            </el-tooltip>
            <el-tag v-if="row.planStatus == 3" type="danger" size="small"
            >已驳回</el-tag
            >
          </template>
          <template slot-scope="{ row }" slot="menu">
            <el-button
              type="text"
              v-if="
                permission.serviceActivityPlan_delete_point && row.planStatus == 0
              "
              @click="$refs.crudLecture.rowDel(row, index)"
            >删除</el-button
            >
            <el-button
              v-if="
                permission.serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 0
              "
              type="text"
              @click="stopProfessionalReview(row, 1)"
            >确认发布</el-button
            >

            <el-button
              v-if="
                permission.serviceActivityPlan_stopCaseCollection_point &&
                row.planStatus == 1
              "
              type="text"
              @click="stopProfessionalReview(row, 2)"
            >计划结束</el-button
            >
          </template>
        </avue-crud>
      </el-tab-pane>
      <!--      <el-tab-pane-->
      <!--        label="临床调研-积分"-->
      <!--        name="3-1"-->
      <!--        v-if="permission.serviceActivityPlan_clinicalResearch_point"-->
      <!--      >-->
      <!--        <avue-crud-->
      <!--          :option="optionClinicalResearchPoint"-->
      <!--          :table-loading="loading"-->
      <!--          :data="data"-->
      <!--          :page.sync="page"-->
      <!--          :permission="permissionList"-->
      <!--          :before-open="beforeOpen"-->
      <!--          v-model="form"-->
      <!--          ref="crudClinicalResearchPoint"-->
      <!--          @row-del="rowDel"-->
      <!--          @search-change="searchChange"-->
      <!--          @search-reset="searchReset"-->
      <!--          @selection-change="selectionChange"-->
      <!--          @current-change="currentChange"-->
      <!--          @size-change="sizeChange"-->
      <!--          @refresh-change="refreshChange"-->
      <!--          @on-load="onLoadClinicalResearchPoint"-->
      <!--        >-->
      <!--          <template slot="fabuqiye" slot-scope="{ row }">-->
      <!--            连云港亿辰信息科技有限责任公司-->
      <!--          </template>-->
      <!--          <template slot="budgetAmount" slot-scope="{ row }">-->
      <!--            {{ Math.floor(Number(row.budgetAmount)) }}-->
      <!--          </template>-->
      <!--          <template slot="planStatus" slot-scope="{ row }">-->
      <!--            <el-tag v-if="row.planStatus == 0" type="info" size="small"-->
      <!--            >计划中</el-tag-->
      <!--            >-->
      <!--            <el-tag v-if="row.planStatus == 1" type="success" size="small"-->
      <!--            >进行中</el-tag-->
      <!--            >-->
      <!--            <el-tooltip-->
      <!--              class="item"-->
      <!--              effect="dark"-->
      <!--              :content="-->
      <!--                row.planEndedMark == 1-->
      <!--                  ? '活动执行完成'-->
      <!--                  : row.planEndedMark == 2-->
      <!--                  ? '活动超期关闭'-->
      <!--                  : row.planEndedMark == 3-->
      <!--                  ? '客户拒签合同'-->
      <!--                  : row.planEndedMark == 4-->
      <!--                  ? '平台主动关闭活动'-->
      <!--                  : '推送平台主动关闭'-->
      <!--              "-->
      <!--              placement="top"-->
      <!--            >-->
      <!--              <el-tag v-if="row.planStatus == 2" type="danger" size="small"-->
      <!--              >已结束</el-tag-->
      <!--              >-->
      <!--            </el-tooltip>-->
      <!--          </template>-->
      <!--          <template slot-scope="{ row }" slot="menu">-->
      <!--            &lt;!&ndash;<el-button&ndash;&gt;-->
      <!--            &lt;!&ndash;type="text"&ndash;&gt;-->
      <!--            &lt;!&ndash;v-if="permission.serviceActivityPlan_edit_point && row.planStatus == 0"&ndash;&gt;-->
      <!--            &lt;!&ndash;@click="$refs.crudClinicalResearch.rowEdit(row, index)"&ndash;&gt;-->
      <!--            &lt;!&ndash;&gt;编辑</el-button&ndash;&gt;-->
      <!--            &lt;!&ndash;&gt;&ndash;&gt;-->
      <!--            &lt;!&ndash;<el-button&ndash;&gt;-->
      <!--            &lt;!&ndash;type="text"&ndash;&gt;-->
      <!--            &lt;!&ndash;v-if="&ndash;&gt;-->
      <!--            &lt;!&ndash;permission.serviceActivityPlan_delete_point && row.planStatus == 0&ndash;&gt;-->
      <!--            &lt;!&ndash;"&ndash;&gt;-->
      <!--            &lt;!&ndash;@click="$refs.crudClinicalResearch.rowDel(row, index)"&ndash;&gt;-->
      <!--            &lt;!&ndash;&gt;删除</el-button&ndash;&gt;-->
      <!--            &lt;!&ndash;&gt;&ndash;&gt;-->
      <!--            <el-button-->
      <!--              v-if="-->
      <!--                permission.
serviceActivityPlan_stopCaseCollection_point &&-->
      <!--                row.planStatus == 0-->
      <!--              "-->
      <!--              type="text"-->
      <!--              @click="stopClinicalResearchPoint(row, 1)"-->
      <!--            >确认发布</el-button-->
      <!--            >-->
      <!--            <el-button-->
      <!--              v-if="-->
      <!--                permission.
serviceActivityPlan_stopCaseCollection_point &&-->
      <!--                row.planStatus == 1-->
      <!--              "-->
      <!--              type="text"-->
      <!--              @click="stopClinicalResearch(row, 2)"-->
      <!--            >计划结束</el-button-->
      <!--            >-->
      <!--          </template>-->
      <!--        </avue-crud>-->
      <!--      </el-tab-pane>-->
    </el-tabs>
  </basic-container>
</template>

<script>
import {
  getPointList as getCaseCollectionlist,
  remove as caseCollectionRemove,
  stopCaseCollection,
} from "@/api/caseCollection/caseCollection";
import optionCaseCollection from "@/const/caseCollection/caseCollection";
import {
  getPointList as getMedicationFeedbacklist,
  remove as medicationFeedbackRemove,
  stopMedicationFeedback,
  startPlanAgain as medicationFeedbackStartPlanAgain,
} from "@/api/medicationFeedback/medicationFeedback";
import optionMedicationFeedback from "@/const/medicationFeedback/medicationFeedback";
import {
  getPointList as getClinicalResearchlist,
  remove as clinicalResearchRemove,
  stopClinicalResearch,
  startPlanAgain as clinicalResearchStartPlanAgain, getPointList,
} from "@/api/clinicalresearch/clinicalResearch";
import optionClinicalResearch from "@/const/clinicalresearch/clinicalResearch";
import optionClinicalResearchPoint from "@/const/clinicalresearchPoint/clinicalResearchPoint";
//学术会议
import {
  getPointList as getMeetingList,
  remove as meetingRemove,
  stopMeeting,
  startPlanAgain as meetingStartPlanAgain,
} from "@/api/meeting/meeting";
import optionAcademicMeeting from "@/const/academicMeeting/academicMeeting";
// 知识创作
import {
  getPointList as getLectureList,
  stopLecture,
  remove as lectureRemove,
  startPlanAgain as lectureStartPlanAgain,
} from "@/api/lecture/lecture";
import planLecture from "@/const/planLecture/planLecture";
// 专家评审
import {
  getPointList as getProfessionalReviewList,
  remove as professionalReviewRemove,
  updateStatus,
  startPlanAgain as professionalReviewStartPlanAgain,
} from "@/api/professionalreview/professionalReview";
import optionProfessionalReview from "@/option/professionalreview/professionalReview";
import { mapGetters } from "vuex";
import dayjs from "dayjs";
import {tree} from "@/api/entrusteddept/entrustedDept";
export default {
  data() {
    return {
      active: "1",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      data: [],
      //病例征集
      optionCaseCollection: optionCaseCollection,
      //病例征集 end
      //用药反馈
      optionMedicationFeedback: optionMedicationFeedback,
      //用药反馈end
      //临床调研
      optionClinicalResearch: optionClinicalResearch,
      optionClinicalResearchPoint: optionClinicalResearchPoint,
      //临床调研end
      //学术会议
      optionAcademicMeeting: optionAcademicMeeting,
      //学术会议end
      //知识创作
      optionLecture: planLecture,
      //知识创作end
      // 专家评审
      optionProfessionalReview: optionProfessionalReview,
      //专业评审end
      dialogRow: {},
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.serviceActivityPlan_add, false),
        viewBtn: this.vaildData(
          this.permission.serviceActivityPlan_view_point,
          false
        ),
        delBtn: this.vaildData(
          this.permission.serviceActivityPlan_delete_point,
          false
        ),
        editBtn: this.vaildData(
          this.permission.serviceActivityPlan_edit_point,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.initData();
    if (!this.validatenull(window.sessionStorage.getItem("active"))) {
      this.active = window.sessionStorage.getItem("active");
      window.sessionStorage.removeItem("active");
    }
  },
  methods: {
    initData() {
      tree().then((res) => {
        var caseCollectionProp = this.findObject(optionCaseCollection.column, "baseDepartment");
        caseCollectionProp.dicData = res.data.data;
        var medicationFeedbackProp = this.findObject(optionMedicationFeedback.column, "baseDepartment");
        medicationFeedbackProp.dicData = res.data.data;
        var clinicalResearchProp = this.findObject(optionClinicalResearch.column, "baseDepartment");
        clinicalResearchProp.dicData = res.data.data;
        var academicMeetingProp = this.findObject(optionAcademicMeeting.column, "baseDepartment");
        academicMeetingProp.dicData = res.data.data;
        var planLectureProp = this.findObject(planLecture.column, "baseDepartment");
        planLectureProp.dicData = res.data.data;
        var professionalReviewProp = this.findObject(optionProfessionalReview.column, "baseDepartment");
        professionalReviewProp.dicData = res.data.data;


        this.orgData = res.data.data;
      });
    },
    handleClick(tab) {
      this.page.currentPage = 1;
      this.query = {};
      this.data = [];
      if (tab.name == "1") {
        this.onLoadCaseCollection(this.page);
      } else if (tab.name == "2") {
        this.onLoadMedicationFeedback(this.page);
      } else if (tab.name == "3") {
        this.onLoadClinicalResearch(this.page);
      } else if (tab.name == "4") {
        this.onLoadMeeting(this.page);
      } else if (tab.name == "5") {
        this.onLoadLecture(this.page);
      } else if (tab.name == "6") {
        this.onLoadProfessionalReview(this.page);
      } else if (tab.name == "3-1") {
        this.onLoadClinicalResearchPoint(this.page);
      }
    },

    //删除
    rowDel(row) {
      if (this.active == "1") {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return caseCollectionRemove(row.id);
          })
          .then(() => {
            this.onLoadCaseCollection(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      } else if (this.active == "2") {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return medicationFeedbackRemove(row.id);
          })
          .then(() => {
            this.onLoadMedicationFeedback(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      } else if (this.active == "3") {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return clinicalResearchRemove(row.id);
          })
          .then(() => {
            this.onLoadClinicalResearch(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      } else if (this.active == "4") {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return meetingRemove(row.id);
          })
          .then(() => {
            this.onLoadMeeting(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      } else if (this.active == "5") {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return lectureRemove(row.id);
          })
          .then(() => {
            this.onLoadLecture(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      } else if (this.active == "6") {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return professionalReviewRemove(row.id);
          })
          .then(() => {
            this.onLoadProfessionalReview(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      } else if (this.active == "3-1") {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return clinicalResearchRemove(row.id);
          })
          .then(() => {
            this.onLoadClinicalResearchPoint(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      }
    },
    beforeOpen(done, type) {
      if (this.active == "1") {
        if (type == "view") {
          this.$router.push({
            path: `/activityPlanPoint/caseCollectionView/${this.form.id}`,
          });
        } else {
          done();
        }
      } else if (this.active == "2") {
        if (type == "view") {
          this.$router.push({
            path: `/activityPlanPoint/medicationFeedbackView/${this.form.id}`,
          });
        } else {
          done();
        }
      } else if (this.active == "3") {
        if (type == "view") {
          this.$router.push({
            path: `/activityPlanPoint/clinicalResearchView/${this.form.id}`,
          });
        } else {
          done();
        }
      } else if (this.active == "4") {
        if (type == "view") {
          this.$router.push({
            path: `/activityPlanPoint/meetingView/${this.form.id}`,
          });
        } else {
          done();
        }
      } else if (this.active == "5") {
        if (type == "view") {
          this.$router.push({
            path: `/activityPlanPoint/lectureView/${this.form.id}`,
          });
        } else {
          done();
        }
      } else if (this.active == "6") {
        if (type == "view") {
          this.$router.push({
            path: `/activityPlanPoint/professionalReviewView/${this.form.id}`,
          });
        } else {
          done();
        }
      } else if (this.active == "3-1") {
        if (type == "view") {
          this.$router.push({
            path: `/activityPlanPoint/clinicalResearchPointView/${this.form.id}`,
            query: {
              active: "3-1",
            },
          });
        } else {
          done();
        }
      }
    },
    searchReset() {
      this.query = {};
      if (this.active == "1") {
        this.onLoadCaseCollection(this.page);
      } else if (this.active == "2") {
        this.onLoadMedicationFeedback(this.page);
      } else if (this.active == "3") {
        this.onLoadClinicalResearch(this.page);
      } else if (this.active == "4") {
        this.onLoadMeeting(this.page);
      } else if (this.active == "5") {
        this.onLoadLecture(this.page);
      } else if (this.active == "6") {
        this.onLoadProfessionalReview(this.page);
      } else if (this.active == "3-1") {
        this.onLoadClinicalResearchPoint(this.page)
      }
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      if (!this.validatenull(params.invitationStartDate)) {
        params.startTimeStart = params.invitationStartDate[0];
        params.startTimeEnd = params.invitationStartDate[1];
        delete this.query.invitationStartDate;
      }
      //实际发布时间
      if (!this.validatenull(params.releaseTime)) {
        params.releaseTimeStart = params.releaseTime[0];
        params.releaseTimeEnd = params.releaseTime[1];
        delete this.query.releaseTime;
      }
      if (!this.validatenull(params.invitationEndDate)) {
        params.endTimeStart = params.invitationEndDate[0];
        params.endTimeEnd = params.invitationEndDate[1];
        delete this.query.invitationEndDate;
      }

      if (params.baseDepartment) {
        let baseDepartmentId = params.baseDepartment.join(",") + ",";
        params.baseDepartment.map((item) => {
          this.orgData.map((item2) => {
            let itemList = this.getAllChildIdsById(item, item2);
            baseDepartmentId += itemList.join(",");
          });
        });
        // 调用方法，传入给定ID和组织架构数据
        params.baseDepartmentIds = baseDepartmentId;
      }

      if (this.active == "1") {
        this.onLoadCaseCollection(this.page, params);
      } else if (this.active == "2") {
        this.onLoadMedicationFeedback(this.page, params);
      } else if (this.active == "3") {
        this.onLoadClinicalResearch(this.page, params);
      } else if (this.active == "4") {
        this.onLoadMeeting(this.page, params);
      } else if (this.active == "5") {
        this.onLoadLecture(this.page, params);
      } else if (this.active == "6") {
        this.onLoadProfessionalReview(this.page, params);
      } else if (this.active == "3-1") {
        this.onLoadClinicalResearchPoint(this.page, params)
      }

      done();
    },
    getAllChildIdsById(id, data) {
      let _this = this;
      let childIds = [];
      // 递归辅助函数，用于遍历数据并获取子节点ID
      function traverseChildren(nodes) {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].id === id) {
            // 如果当前节点匹配到给定ID，将其子节点ID添加到结果数组中
            if (nodes[i].children && nodes[i].children.length > 0) {
              childIds = childIds.concat(
                _this.getAllChildIdsByIdHelper(nodes[i].children)
              );
            }
          } else if (nodes[i].children && nodes[i].children.length > 0) {
            // 如果当前节点不匹配给定ID，继续向下遍历子节点
            traverseChildren(nodes[i].children);
          }
        }
      }

      traverseChildren([data]); // 调用辅助函数从根节点开始遍历

      return childIds;
    },
    getAllChildIdsByIdHelper(nodes) {
      let childIds = [];
      let _this = this;
      for (let i = 0; i < nodes.length; i++) {
        childIds.push(nodes[i].id);

        if (nodes[i].children && nodes[i].children.length > 0) {
          childIds = childIds.concat(
            _this.getAllChildIdsByIdHelper(nodes[i].children)
          );
        }
      }

      return childIds;
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      if (this.active == "1") {
        this.$refs.crudCaseCollection.toggleSelection();
      } else if (this.active == "2") {
        this.$refs.crudMedicationFeedback.toggleSelection();
      } else if (this.active == "3") {
        this.$refs.crudClinicalResearch.toggleSelection();
      } else if (this.active == "4") {
        this.$refs.crudMeeting.toggleSelection();
      } else if (this.active == "5") {
        this.$refs.crudLecture.toggleSelection();
      } else if (this.active == "3-1") {
        this.$refs.crudClinicalResearchPoint.toggleSelection();
      }
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      if (this.active == "1") {
        this.onLoadCaseCollection(this.page, this.query);
      } else if (this.active == "2") {
        this.onLoadMedicationFeedback(this.page, this.query);
      } else if (this.active == "3") {
        this.onLoadClinicalResearch(this.page, this.query);
      } else if (this.active == "4") {
        this.onLoadMeeting(this.page, this.query);
      } else if (this.active == "5") {
        this.onLoadLecture(this.page, this.query);
      } else if (this.active == "6") {
        this.onLoadProfessionalReview(this.page, this.query);
      } else if (this.active == "3-1") {
        this.onLoadClinicalResearchPoint(this.page, this.query)
      }
    },
    //病例征集
    onLoadCaseCollection(page, params = {}) {
      if (this.active != "1") {
        return;
      }
      this.loading = true;
      getCaseCollectionlist(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
        this.$nextTick(() => {
          if (!this.$refs.crudCaseCollection.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.crudCaseCollection.doLayout();
          }
        });
      });
    },

    //开始结束病例征集计划
    stopCaseCollection(row, status) {
      this.$confirm("本操作将更新此数据状态，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return stopCaseCollection(row.id, status);
        })
        .then(() => {
          this.onLoadCaseCollection(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    //病例征集end

    //用药反馈
    // 重发合同
    medicationFeedbackStartPlanAgain(row) {
      let date = dayjs(row.invitationEndDate).valueOf();
      const timeStamp = dayjs().valueOf();
      if (timeStamp > date) {
        this.$message({
          type: "error",
          message: "计划已经到期，不能重发",
        });
        return;
      }
      medicationFeedbackStartPlanAgain(row.id).then(() => {
        this.onLoadMedicationFeedback(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    onLoadMedicationFeedback(page, params = {}) {
      if (this.active != "2") {
        return;
      }
      this.loading = true;
      getMedicationFeedbacklist(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
        this.$nextTick(() => {
          if (!this.$refs.crudMedicationFeedback.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.crudMedicationFeedback.doLayout();
          }
        });
      });
    },
    //开始结束用药反馈
    stopMedicationFeedback(row, status) {
      this.$confirm("本操作将更新此数据状态，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return stopMedicationFeedback(row.id, status);
        })
        .then(() => {
          this.onLoadMedicationFeedback(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    //用药反馈end

    //临床调研
    // 重发合同
    clinicalResearchStartPlanAgain(row) {
      let date = dayjs(row.invitationEndDate).valueOf();
      const timeStamp = dayjs().valueOf();
      if (timeStamp > date) {
        this.$message({
          type: "error",
          message: "计划已经到期，不能重发",
        });
        return;
      }
      clinicalResearchStartPlanAgain(row.id).then(() => {
        this.onLoadClinicalResearch(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    onLoadClinicalResearch(page, params = {}) {
      if (this.active != "3") {
        return;
      }
      this.loading = true;
      getClinicalResearchlist(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
        this.$nextTick(() => {
          if (!this.$refs.crudClinicalResearch.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.crudClinicalResearch.doLayout();
          }
        });
      });
    },

    onLoadClinicalResearchPoint(page, params = {}) {
      if (this.active != "3-1") {
        return;
      }
      this.loading = true;
      getPointList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
        this.$nextTick(() => {
          if (!this.$refs.crudClinicalResearchPoint.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.crudClinicalResearchPoint.doLayout();
          }
        });
      });
    },
    //开始结束用药反馈
    stopClinicalResearch(row, status) {
      this.$confirm("本操作将更新此数据状态，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return stopClinicalResearch(row.id, status);
        })
        .then(() => {
          this.onLoadClinicalResearch(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    stopClinicalResearchPoint(row, status) {
      this.$confirm("本操作将更新此数据状态，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return stopClinicalResearch(row.id, status);
        })
        .then(() => {
          this.onLoadClinicalResearchPoint(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    //临床调研end
    //学术会议
    // 重发合同
    meetingStartPlanAgain(row) {
      let date = dayjs(row.overdueCloseTime).valueOf();
      const timeStamp = dayjs().valueOf();
      if (timeStamp > date) {
        this.$message({
          type: "error",
          message: "计划超期时间已经到期，不能重发",
        });
        return;
      }
      meetingStartPlanAgain(row.id).then(() => {
        this.onLoadMeeting(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    onLoadMeeting(page, params = {}) {
      if (this.active != "4") {
        return;
      }
      this.loading = true;
      getMeetingList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
        this.$nextTick(() => {
          if (!this.$refs.crudMeeting.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.crudMeeting.doLayout();
          }
        });
      });
    },
    //开始结束驳回学术会议
    stopMeeting(row, status) {
      this.$confirm("本操作将更新此数据状态，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return stopMeeting(row.id, status);
        })
        .then(() => {
          this.onLoadMeeting(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    //学术会议end
    //知识创作
    //重发合同
    lectureStartPlanAgain(row) {
      let date = dayjs(row.invitationEndDate).valueOf();
      const timeStamp = dayjs().valueOf();
      if (timeStamp > date) {
        this.$message({
          type: "error",
          message: "计划已经到期，不能重发",
        });
        return;
      }
      lectureStartPlanAgain(row.id).then(() => {
        this.onLoadLecture(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    onLoadLecture(page, params = {}) {
      if (this.active != "5") {
        return;
      }
      this.loading = true;
      getLectureList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
        this.$nextTick(() => {
          if (!this.$refs.crudLecture.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.crudLecture.doLayout();
          }
        });
      });
    },
    //开始结束驳回知识创作
    stopLecture(row, status) {
      this.$confirm("本操作将更新此数据状态，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return stopLecture(row.id, status);
        })
        .then(() => {
          this.onLoadLecture(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    //知识创作end
    // 专业评审
    professionalReviewStartPlanAgain(row) {
      let date = dayjs(row.invitationEndDate).valueOf();
      const timeStamp = dayjs().valueOf();
      if (timeStamp > date) {
        this.$message({
          type: "error",
          message: "计划已经到期，不能重发",
        });
        return;
      }
      professionalReviewStartPlanAgain(row.id).then(() => {
        this.onLoadProfessionalReview(this.page);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },
    onLoadProfessionalReview(page, params = {}) {
      if (this.active != "6") {
        return;
      }
      this.loading = true;
      getProfessionalReviewList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
        this.$nextTick(() => {
          if (!this.$refs.crudCaseCollection.gridShow) {
            // myTable是表格的ref属性值
            this.$refs.crudCaseCollection.doLayout();
          }
        });
      });
    },
    //开始专业评审集计划
    stopProfessionalReview(row, status) {
      this.$confirm("本操作将更新此数据状态，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return updateStatus(row.id, status);
        })
        .then(() => {
          this.onLoadProfessionalReview(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 专业评审end
  },
};
</script>

<style></style>
