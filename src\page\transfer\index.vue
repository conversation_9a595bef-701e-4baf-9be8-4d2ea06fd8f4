<template>
  <div class="transfer">
    <!-- 学术会议 -->
    <meeting-detail-sfe
      v-if="actType == '1'"
      :sfeId="sfeId"
    ></meeting-detail-sfe>
    <!-- 知识创作2 -->
    <div v-if="actType == '2'">
      <!-- type=='1'大讲堂 -->
      <lecture_1DetailSfe
        v-if="type == '1'"
        :sfeId="sfeId"
      ></lecture_1DetailSfe>
      <!-- type=='2'专业录播 -->
      <lecture_2DetailSfe
        v-if="type == '2'"
        :sfeId="sfeId"
      ></lecture_2DetailSfe>
      <!-- type=='3'微学堂 -->
      <lecture_3DetailSfe
        v-if="type == '3'"
        :sfeId="sfeId"
      ></lecture_3DetailSfe>
      <!-- type=='4'杏林集萃 -->
      <lecture_4DetailSfe
        v-if="type == '4'"
        :sfeId="sfeId"
      ></lecture_4DetailSfe>
    </div>
    <!-- 临床调研 -->
    <clinical-research-report-detail-sfe
      v-if="actType == '4'"
      :sfeId="sfeId"
    ></clinical-research-report-detail-sfe>
    <!-- 用药反馈 -->
    <medication-feedback-report-detail-sfe
      v-if="actType == '5'"
      :sfeId="sfeId"
    ></medication-feedback-report-detail-sfe>
    <!-- 专业评审 -->
    <professional-review-report-detail-sfe
      v-if="actType == '6'"
      :sfeId="sfeId"
    ></professional-review-report-detail-sfe>
    <!-- 病例征集 -->
    <case-collection-report-detail-sfe
      v-if="actType == '8'"
      :sfeId="sfeId"
    ></case-collection-report-detail-sfe>
  </div>
</template>

<script>
import { getWebToken } from "@/api/common";
import meetingDetailSfe from "../../../src/views/meeting/meetingDetailSfe";
import lecture_1DetailSfe from "../../../src/views/lecture/lecture_1DetailSfe";
import lecture_2DetailSfe from "../../../src/views/lecture/lecture_2DetailSfe";
import lecture_3DetailSfe from "../../../src/views/lecture/lecture_3DetailSfe";
import lecture_4DetailSfe from "../../../src/views/lecture/lecture_4DetailSfe";
import clinicalResearchReportDetailSfe from "../../../src/views/clinicalresearchreport/clinicalResearchReportDetailSfe";
import medicationFeedbackReportDetailSfe from "../../../src/views/medicationFeedbackReport/medicationFeedbackReportDetailSfe";
import professionalReviewReportDetailSfe from "../../../src/views/professionalreviewreport/professionalReviewReportDetailSfe";
import caseCollectionReportDetailSfe from "../../../src/views/caseCollectionReport/caseCollectionReportDetailSfe";
export default {
  name: "transfer",
  components: {
    meetingDetailSfe,
    lecture_1DetailSfe,
    lecture_2DetailSfe,
    lecture_3DetailSfe,
    lecture_4DetailSfe,
    clinicalResearchReportDetailSfe,
    medicationFeedbackReportDetailSfe,
    professionalReviewReportDetailSfe,
    caseCollectionReportDetailSfe,
  },
  data() {
    return {
      sfeId: "",
      actType: "",
      type: "",
    };
  },

  created() {
    const loading = this.$loading({
      lock: true,
      text: "加载中",
      spinner: "el-icon-loading",
    });
    let params = {
      appId: this.$route.query.appId,
      timestamp: this.$route.query.timestamp,
      token: this.$route.query.token,
    };
    this.sfeId = null;
    this.actType = null;
    this.type = null;
    getWebToken(params).then((res) => {
      const data = res.data;
      this.$store.commit("SET_COLLAPSE");
      this.$store.commit("SET_TOKEN", data.data);
      this.$store.commit("SET_REFRESH_TOKEN", data.data);
      localStorage.setItem("saber-access-token", data.data);
      this.sfeId = this.$route.query.id;
      this.actType = this.$route.query.actType;
      this.type = this.$route.query.type;
      loading.close();
    });
  },
  methods: {},
};
</script>
<style lang="scss" scoped>
.transfer {
  overflow-x: auto;
}
</style>
