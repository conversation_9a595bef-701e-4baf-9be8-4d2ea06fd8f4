<template>
  <div style="overflow-y: auto; height: 100vh">
    <basic-container>
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="1">
          <avue-form
            v-if="!validatenull(form)"
            ref="form"
            :option="option"
            v-model="form"
            @submit="submit"
          >
            <template slot-scope="{}" slot="patientFeedback">
              <el-popover
                placement="top-start"
                width="200"
                trigger="hover"
                :content="form.patientFeedback"
              >
                <div style="display: flex" slot="reference">
                  <el-input
                    disabled
                    v-model="form.patientFeedback"
                    placeholder="请输入内容"
                  ></el-input>
                </div>
              </el-popover>
            </template>
            <template slot-scope="{}" slot="confirmResult">
              <el-popover
                placement="top-start"
                width="200"
                trigger="hover"
                :content="form.confirmResult"
              >
                <div style="display: flex" slot="reference">
                  <el-input
                    disabled
                    v-model="form.confirmResult"
                    placeholder="请输入内容"
                  ></el-input>
                </div>
              </el-popover>
            </template>
          </avue-form>
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>
        <el-tab-pane label="活动会员" name="2">
          <avue-crud
            :option="doctorOption"
            :data="doctorList"
            @refresh-change="getDetail"
            ref="doctor"
          >
            <template slot-scope="{ row }" slot="doctorName">
              <div class="to-view" @click="toViewDoctor()">
                <a readonly>
                  {{ row.doctorName }}
                </a>
              </div>
            </template>
            <template slot="doctorAgreement" slot-scope="{ row }">
              <div style="cursor: pointer" v-if="row.doctorAgreement">
                <el-tag @click="previewCoursework(row.doctorAgreement)"
                  >预览合同</el-tag
                >
              </div>
              <div v-else>无</div>
            </template>
            <template slot="agreementStatus" slot-scope="{ row }">
              <el-tag v-if="row.agreementStatus == 0" size="small"
                >待签署</el-tag
              >
              <el-tag
                v-if="row.agreementStatus == 1"
                type="success"
                size="small"
                >同意</el-tag
              >
              <el-tag v-if="row.agreementStatus == 2" type="danger" size="small"
                >拒绝</el-tag
              >
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/medicationFeedbackReport/medicationFeedbackReport";
import { mapGetters } from "vuex";
import { Base64 } from "js-base64";
export default {
  props: {
    sfeId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      activeName: "1",
      id: "",
      form: {},
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "基本信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "计划订单编号",
                prop: "code",
                type: "input",
              },
              {
                label: "编码",
                prop: "codeNumber",
                type: "input",
              },
              {
                label: "客户名称",
                prop: "entrustedCompanyName",
                type: "input",
              },

              {
                label: "业务人员名称",
                prop: "baseEmployeeName",
                type: "input",
              },

              {
                label: "代表部门名称",
                prop: "baseDepartmentName",
                type: "input",
              },
            ],
          },
          {
            label: "活动信息",
            arrow: true,
            prop: "group2",
            column: [
              {
                label: "反馈药品名称",
                prop: "baseProductName",
                type: "input",
              },
              {
                label: "收集时间",
                prop: "createTime",
                type: "input",
              },
              {
                label: " 收集场所类型",
                prop: "collectPlaceType",
                type: "input",
              },
              {
                label: "收集场所名称",
                prop: "collectPlaceName",
                type: "input",
              },
              {
                label: "患者信息代号",
                prop: "patientCode",
                type: "input",
              },
              {
                label: "患者性别",
                prop: "patientSex",
                type: "select",
                dicData: [
                  {
                    label: "男",
                    value: 1,
                  },
                  {
                    label: "女",
                    value: 2,
                  },
                ],
              },
              {
                label: "患者年龄",
                prop: "patientAge",
                type: "input",
              },
              {
                label: " 疾病诊断",
                prop: "diseaseDiagnosis",
                type: "input",
              },
              {
                label: "处方来源",
                prop: "prescriptionSource",
                type: "input",
              },
              {
                label: "药品来源",
                prop: "drugSource",
                type: "input",
              },
              {
                label: "用药状态",
                prop: "medicationStatus",
                type: "input",
              },
              {
                label: "用药效果",
                prop: "medicationEffect",
                type: "input",
              },
              {
                label: "有无不适",
                prop: "isDiscomfort",
                type: "input",
              },
              {
                label: "不适表现",
                prop: "discomfortExpression",
                type: "input",
              },
              {
                label: "对患者建议",
                prop: "adviceToPatients",
                type: "input",
              },
              {
                label: "患者反馈意见",
                prop: "patientFeedback",
                type: "input",
              },
              {
                label: "疾病诊断资料",
                prop: "diseaseDiagnosisUrl",
                listType: "picture-img",
                type: "upload",
              },
              {
                label: "治疗及用药情况资料",
                prop: "treatmentOtherUrl",
                listType: "picture-img",
                type: "upload",
              },
            ],
          },
          {
            label: "初审结果",
            arrow: true,
            prop: "group3",
            column: [
              {
                label: "提交时间",
                prop: "submitTime",
                type: "input",
              },
              {
                label: "初审时间",
                prop: "approvalDate",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "初审状态",
                prop: "approvalStatus",
                type: "select",
                dicData: [
                  {
                    label: "待审核",
                    value: 0,
                  },
                  {
                    label: "通过",
                    value: 1,
                  },
                  {
                    label: "驳回",
                    value: 2,
                  },
                ],
              },
              {
                label: "初审说明",
                prop: "approvalRemark",
                type: "input",
              },
              {
                label: "初审人",
                prop: "approvalOfficer",
                type: "input",
              },
            ],
          },
          {
            label: "复审结果",
            arrow: true,
            prop: "group3",
            column: [
              {
                label: "复审时间",
                prop: "confirmDate",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "复审状态",
                prop: "confirmStatus",
                type: "select",
                dicData: [
                  {
                    label: "待验收",
                    value: 0,
                  },
                  {
                    label: "通过",
                    value: 1,
                  },
                  {
                    label: "驳回",
                    value: 2,
                  },
                ],
              },
              {
                label: "复审说明",
                prop: "confirmResult",
                type: "input",
              },
              {
                label: "复审人",
                prop: "confirmer",
                type: "input",
              },
            ],
          },
        ],
      },
      doctorList: [],
      doctorOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "部门",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职称",
            prop: "professional",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "计划数量",
            prop: "planSearchNum",
            type: "input",
          },
          {
            label: "签署状态",
            prop: "agreementStatus",
            type: "select",
            dicData: [
              {
                label: "同意",
                value: 1,
              },
              {
                label: "拒绝",
                value: 2,
              },
              {
                label: "作废",
                value: 3,
              },
            ],
          },
          {
            label: "签署时间",
            prop: "agreementTime",
            type: "input",
          },
          {
            label: "合同协议",
            prop: "doctorAgreement",
            type: "input",
          },
        ],
      },
    };
  },
  created() {
    this.id = this.sfeId;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    //去会员详情
    toViewDoctor() {
      this.$router.push({
        path: `/detailSfe/${this.form.doctorId}/1`,
      });
    },
    previewCoursework(i) {
      if (i) {
        window.open(
          "http://1.94.42.192:8886/onlinePreview?url=" +
            encodeURIComponent(Base64.encode(i))
        );
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          this.doctorList = [];
          let obj = {
            doctorName: _this.form.doctorName, //姓名
            hospitalName: _this.form.hospitalName, //单位
            departmentName: _this.form.departmentName, //部门
            professional: _this.form.professional, //职称
            duty: _this.form.duty, //职务
            planSearchNum: _this.form.planSearchNum, //计划数量
            agreementStatus: _this.form.agreementStatus, //签署状态
            agreementTime: _this.form.agreementTime, //签署时间
            doctorAgreement: _this.form.doctorAgreement, //合同协议
          };
          this.doctorList.push(obj);
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    submit() {},
  },
};
</script>

<style scoped></style>
