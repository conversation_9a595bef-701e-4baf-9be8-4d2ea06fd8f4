export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  searchLabelWidth: 130,
  selectable: (row) => {
    return row.handleType === 1;
  },
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "经办单号",
      prop: "yurref",
      type: "input",
      search: true,
      width: "205",
    },
    {
      label: "经办类型",
      prop: "handleType",
      type: "select",
      search: true,
      dicData: [
        {
          label: "首经办",
          value: 1,
        },
        {
          label: "再经办",
          value: 2,
        },
      ],
    },
    {
      label: "订单结算单号",
      prop: "settlementCode",
      type: "input",
      width: "120",
      search: true,
    },
    // {
    //   label: "客户名称",
    //   prop: "entrustedName",
    //   type: "input",
    //   width: "200",
    //   overHidden: true,
    //   hide: true,
    // },
    {
      label: "支付总金额",
      prop: "ttlamt",
      type: "input",
    },
    {
      label: "总笔数",
      prop: "ttlcnt",
      type: "input",
    },
    {
      label: "审核状态",
      prop: "approvalStatus",
      type: "select",
      search: true,
      dicData: [
        {
          label: "待提交",
          value: -1,
        },
        {
          label: "待审批",
          value: 0,
        },
        {
          label: "通过",
          value: 1,
        },
        {
          label: "驳回",
          value: 2,
        },
      ],
    },
    {
      label: "操作状态",
      prop: "state",
      type: "select",
      search: true,
      dicData: [
        {
          label: "制单",
          value: 1,
        },
        {
          label: "经办中",
          value: 2,
        },
        {
          label: "完成",
          value: 3,
        },
        {
          label: "作废",
          value: 4,
        },
        {
          label: "冻结",
          value: 5,
        },
      ],
    },
    {
      label: "请求状态",
      prop: "reqsta",
      type: "select",
      search: true,
      dicData: [
        {
          label: "数据接收中",
          value: "OPR",
        },
        {
          label: "等待审批",
          value: "AUT",
        },
        {
          label: "终审完毕",
          value: "NTE",
        },
        {
          label: "银行人工审批",
          value: "APW",
        },
        {
          label: "可疑",
          value: "WRF",
        },
        {
          label: "银行处理中",
          value: "BNK",
        },
        {
          label: "完成",
          value: "FIN",
        },
      ],
    },
    {
      label: "结果状态",
      prop: "rtnflg",
      type: "select",
      search: true,
      width: "130",
      dicData: [
        {
          label: "成功",
          value: "S",
        },
        {
          label: "失败",
          value: "F",
        },
        {
          label: "撤销",
          value: "C",
        },
        {
          label: "企业过期不审批",
          value: "D",
        },
        {
          label: "企业审批否决",
          value: "R",
        },
        {
          label: "部分成功",
          value: "P",
        },
        {
          label: "支付退回",
          value: "T",
        },
        {
          label: "失败再经办",
          value: "Z",
        },
      ],
    },
    {
      label: "期望日期",
      prop: "eptdat",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      search: true,
      searchRange: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "原经办单号",
      prop: "parentId",
      type: "input",
      width: "100",
    },
    {
      label: "用途",
      prop: "nusage",
      type: "input",
      width: "100",
    },
    {
      hide: true,
      label: "业务类型",
      prop: "buscod",
      type: "select",
      dicData: [
        {
          label: "代发",
          value: "N03020",
        },
      ],
    },
    {
      hide: true,
      label: "业务模式",
      prop: "busmod",
      type: "input",
    },

    {
      hide: true,
      label: "本次金额",
      prop: "curamt",
      type: "input",
    },

    {
      hide: true,
      label: "交易类型",
      prop: "trstyp",
      type: "input",
    },
    {
      hide: true,
      label: "总次数",
      prop: "ttlnum",
      type: "input",
    },

    {
      label: "批次开始标志",
      prop: "begtag",
      type: "input",
      hide: true,
    },
    {
      label: "批次结束标志",
      prop: "endtag",
      type: "input",
      hide: true,
    },
    {
      hide: true,
      label: "币种",
      prop: "ccynbr",
      type: "select",
      dicData: [
        {
          label: "人民币",
          value: "10",
        },
      ],
    },
    {
      hide: true,
      label: "结算通道",
      prop: "chlflg",
      type: "select",
      dicData: [
        {
          label: "超网",
          value: "Y",
        },
        {
          label: "大小额",
          value: "N",
        },
        {
          label: "智能路由",
          value: "M",
        },
        {
          label: "数币代发",
          value: "A",
        },
        {
          label: "钱包代发",
          value: "W",
        },
      ],
    },
  ],
};
