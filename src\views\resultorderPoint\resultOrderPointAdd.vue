<template>
  <div>
    <basic-container>
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <avue-form ref="form" :option="option" v-model="form" @submit="submit">
      </avue-form>
    </basic-container>
  </div>
</template>

<script>
import { submitInfo } from "@/api/resultorderPoint/resultOrderPoint";
import { mapGetters } from "vuex";
import dayjs from "dayjs";
export default {
  data() {
    return {
      activeName: "1",
      id: "",
      //基础信息
      option: {
        disabled: false,
        labelWidth: "150",
        submitIcon: " ",
        emptyIcon: " ",
        column: [
          {
            label: "客户",
            prop: "entrustedCompanyId",
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            dicUrl: `/api/blade-csc/entrustedCompany/getList?name={{key}}`,
            rules: [
              {
                required: true,
                message: "请选择客户",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "业务期间",
            prop: "accountPeriod",
            type: "month",
            format: "yyyy年MM月",
            valueFormat: "yyyy年MM月",
            rules: [
              {
                required: true,
                message: "请选择业务期间",
                trigger: ["blur", "change"],
              },
            ],
            pickerOptions: {
              disabledDate: (time) => {
                return time.getTime() > dayjs().valueOf();
              },
            },
          },
          {
            label: "服务开始日期",
            prop: "actStartTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            rules: [
              {
                required: true,
                message: "请选择服务开始日期",
                trigger: ["blur", "change"],
              },
            ],
            change: ({ value }) => {
              if (
                dayjs(value).valueOf() > dayjs(this.form.actEndTime).valueOf()
              ) {
                this.form.actEndTime = "";
              }
            },
          },
          {
            label: "服务结束日期",
            prop: "actEndTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择服务结束日期",
                trigger: ["blur", "change"],
              },
            ],
            pickerOptions: {
              disabledDate: (time) => {
                let start = dayjs(this.form.actStartTime).valueOf();
                return time.getTime() < start;
              },
            },
          },
        ],
      },
      form: {
        accountPeriod: "",
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  created() {
    this.id = this.$route.params.id;
    var year = dayjs().year();
    this.form.accountPeriod = year + "年" + month + "月";
    var month =
      dayjs().month() + 1 + 1 > 9
        ? dayjs().month() + 1
        : "0" + (dayjs().month() + 1);
    this.form.accountPeriod = year + "年" + month + "月";
  },
  methods: {
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    submit(form, done) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          form.entrustedCompanyName = this.form.$entrustedCompanyId;
          form.actEndTime = form.actEndTime + " 23:59:59";
          submitInfo(form).then(
            () => {
              this.$message({
                type: "success",
                message: "保存成功!",
              });
              done();
              this.goBack();
            },
            (error) => {
              done();
              console.log(error);
            }
          );
        }
      });
    },

    handleClick(tab) {
      if (tab.name == "1") {
        // this.getTenderDetail();
      }
    },
  },
};
</script>

<style></style>
