<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="name" slot-scope="{ row }">
        <div class="to-view" @click="toEntrustedcompany(row)" v-if="row.name">
          <a readonly>
            {{ row.name }}
          </a>
        </div>
      </template>
    </avue-crud>
  </basic-container>
</template>
<script>
import { getList } from "@/api/entrustedcompany/entrustedCompany";
import { option2 } from "@/const/taxTraceability/taxTraceability";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      customerType: "1",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option2,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.taxTraceability_add, false),
        viewBtn: this.vaildData(this.permission.taxTraceability_view, false),
        delBtn: this.vaildData(this.permission.taxTraceability_delete, false),
        editBtn: this.vaildData(this.permission.taxTraceability_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    toEntrustedcompany(row) {
      this.$router.push({
        path: `/entrustedcompany/detail/${row.id}/1`,
      });
    },
    beforeOpen(done) {
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      //注册时间
      if (!this.validatenull(params.createTime)) {
        this.query.registerStart = params.createTime[0];
        this.query.registerEnd = params.createTime[1];
        delete this.query.createTime;
      }
      //审核时间
      if (!this.validatenull(params.auditTime)) {
        this.query.auditTimeStart = params.auditTime[0];
        this.query.auditTimeEnd = params.auditTime[1];
        delete this.query.auditTime;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;

      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
