<template>
  <basic-container>
    <avue-crud
      :option="option"
      :search.sync="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- 会员订单编号 -->
      <template slot-scope="{ type, size, row, index }" slot="orderDoctorCode">
        <div
          class="to-view"
          @click="toOrderDoctor(row.orderDoctorId)"
          v-if="row.orderDoctorCode"
        >
          <a readonly>
            {{ row.orderDoctorCode }}
          </a>
        </div>
        <div v-else>无</div>
      </template>
      <template
        slot-scope="{ type, size, row, index }"
        slot="doctorSettlementCode"
      >
        <div
          class="to-view"
          @click="toOrderDoctorSettlement(row.doctorSettlementId)"
          v-if="row.doctorSettlementCode"
        >
          <a readonly>
            {{ row.doctorSettlementCode }}
          </a>
        </div>
        <div v-else>无</div>
      </template>
      <!-- 支付结果 -->
      <template slot="paymentResult" slot-scope="{ row }">
        <el-tag v-if="row.paymentResult == 1" type="warning" size="small"
          >未支付</el-tag
        >
        <el-tag v-if="row.paymentResult == 2" type="success" size="small"
          >支付成功</el-tag
        >
        <el-tag v-if="row.paymentResult == 3" type="danger" size="small"
          >支付失败</el-tag
        >
      </template>
      <template slot="menuLeft">
        <!--        <el-button type="danger"-->
        <!--                   size="small"-->
        <!--                   icon="el-icon-delete"-->
        <!--                   plain-->
        <!--                   v-if="permission.orderDoctorPayment_delete"-->
        <!--                   @click="handleDelete">删 除-->
        <!--        </el-button>-->
        <el-button
          type="warning"
          size="small"
          v-if="permission.orderDoctorPayment_export"
          plain
          icon="el-icon-download"
          @click="handleExport"
          >支付单导出
        </el-button>
        <el-button
          type="warning"
          size="small"
          v-if="permission.orderDoctorPaymentFinance_export"
          plain
          icon="el-icon-download"
          @click="handleExportFinance"
          >动资导出
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove,
} from "@/api/orderdoctorpayment/orderDoctorPayment";
import option from "@/const/orderdoctorpayment/orderDoctorPayment";
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import func from "@/util/func";
export default {
  data() {
    return {
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.orderDoctorPayment_add, false),
        viewBtn: this.vaildData(this.permission.orderDoctorPayment_view, false),
        delBtn: this.vaildData(
          this.permission.orderDoctorPayment_delete,
          false
        ),
        editBtn: this.vaildData(this.permission.orderDoctorPayment_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    toOrderDoctor(id) {
      this.$router.push({
        path: `/orderdoctor/detail/${id}`,
      });
    },
    toOrderDoctorSettlement(id) {
      this.$router.push({
        path: `/orderdoctosettlement/detail/${id}`,
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleExport() {
      const orderDoctorCode = func.toStr(this.search.orderDoctorCode);
      const doctorName = func.toStr(this.search.doctorName);
      const doctorPhone = func.toStr(this.search.doctorPhone);
      //支付时间
      let paymentTimeStart = "";
      let paymentTimeEnd = "";
      if (!this.validatenull(this.search.paymentTime)) {
        paymentTimeStart = this.search.paymentTime[0];
        paymentTimeEnd = this.search.paymentTime[1];
      }
      let values = {
        orderDoctorCode,
        doctorName,
        doctorPhone,
        paymentTimeStart,
        paymentTimeEnd,
      };
      let downloadUrl = `/api/blade-svc/orderDoctorPayment/export-orderDoctorPayment?${
        this.website.tokenHeader
      }=${getToken()}`;
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `会员订单支付明细表${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    handleExportFinance() {
      const orderDoctorCode = func.toStr(this.search.orderDoctorCode);
      const doctorName = func.toStr(this.search.doctorName);
      const doctorPhone = func.toStr(this.search.doctorPhone);
      //支付时间
      let paymentTimeStart = "";
      let paymentTimeEnd = "";
      if (!this.validatenull(this.search.paymentTime)) {
        paymentTimeStart = this.search.paymentTime[0];
        paymentTimeEnd = this.search.paymentTime[1];
      }
      let values = {
        orderDoctorCode,
        doctorName,
        doctorPhone,
        paymentTimeStart,
        paymentTimeEnd,
      };
      let downloadUrl = `/api/blade-svc/orderDoctorPayment/export-orderDoctorPaymentFinance?${
        this.website.tokenHeader
      }=${getToken()}`;
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `会员订单动资明细表${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/orderdoctorpayment/detail/${this.form.id}`,
        });
        return;
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      //支付时间
      if (!this.validatenull(params.paymentTime)) {
        this.query.paymentTimeStart = params.paymentTime[0];
        this.query.paymentTimeEnd = params.paymentTime[1];
        delete this.query.paymentTime;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
