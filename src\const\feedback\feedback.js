export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  labelWidth: 150,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  editBtn: false,
  delBtn: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "意见反馈类型",
      search: true,
      prop: "type",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=feedback_type",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
    },
    {
      label: "客户端类型",
      prop: "clientType",
      type: "input",
      search: true
    },
    // {
    //   label: "反馈用户",
    //   prop: "userId",
    //   type: "input",
    // },
    {
      label: "联系人姓名",
      prop: "name",
      type: "input",
      search: false
    },
    {
      label: "联系电话",
      prop: "phone",
      type: "input",
      search: true,
    },
    {
      label: "备用电话",
      prop: "alternativePhone",
      type: "input",
      search: true,
    },
    {
      label: "内容",
      prop: "content",
      type: "textarea",
      overHidden: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "反馈时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    // {
    //   label: "图片",
    //   prop: "imgUrl",
    //   type: 'upload',
    //   listType: 'picture-img',
    //   dataType: 'string',
    //   action: '/api/blade-resource/oss/endpoint/put-file',
    //   propsHttp: {
    //     res: 'data',
    //     url: 'link'
    //   }
    // },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
