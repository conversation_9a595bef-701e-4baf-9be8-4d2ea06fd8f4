<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      <!-- 初审 -->
      <div
        class="button"
        v-if="
          permission.medicationFeedbackReport_approval &&
          form.approvalBusinessType == 1
        "
      >
        <el-button
          type="success"
          v-if="form.confirmStatus == 0 || form.approvalStatus == 0"
          @click="onAudit(1)"
          >通过</el-button
        >
        <el-button
          type="danger"
          v-if="form.confirmStatus == 0 || form.approvalStatus == 0"
          @click="onAudit(2)"
          >驳回</el-button
        >
      </div>
      <!-- 复审 -->
      <div
        class="button"
        v-if="
          permission.medicationFeedbackReport_confirm &&
          form.approvalBusinessType == 2
        "
      >
        <el-button
          type="success"
          v-if="form.confirmStatus == 0 || form.approvalStatus == 0"
          @click="onAudit(1)"
          >通过</el-button
        >
        <el-button
          type="danger"
          v-if="form.confirmStatus == 0 || form.approvalStatus == 0"
          @click="onAudit(2)"
          >驳回</el-button
        >
      </div>
    </basic-container>
    <basic-container>
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="1">
          <avue-form
            v-if="!validatenull(form)"
            ref="form"
            :option="option"
            v-model="form"
            :upload-preview="uploadPreview"
            @submit="submit"
          >
            <template slot-scope="{}" slot="patientFeedback">
              <el-popover
                placement="top-start"
                width="200"
                trigger="hover"
                :content="form.patientFeedback"
              >
                <div style="display: flex" slot="reference">
                  <el-input
                    disabled
                    v-model="form.patientFeedback"
                    placeholder="请输入内容"
                  ></el-input>
                </div>
              </el-popover>
            </template>
            <template slot-scope="{}" slot="confirmResult">
              <el-popover
                placement="top-start"
                width="200"
                trigger="hover"
                :content="form.confirmResult"
              >
                <div style="display: flex" slot="reference">
                  <el-input
                    disabled
                    v-model="form.confirmResult"
                    placeholder="请输入内容"
                  ></el-input>
                </div>
              </el-popover>
            </template>
          </avue-form>
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>
        <el-tab-pane label="活动会员" name="2">
          <avue-crud
            :option="doctorOption"
            :data="doctorList"
            @refresh-change="getDetail"
            ref="doctor"
          >
            <template slot-scope="{ row }" slot="doctorName">
              <div class="to-view" @click="toViewDoctor()">
                <a readonly>
                  {{ row.doctorName }}
                </a>
              </div>
            </template>
            <template slot="doctorAgreement" slot-scope="{ row }">
              <div style="cursor: pointer" v-if="row.doctorAgreement">
                <el-tag @click="previewPDF(row.doctorAgreement)"
                  >预览合同</el-tag
                >
              </div>
              <div v-else>无</div>
            </template>
            <template slot="agreementStatus" slot-scope="{ row }">
              <el-tag v-if="row.agreementStatus == 0" size="small"
                >待签署</el-tag
              >
              <el-tag
                v-if="row.agreementStatus == 1"
                type="success"
                size="small"
                >同意</el-tag
              >
              <el-tag v-if="row.agreementStatus == 2" type="danger" size="small"
                >拒绝</el-tag
              >
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
    <!-- 审核驳回 -->
    <el-dialog
      title="驳回"
      @close="auditForm = {}"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="auditDialogVisible"
      width="30%"
    >
      <avue-form
        v-if="auditDialogVisible"
        ref="auditForm"
        :option="auditOption"
        v-model="auditForm"
      >
        <template slot-scope="{ disabled, size }" slot="quickReplyList">
          <div class="quickReplyList">
            <el-tag
              style="cursor: pointer"
              :key="tag.id"
              v-for="tag in quickReplyList"
              :disable-transitions="false"
              @click="clickQuickReply(tag)"
            >
              {{ tag.content }}
            </el-tag>
          </div>
        </template>
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取 消</el-button>
        <el-button :loading="auditLoading" type="primary" @click="auditClick()"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <image-viewer z-index="2000"   :initial-index="previewIndex"  v-if="previewSrcList.length > 0" :on-close="closeViewer" :url-list="previewSrcList"/>
  </div>
</template>

<script>
import ImageViewer from 'element-ui/packages/image/src/image-viewer'
import {audit, getDetail,} from "@/api/medicationFeedbackReport/medicationFeedbackReport";
import {mapGetters} from "vuex";
import {projectQuickReplyList} from "@/api/sysprojectQuickReply/projectQuickReply";

export default {
  components: { ImageViewer },
  data() {
    return {
      previewSrcList: [],
      previewIndex: 0,
      activeName: "1",
      //审核弹窗
      quickReplyList: [],
      auditLoading: false,
      auditDialogVisible: false,
      auditForm: {},
      auditOption: {
        submitText: "完成",
        span: 24,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "验收说明",
            prop: "auditContent",
            type: "textarea",
            maxlength: 500,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入验收说明",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "快捷回复",
            prop: "quickReplyList",
            type: "input",
          },
        ],
      },

      //end
      id: "",
      form: {},
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "基本信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "计划订单编号",
                prop: "code",
                type: "input",
              },
              {
                label: "编码",
                prop: "codeNumber",
                type: "input",
              },
              {
                label: "客户名称",
                prop: "entrustedCompanyName",
                type: "input",
              },

              {
                label: "业务人员名称",
                prop: "baseEmployeeName",
                type: "input",
              },

              {
                label: "代表部门名称",
                prop: "baseDepartmentName",
                type: "input",
              },
            ],
          },
          {
            label: "活动信息",
            arrow: true,
            prop: "group2",
            column: [
              {
                label: "反馈药品名称",
                prop: "baseProductName",
                type: "input",
              },
              {
                label: "收集时间",
                prop: "createTime",
                type: "input",
              },
              {
                label: " 收集场所类型",
                prop: "collectPlaceType",
                type: "input",
              },
              {
                label: "收集场所名称",
                prop: "collectPlaceName",
                type: "input",
              },
              {
                label: "患者信息代号",
                prop: "patientCode",
                type: "input",
              },
              {
                label: "患者性别",
                prop: "patientSex",
                type: "select",
                dicData: [
                  {
                    label: "男",
                    value: 1,
                  },
                  {
                    label: "女",
                    value: 2,
                  },
                ],
              },
              {
                label: "患者年龄",
                prop: "patientAge",
                type: "input",
              },
              {
                label: " 疾病诊断",
                prop: "diseaseDiagnosis",
                type: "input",
              },
              {
                label: "处方来源",
                prop: "prescriptionSource",
                type: "input",
              },
              {
                label: "药品来源",
                prop: "drugSource",
                type: "input",
              },
              {
                label: "用药状态",
                prop: "medicationStatus",
                type: "input",
              },
              {
                label: "用药效果",
                prop: "medicationEffect",
                type: "input",
              },
              {
                label: "有无不适",
                prop: "isDiscomfort",
                type: "input",
              },
              {
                label: "不适表现",
                prop: "discomfortExpression",
                type: "input",
              },
              {
                label: "对患者建议",
                prop: "adviceToPatients",
                type: "input",
              },
              {
                label: "患者反馈意见",
                prop: "patientFeedback",
                type: "input",
              },
              {
                label: "疾病诊断资料",
                prop: "diseaseDiagnosisUrl",
                listType: "picture-card",
                type: "upload",
                limit: 5,
              },
              {
                label: "治疗及用药情况资料",
                prop: "treatmentOtherUrl",
                listType: "picture-card",
                type: "upload",
                limit: 5,
              },
            ],
          },
          {
            label: "初审结果",
            arrow: true,
            prop: "group3",
            column: [
              {
                label: "提交时间",
                prop: "submitTime",
                type: "input",
              },
              {
                label: "初审时间",
                prop: "approvalDate",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "初审状态",
                prop: "approvalStatus",
                type: "select",
                dicData: [
                  {
                    label: "待审核",
                    value: 0,
                  },
                  {
                    label: "通过",
                    value: 1,
                  },
                  {
                    label: "驳回",
                    value: 2,
                  },
                ],
              },
              {
                label: "初审说明",
                prop: "approvalRemark",
                type: "input",
              },
              {
                label: "初审人",
                prop: "approvalOfficer",
                type: "input",
              },
            ],
          },
          {
            label: "复审结果",
            arrow: true,
            prop: "group3",
            column: [
              {
                label: "复审时间",
                prop: "confirmDate",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "复审状态",
                prop: "confirmStatus",
                type: "select",
                dicData: [
                  {
                    label: "待验收",
                    value: 0,
                  },
                  {
                    label: "通过",
                    value: 1,
                  },
                  {
                    label: "驳回",
                    value: 2,
                  },
                ],
              },
              {
                label: "复审说明",
                prop: "confirmResult",
                type: "input",
              },
              {
                label: "复审人",
                prop: "confirmer",
                type: "input",
              },
            ],
          },
        ],
      },
      doctorList: [],
      doctorOption: {
        searchShowBtn: false,
        columnBtn: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: true,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "部门",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职称",
            prop: "professional",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "计划数量",
            prop: "planSearchNum",
            type: "input",
          },
          {
            label: "签署状态",
            prop: "agreementStatus",
            type: "select",
            dicData: [
              {
                label: "同意",
                value: 1,
              },
              {
                label: "拒绝",
                value: 2,
              },
              {
                label: "作废",
                value: 3,
              },
            ],
          },
          {
            label: "签署时间",
            prop: "agreementTime",
            type: "input",
          },
          {
            label: "合同协议",
            prop: "doctorAgreement",
            type: "input",
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    uploadPreview(image,option) {
        const { url, prop } = {...image, ...option}
        if(prop === 'diseaseDiagnosisUrl' || prop === 'treatmentOtherUrl') {
            this.previewSrcList= [...this.form.diseaseDiagnosisUrl.split(','),...this.form.treatmentOtherUrl.split(',')].filter(src=> src)
            this.previewIndex = this.previewSrcList.indexOf(url); // 获取当前点击的图片索引
        }  else {
            this.previewSrcList = [url]
        }
    },
    closeViewer() {
      this.previewSrcList = []
    },
    //验收
    onAudit(auditStatus) {
      let _this = this;
      if (auditStatus == 1) {
        this.$confirm("确定将该条数据审核通过?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let data = {
              auditStatus: auditStatus,
              id: _this.id,
              approvalBusinessType: _this.form.approvalBusinessType,
              auditContent: "同意",
            };
            return audit(data);
          })
          .then(() => {
            // this.getDetail();
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.goBack()
          });
      } else {
        projectQuickReplyList({ moduleCode: 'medicationFeedbackReport' }).then(({ data: res }) => {
          this.quickReplyList = res.data;
          this.auditForm.auditStatus = auditStatus;
          this.auditForm.id = _this.id;
          this.auditForm.approvalBusinessType = this.form.approvalBusinessType;
          this.auditDialogVisible = true;
        });
      }
    },
    clickQuickReply(tag) {
      this.auditForm.auditContent = tag.content;
    },
    auditClick() {
      this.$refs.auditForm.validate((valid) => {
        if (valid) {
          this.auditLoading = true;
          audit(this.auditForm).then(
            () => {
              this.auditDialogVisible = false;
              this.auditLoading = false;
              // this.getDetail();
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              this.goBack()
            },
            (error) => {
              console.log(error);
              this.auditLoading = false;
            }
          );
        }
      });
    },
    //去会员详情
    toViewDoctor() {
      this.$router.push({
        path: `/authenticationDoctor/detail/${this.form.doctorId}/1`,
      });
    },
    // 打开新页面
    toopen(i) {
      window.open(i, "_blank");
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    handleClick() {},
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          this.doctorList = [];
          let obj = {
            doctorName: _this.form.doctorName, //姓名
            hospitalName: _this.form.hospitalName, //单位
            departmentName: _this.form.departmentName, //部门
            professional: _this.form.professional, //职称
            duty: _this.form.duty, //职务
            planSearchNum: _this.form.planSearchNum, //计划数量
            agreementStatus: _this.form.agreementStatus, //签署状态
            agreementTime: _this.form.agreementTime, //签署时间
            doctorAgreement: _this.form.doctorAgreement, //合同协议
          };
          this.doctorList.push(obj);
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    previewPDF(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    submit() {},
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}

.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
