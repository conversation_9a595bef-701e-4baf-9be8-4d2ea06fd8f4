<template>
  <div class="login-container" ref="login" @keyup.enter.native="handleLogin">
    <div class="login-container-left">
      <div class="login-img">
        <img src="../../../public/img/login/logo.png" alt="" />
      </div>
      <div class="login-img2">
<!--        <img src="../../../public/img/login/banner2.png" alt="" />-->
      </div>
    </div>
    <div class="login-container-right">
      <div class="login-main">
        <span class="login-title">欢迎登录</span>
        <el-tabs v-model="activeName">
          <el-tab-pane label="密码登录" name="user"></el-tab-pane>
          <el-tab-pane label="手机号登录" name="code"></el-tab-pane>
        </el-tabs>
        <userLogin v-if="activeName === 'user'"></userLogin>
        <codeLogin v-else-if="activeName === 'code'"></codeLogin>
        <thirdLogin v-else-if="activeName === 'third'"></thirdLogin>
      </div>
    </div>
    <div style="position: fixed; bottom: 1%; left: 50%">
      <span class="footer-copyright" @click="toCopyright"
        >苏ICP备2022026988号-3</span
      >
    </div>
  </div>
</template>
<script>
import userLogin from "./userlogin";
import codeLogin from "./codelogin";
import thirdLogin from "./thirdlogin";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
import { validatenull } from "@/util/validate";
import topLang from "@/page/index/top/top-lang";
import topColor from "@/page/index/top/top-color";
import { getQueryString, getTopUrl } from "@/util/util";

export default {
  name: "login",
  components: {
    userLogin,
    codeLogin,
    thirdLogin,
    topLang,
    topColor,
  },
  data() {
    return {
      time: "",
      activeName: "user",
      socialForm: {
        tenantId: "000000",
        source: "",
        code: "",
        state: "",
      },
    };
  },
  watch: {
    $route() {
      this.handleLogin();
    },
  },
  created() {
    this.handleLogin();
    this.getTime();
  },
  mounted() {},
  computed: {
    ...mapGetters(["website", "tagWel"]),
  },
  props: [],
  methods: {
    toCopyright() {
      window.open("https://beian.miit.gov.cn/#/Integrated/index");
    },
    getTime() {
      setInterval(() => {
        this.time = dateFormat(new Date());
      }, 1000);
    },
    handleLogin() {
      const topUrl = getTopUrl();
      const redirectUrl = "/oauth/redirect/";
      const ssoCode = "?code=";
      this.socialForm.source = getQueryString("source");
      this.socialForm.code = getQueryString("code");
      this.socialForm.state = getQueryString("state");
      if (
        validatenull(this.socialForm.source) &&
        topUrl.includes(redirectUrl)
      ) {
        let source = topUrl.split("?")[0];
        source = source.split(redirectUrl)[1];
        this.socialForm.source = source;
      }
      if (
        topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.source) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: "第三方系统登录中,请稍后。。。",
          spinner: "el-icon-loading",
        });
        this.$store
          .dispatch("LoginBySocial", this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(redirectUrl)[0];
            this.$router.push({ path: this.tagWel.value });
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      } else if (
        !topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: "单点系统登录中,请稍后。。。",
          spinner: "el-icon-loading",
        });
        this.$store
          .dispatch("LoginBySso", this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(ssoCode)[0];
            this.$router.push({ path: this.tagWel.value });
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      }
    },
  },
};
</script>

<style lang="scss">
@import "@/styles/login.scss";
</style>
