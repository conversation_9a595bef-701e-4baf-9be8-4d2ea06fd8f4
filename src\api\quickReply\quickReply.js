import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-sys/quickReply/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-sys/quickReply/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-sys/quickReply/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-sys/quickReply/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-sys/quickReply/submit",
    method: "post",
    data: row,
  });
};
//获取快捷回复内容
export const quickReplyList = () => {
  return request({
    url: "/api/blade-sys/quickReply/quickReplyList",
    method: "get",
    params: {},
  });
};
