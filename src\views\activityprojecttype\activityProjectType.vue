<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="clinicalCooperation" slot-scope="{ row }">
        <el-tag v-if="row.clinicalCooperation == 0" type="info" size="small"
          >否</el-tag
        >
        <el-tag v-if="row.clinicalCooperation == 1" type="success" size="small"
          >是</el-tag
        >
      </template>
    </avue-crud>

    <el-dialog
      title="详情"
      append-to-body
      :visible.sync="dialogVisible"
      width="60%"
    >
      <div class="avueForm">
        <avue-form ref="form" :option="viewOption" v-model="form" />
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from "@/api/activityprojecttype/activityProjectType";
import option from "@/const/activityprojecttype/activityProjectType";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      //详情
      dialogVisible: false,
      viewOption: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        column: [
          {
            label: "服务项目编号",
            prop: "projectCode",
            type: "input",

            rules: [
              {
                required: true,
                message: "请输入项目编号",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "服务项目大类",
            prop: "projectCategory",
            type: "select",
            rules: [
              {
                required: true,
                message: "请选择服务项目大类",
                trigger: ["blur", "change"],
              },
            ],
            dicData: [
              {
                label: "推广交流服务",
                value: "推广交流服务",
              },
              {
                label: "演讲培训服务",
                value: "演讲培训服务",
              },
              {
                label: "专业技术服务",
                value: "专业技术服务",
              },
              {
                label: "信息调研服务",
                value: "信息调研服务",
              },
              {
                label: "健康咨询服务",
                value: "健康咨询服务",
              },
            ],
          },
          {
            label: "服务项目名称",
            prop: "projectTypeName",
            type: "input",

            rules: [
              {
                required: true,
                message: "请输入服务项目名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "关联活动类型",
            prop: "actType",
            type: "select",
            rules: [
              {
                required: true,
                message: "请选择活动类型",
                trigger: ["blur", "change"],
              },
            ],
            dicData: [
              {
                label: "学术会议",
                value: 1,
              },
              {
                label: "知识创作",
                value: 2,
              },
              {
                label: "知识传播",
                value: 3,
              },
              {
                label: "临床调研",
                value: 4,
              },
              {
                label: "用药反馈",
                value: 5,
              },
              {
                label: "专业评审",
                value: 6,
              },
              {
                label: "病例征集",
                value: 8,
              },
            ],
          },
          {
            label: "服务收入性质",
            prop: "natureOfIncome",
            type: "select",

            rules: [
              {
                required: true,
                message: "请选择服务收入性质",
                trigger: ["blur", "change"],
              },
            ],
            dicData: [
              {
                label: "经营所得",
                value: 1,
              },
              {
                label: "劳务报酬",
                value: 2,
              },
            ],
          },

          {
            label: "应税项目名称",
            prop: "projectTaxItemName",
            type: "input",

            rules: [
              {
                required: true,
                message: "请输入应税项目名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "规格型号",
            prop: "specification",
            type: "input",
          },
          {
            label: "单位",
            prop: "expenceUnit",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入单位",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "医健服务项目说明",
            prop: "projectNote",
            type: "textarea",
            maxlength: 1000,
            span: 24,
          },
          {
            label: "服务合规验收要求",
            prop: "checkRequire",
            type: "textarea",
            hide: true,
            maxlength: 1000,
            showWordLimit: true,
            span: 24,
          },
          {
            label: "医健服务成果规范",
            prop: "resultRequire",
            type: "textarea",
            hide: true,
            maxlength: 1000,
            showWordLimit: true,
            span: 24,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.activityProjectType_add, false),
        viewBtn: this.vaildData(
          this.permission.activityProjectType_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.activityProjectType_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.activityProjectType_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          done();
        });
      } else if (type == "view") {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          this.dialogVisible = true;
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style scoped>
.avueForm {
  width: 95%;
  margin: 0 auto;
}
</style>
