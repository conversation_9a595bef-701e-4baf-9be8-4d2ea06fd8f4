<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button :size="option.size" type="text" @click="recharge(row)"
          >充值</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  recharge,
} from "@/api/entrustedaccount/entrustedAccount";
import option from "@/const/entrustedaccount/entrustedAccount";
import { mapGetters } from "vuex";

export default {
  data() {
    var checkNum = (rule, value, callback) => {
      if (isNaN(value)) {
        callback(new Error("请输入数字值"));
      } else {
        callback();
      }
    };
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      dialogOption: {
        submitText: "完成",
        span: 24,
        labelWidth: 110,
        column: [
          {
            label: "充值金额",
            prop: "recordAmount",
            type: "input",
            maxlength: 50,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入充值金额",
                trigger: ["blur", "change"],
              },
              {
                validator: checkNum,
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "凭证单号",
            prop: "recordVoucher",
            type: "input",
            maxlength: 50,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入凭证单号",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "记账业务描述",
            prop: "recordDesc",
            type: "textarea",
            maxlength: 150,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入记账业务描述",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.entrustedAccount_add, false),
        viewBtn: this.vaildData(this.permission.entrustedAccount_view, false),
        delBtn: this.vaildData(this.permission.entrustedAccount_delete, false),
        editBtn: this.vaildData(this.permission.entrustedAccount_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    recharge(row) {
      let _this = this;
      _this.$DialogForm.show({
        title: "充值",
        width: "30%",
        menuPosition: "right",
        option: this.dialogOption,
        beforeClose: (done) => {
          setTimeout(() => {
            done();
          }, 100);
        },
        callback: (res) => {
          res.data.entrustedCompanyId = row.entrustedCompanyId;
          console.log(res.data);
          recharge(res.data).then(
            () => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              res.close();
            },
            (error) => {
              res.done();
              console.log(error);
            }
          );
        },
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/entrustedaccount/entrustedAccountRecord/${this.form.id}`,
        });
      } else if (type == "view") {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
