<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="auditStatus" slot-scope="{ row }">
        <el-tag v-if="row.auditStatus == 1" type="info" size="small"
          >待审核</el-tag
        >
        <el-tag v-if="row.auditStatus == 2" type="success" size="small"
          >已注销</el-tag
        >
        <el-tag v-if="row.auditStatus == 3" type="danger" size="small"
          >已拒绝</el-tag
        >
      </template>
      <template slot="menu" slot-scope="{ row }">
        <el-button
          type="text"
          v-if="row.auditStatus == 1 && permission.cancellationRecord_audit"
          @click="onAudit(row, 2)"
          >同 意
        </el-button>
        <el-button
          type="text"
          v-if="row.auditStatus == 1 && permission.cancellationRecord_audit"
          @click="onAudit(row, 3)"
          >拒 绝
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  audit,
} from "@/api/cancellationRecord/cancellationRecord";
import option from "@/const/cancellationRecord/cancellationRecord";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      dialogOption: {
        submitText: "完成",
        span: 24,
        column: [
          {
            label: "审核备注",
            prop: "auditCause",
            type: "textarea",
            maxlength: 150,
            showWordLimit: true,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.cancellationRecord_add, false),
        viewBtn: this.vaildData(this.permission.cancellationRecord_view, false),
        delBtn: this.vaildData(
          this.permission.cancellationRecord_delete,
          false
        ),
        editBtn: this.vaildData(this.permission.cancellationRecord_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    onAudit(row, auditStatus) {
      let _this = this;
      if (auditStatus == 2) {
        this.$confirm("确定将该条数据同意注销?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let data = {
              auditStatus: auditStatus,
              id: row.id,
              auditCause: "同意",
            };
            return audit(data);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      } else {
        _this.$DialogForm.show({
          title: "拒绝注销",
          width: "30%",
          menuPosition: "right",
          option: this.dialogOption,
          beforeClose: (done) => {
            setTimeout(() => {
              done();
            }, 100);
          },
          callback: (res) => {
            res.data.auditStatus = auditStatus;
            res.data.id = row.id;
            console.log(res.data);
            audit(res.data).then(
              () => {
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
                res.close();
              },
              (error) => {
                res.done();
                console.log(error);
              }
            );
          },
        });
      }
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
