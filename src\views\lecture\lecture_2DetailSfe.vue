<template>
  <div style="overflow-y: auto; height: 100vh">
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
        @submit="submit"
      >
        <template slot-scope="{ disabled, size }" slot="entrustAgreement">
          <div style="cursor: pointer">
            <el-tag @click="preview(form.entrustAgreement)">预览附件</el-tag>
          </div>
        </template>
        <!-- 邀请讲者 -->
        <template slot-scope="{ type, disabled }" slot="invitingSpeakersList">
          <div style="">
            <div style="width: 95%; margin: 0 auto">
              <avue-crud
                :option="invitingSpeakersListOption"
                :data="invitingSpeakersList"
              >
                <template slot="doctorName" slot-scope="{ row }">
                  <div class="to-view" @click="toDoctorView(row)">
                    <a readonly>
                      {{ row.doctorName }}
                    </a>
                  </div>
                </template>
                <template slot-scope="{ row }" slot="doctorAgreement">
                  <div style="cursor: pointer" v-if="row.doctorAgreement">
                    <el-tag @click="preview(row.doctorAgreement)"
                      >预览合同</el-tag
                    >
                  </div>
                  <div v-else>无</div>
                </template>
              </avue-crud>
            </div>
          </div>
        </template>
        <!-- 附件Tab -->
        <template slot-scope="{ type, disabled }" slot="fileLecture">
          <div style="padding: 0 30px">
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <!-- <el-tab-pane label="文件" name="first">
                  <avue-crud
                    :option="fileOption"
                    :data="fileData"
                    :page.sync="filePage"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                  >
                    <template slot-scope="{ type, size, row, index }" slot="menu">
                      <el-button :size="size" type="text" @click="toOpen(row.url)"
                        >预览
                      </el-button>
                      <el-button
                        :size="size"
                        type="text"
                        @click="downloadImage(row.url, row.name)"
                        >下载
                      </el-button>
                    </template>
                  </avue-crud>
                </el-tab-pane>
                <el-tab-pane label="音频" name="second">
                  <avue-crud
                    :option="audioOption"
                    :data="audioData"
                    :page.sync="filePage"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                  >
                    <template slot-scope="{ row }" slot="url">
                      <audio :src="row.url" controls="controls"></audio>
                    </template>
                  </avue-crud>
                </el-tab-pane> -->
              <el-tab-pane label="视频" name="third">
                <avue-crud
                  :option="videoOption"
                  :data="videoData"
                  :page.sync="filePage"
                  @size-change="sizeChange"
                  @current-change="currentChange"
                >
                  <template slot-scope="{ type, size, row, index }" slot="menu">
                    <el-button :size="size" type="text" @click="toOpen(row.url)"
                      >预览
                    </el-button>
                  </template>
                </avue-crud>
              </el-tab-pane>
            </el-tabs>
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
  </div>
</template>

<script>
import { getDetail, getFileList } from "@/api/lecture/lecture";
import { Base64 } from "js-base64";
import { mapGetters } from "vuex";
import { downloadFileBlob } from "@/util/util";
export default {
  props: {
    sfeId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      activeName: "third",
      filePage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      id: "",
      form: {},
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "150",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "基本信息",
            arrow: true,
            prop: "basicGroup",
            column: [
              {
                label: "活动编号",
                prop: "code",
                type: "input",
              },
              {
                label: "活动名称",
                prop: "name",
                type: "input",
              },
              {
                label: "活动类型",
                prop: "type",
                type: "select",
                dicData: [
                  {
                    label: "大讲堂",
                    value: 1,
                  },
                  {
                    label: "专业录播",
                    value: 2,
                  },
                  {
                    label: "微学堂",
                    value: 3,
                  },
                  {
                    label: "杏林集萃",
                    value: 4,
                  },
                ],
              },
              {
                label: "计划开始时间",
                prop: "invitationStartDate",
                type: "input",
              },
              {
                label: "计划结束时间",
                prop: "invitationEndDate",
                type: "input",
              },
              {
                label: "客户名称",
                prop: "entrustedCompanyName",
                type: "input",
                overHidden: true,
              },
              {
                label: "预算金额",
                prop: "budgetAmount",
                type: "input",
              },
              {
                label: "活动负责人",
                prop: "baseEmployeeName",
                type: "input",
              },
              {
                label: "负责人部门",
                prop: "baseDepartmentName",
                type: "input",
              },
              {
                label: "产品名称",
                prop: "baseProductName",
                type: "input",
              },
              {
                label: "产品线",
                prop: "baseProductLine",
                type: "input",
              },
            ],
          },
          {
            label: "课程内容",
            arrow: true,
            prop: "lectureGroup",
            column: [
              {
                label: "课程名称",
                prop: "courseName",
                type: "input",
                row: true,
              },

              {
                label: "课程封面图",
                prop: "courseCoverImage",
                listType: "picture-img",
                type: "upload",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },

              {
                span: 24,
                labelPosition: "top",
                label: "",
                prop: "fileLecture",
                type: "input",
              },
            ],
          },
          {
            label: "活动讲者",
            arrow: true,
            prop: "inviteGroup",
            column: [
              {
                span: 24,
                labelPosition: "top",
                label: "",
                prop: "invitingSpeakersList",
                type: "input",
              },
            ],
          },
          {
            label: "初审结果",
            arrow: true,
            prop: "firstGroup",
            column: [
              {
                label: "提交时间",
                prop: "submitTime",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "初审时间",
                prop: "approvalDate",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "初审状态",
                prop: "approvalStatus",
                type: "select",
                dicData: [
                  {
                    label: "待审核",
                    value: 0,
                  },
                  {
                    label: "通过",
                    value: 1,
                  },
                  {
                    label: "驳回",
                    value: 2,
                  },
                ],
              },
              {
                label: "初审说明",
                prop: "approvalRemark",
                type: "input",
              },
              {
                label: "初审人",
                prop: "approvalOfficer",
                type: "input",
              },
            ],
          },
          {
            label: "复审结果",
            arrow: true,
            prop: "reviewGroup",
            column: [
              {
                label: "复审时间",
                prop: "confirmDate",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "复审状态",
                prop: "confirmStatus",
                type: "select",
                dicData: [
                  {
                    label: "待验收",
                    value: 0,
                  },
                  {
                    label: "通过",
                    value: 1,
                  },
                  {
                    label: "驳回",
                    value: 2,
                  },
                ],
              },
              {
                label: "复审说明",
                prop: "confirmResult",
                type: "input",
              },
              {
                label: "复审人",
                prop: "confirmer",
                type: "input",
              },
            ],
          },
        ],
      },
      //邀请讲者信息

      invitingSpeakersListOption: {
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        refreshBtn: false,
        columnBtn: false,
        menu: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "单位",
            prop: "hospitalName",
            type: "input",
          },
          {
            label: "部门",
            prop: "departmentName",
            type: "input",
          },
          {
            label: "职称",
            prop: "professional",
            type: "input",
          },
          {
            label: "职务",
            prop: "duty",
            type: "input",
          },
          {
            label: "讲者级别",
            prop: "doctorLevel",
            type: "input",
          },
          {
            label: "计划数量",
            prop: "planSearchNum",
            type: "input",
          },

          {
            label: "签署状态",
            prop: "agreementStatus",
            type: "select",
            dicData: [
              {
                label: "待签署",
                value: 0,
              },
              {
                label: "同意",
                value: 1,
              },
              {
                label: "拒绝",
                value: 2,
              },
              {
                label: "作废",
                value: 3,
              },
            ],
          },
          {
            label: "签署时间",
            prop: "agreementTime",
            type: "input",
          },
          {
            label: "合同协议",
            prop: "doctorAgreement",
            width: "100",
            type: "input",
          },
        ],
      },
      invitingSpeakersList: [],
      //
      fileOption: {
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        refreshBtn: false,
        columnBtn: false,
        column: [
          {
            label: "文件名称",
            prop: "name",
            type: "input",
          },
        ],
      },
      fileData: [],
      audioOption: {
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        menu: false,
        delBtn: false,
        refreshBtn: false,
        columnBtn: false,
        column: [
          {
            label: "音频名称",
            prop: "url",
            type: "input",
          },
        ],
      },
      audioData: [],
      videoOption: {
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        refreshBtn: false,
        columnBtn: false,
        column: [
          {
            label: "视频",
            prop: "url",
            listType: "picture-img",
            type: "upload",
            action: "/api/blade-resource/oss/endpoint/put-file",
            propsHttp: {
              res: "data",
              url: "link",
            },
          },
        ],
      },
      videoData: [],
    };
  },
  created() {
    this.id = this.sfeId;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    // 下载
    downloadImage(url, name) {
      downloadFileBlob(url, name);
    },

    // 打开新页面
    toOpen(i) {
      window.open(
        "http://1.94.42.192:8886/onlinePreview?url=" +
          encodeURIComponent(Base64.encode(i))
      );
    },
    preview(i) {
      if (i) {
        window.open(
          "http://1.94.42.192:8886/onlinePreview?url=" +
            encodeURIComponent(Base64.encode(i))
        );
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    sizeChange(val) {
      this.filePage.currentPage = 1;
      this.filePage.pageSize = val;
      if (this.activeName == "first") {
        this.getFile(1);
      } else if (this.activeName == "second") {
        this.getFile(2);
      } else {
        this.getFile(3);
      }
    },
    currentChange(val) {
      this.filePage.currentPage = val;
      if (this.activeName == "first") {
        this.getFile(1);
      } else if (this.activeName == "second") {
        this.getFile(2);
      } else {
        this.getFile(3);
      }
    },

    handleClick(tab) {
      this.filePage.currentPage = 1;
      this.filePage.pageSize = 10;
      this.filePage.total = 0;
      if (tab.name == "first") {
        this.getFile(1);
      } else if (tab.name == "second") {
        this.getFile(2);
      } else {
        this.getFile(3);
      }
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          _this.invitingSpeakersList = res.data.data.doctorList;
          this.getFile(3);
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    // 获取文件
    getFile(type) {
      getFileList(this.filePage.currentPage, this.filePage.pageSize, {
        activityId_equal: this.form.id,
        materialType_equal: type,
      }).then((res) => {
        if (this.activeName == "first") {
          this.fileData = res.data.data.records;
        } else if (this.activeName == "second") {
          this.audioData = res.data.data.records;
        } else {
          this.videoData = res.data.data.records;
        }
        this.filePage.total = res.data.data.total;
      });
    },
    toDoctorView(row) {
      this.$router.push({
        path: `/detailSfe/${row.doctorId ? row.doctorId : 0}/1`,
      });
    },
    submit() {},
  },
};
</script>

<style lang="scss">
.el-card__body {
  position: relative;
}

.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
