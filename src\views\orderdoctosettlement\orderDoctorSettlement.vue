<template>
  <basic-container>
    <avue-crud
      :option="option"
      :search.sync="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- 是否申请打款支付 -->
      <template slot="isApplyPayment" slot-scope="{ row }">
        <el-tag v-if="row.isApplyPayment == 0" type="info" size="small"
          >未申请</el-tag
        >
        <el-tag v-if="row.isApplyPayment == 1" type="warning" size="small"
          >已申请待经办</el-tag
        >
        <el-tag v-if="row.isApplyPayment == 2" type="success" size="small"
          >支付成功</el-tag
        >
        <el-tag v-if="row.isApplyPayment == 3" type="danger" size="small"
          >支付失败</el-tag
        >
        <el-tag v-if="row.isApplyPayment == 4" type="danger" size="small"
          >已作废</el-tag
        >
      </template>
      <!-- 是否完成纳税申报 -->
      <template slot="isTaxDeclaration" slot-scope="{ row }">
        <el-tag v-if="row.isTaxDeclaration == 0" type="info" size="small"
          >未申报</el-tag
        >
        <el-tag v-if="row.isTaxDeclaration == 1" type="success" size="small"
          >已申报</el-tag
        >
      </template>
      <!-- 会员订单编号 -->
      <template slot-scope="{ type, size, row, index }" slot="orderDoctorCode">
        <div
          class="to-view"
          @click="toOrderDoctor(row.orderDoctorId)"
          v-if="row.orderDoctorCode"
        >
          <a readonly>
            {{ row.orderDoctorCode }}
          </a>
        </div>
        <div v-else>无</div>
      </template>
      <template slot="menuLeft">
        <el-button
          v-if="permission.orderDoctorSettlement_applyForPayment"
          type="primary"
          @click="applyForPayment"
          >申请打款支付
        </el-button>
      </template>
      <template slot="menuLeft">
        <el-button
          v-if="permission.orderDoctorSettlement_applyForVoid"
          type="danger"
          @click="applyForVoid"
          >作废
        </el-button>
        <el-button
          type="primary"
          @click="handleExportFinance"
          v-if="permission.orderDoctorSettlement_export"
          >导 出
        </el-button>
      </template>
      <template slot="menuLeft">
        <el-button
          v-if="permission.orderDoctorSettlement_applyForPayment"
          type="primary"
          @click="simulatedPayment"
          >模拟支付
        </el-button>
      </template>
      <template slot="menuLeft">
        <el-button
          v-if="permission.orderDoctorSettlement_blackList"
          type="primary"
          @click="blackListClick"
        >黑名单
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove,
  applyForPayment,
  simulatedPayment,
  applyForVoid,
} from "@/api/orderdoctosettlement/orderDoctorSettlement";
import option from "@/const/orderdoctosettlement/orderDoctorSettlement";
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from "nprogress";
import "nprogress/nprogress.css";

export default {
  data() {
    return {
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.orderDoctorSettlement_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.orderDoctorSettlement_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.orderDoctorSettlement_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.orderDoctorSettlement_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    blackListClick() {
      this.$router.push({
        path: `/orderdoctosettlement/blackList`,
      });
    },
    toOrderDoctor(id) {
      this.$router.push({
        path: `/orderdoctor/detail/${id}`,
      });
    },
    applyForPayment: function () {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      let isApplyPayment = this.selectionList.some((item) =>
        [1, 2, 3, 4].includes(item.isApplyPayment)
      );
      if (isApplyPayment) {
        this.$message.warning("请选择未申请的数据");
        return;
      }
      this.$confirm("确定将选择数据申请打款支付?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return applyForPayment(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 模拟支付
    simulatedPayment() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      let isApplyPayment = this.selectionList.some((item) =>
        [0, 2, 3, 4].includes(item.isApplyPayment)
      );
      if (isApplyPayment) {
        this.$message.warning("请选择已申请待经办的数据");
        return;
      }
      this.$confirm("确定将选择数据模拟支付?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return simulatedPayment(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    applyForVoid: function () {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      let isApplyPayment = this.selectionList.some((item) =>
        [2].includes(item.isApplyPayment)
      );
      if (isApplyPayment) {
        this.$message.warning("请选择未支付的数据");
        return;
      }
      let isApplyPayment2 = this.selectionList.some((item) =>
        [4].includes(item.isApplyPayment)
      );
      if (isApplyPayment2) {
        this.$message.warning("该数据已作废，请勿重复操作");
        return;
      }
      this.$confirm("确定将选择数据作废?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return applyForVoid(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleExport() {
      let downloadUrl = `/api/blade-svc/orderDoctorSettlement/export-orderDoctorSettlement?${
        this.website.tokenHeader
      }=${getToken()}`;
      let values = {};
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `会员订单结算单${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/orderdoctosettlement/detail/${this.form.id}`,
        });
        return;
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      console.log(params);
      if (!this.validatenull(params.paymentTime)) {
        params.startTime = params.paymentTime[0];
        params.endTime = params.paymentTime[1];
        delete params.paymentTime;
      }
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;

      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },

    handleExportFinance() {
      // //时间
      let startTime = "";
      let endTime = "";
      if (!this.validatenull(this.search.paymentTime)) {
        startTime = this.search.paymentTime[0];
        endTime = this.search.paymentTime[1];
      }
      let values = {
        code: this.search.code,
        orderDoctorCode: this.search.orderDoctorCode,
        activityPlanType: this.search.activityPlanType,
        doctorName: this.search.doctorName,
        doctorPhone: this.search.doctorPhone,
        isApplyPayment: this.search.isApplyPayment,
        isTaxDeclaration: this.search.isTaxDeclaration,
        natureOfIncome: this.search.natureOfIncome,
        startTime,
        endTime,
      };
      let downloadUrl = `/api/blade-svc/orderDoctorSettlement/export-orderDoctorSettlement?${
        this.website.tokenHeader
      }=${getToken()}`;
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `会员订单结算单${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
  },
};
</script>

<style></style>
