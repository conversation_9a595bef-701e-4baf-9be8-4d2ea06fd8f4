import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-authenticationDoctor/authenticationDoctor/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-authenticationDoctor/authenticationDoctor/detail",
    method: "get",
    params: {
      id,
    },
  });
};
//客户认证信息详情
export const getCertificationDetail = (id) => {
  return request({
    url: "/api/blade-csc/customerCertificationRecord/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-authenticationDoctor/authenticationDoctor/submit",
    method: "post",
    data: row,
  });
};

// 申请电子签章
export const regEsignSeal = (id) => {
  return request({
    url: "/api/blade-authenticationDoctor/authenticationDoctor/regEsignSeal",
    method: "get",
    params: {
      id,
    },
  });
};

// 开通
export const regEsignAccount = (id) => {
  return request({
    url: "/api/blade-authenticationDoctor/authenticationDoctor/regEsignAccount",
    method: "get",
    params: {
      id,
    },
  });
};

// 启用停用电子签章
export const stopEsignAccount = (id, status) => {
  return request({
    url: "/api/blade-authenticationDoctor/authenticationDoctor/stopEsignAccount",
    method: "get",
    params: {
      id,
      status,
    },
  });
};

// 重新生成会员协议
export const generateProtocol = (doctorId) => {
  return request({
    url: "/api/blade-authenticationDoctor/authenticationDoctor/generateProtocol",
    method: "get",
    params: {
      doctorId,
    },
  });
};

// 已认证数据
export const detailByNameOrPhone = (name, phone) => {
  return request({
    url: "/api/blade-csc/doctorTemp/detailByNameOrPhone",
    method: "get",
    params: {
      name,
      phone,
    },
  });
};

export const downloadIdCardPhotoTemplate = () => {
  return request({
    url: "/api/blade-authenticationDoctor/authenticationDoctor/export-IDPhoto-template",
    method: "get",
    responseType: "blob",
  });
};

export const exportIDPhoto = (formData) => {
  return request({
    url: "/api/blade-authenticationDoctor/authenticationDoctor/export-IDPhoto",
    method: "post",
    data: formData ,
    responseType: "blob",
  });
};
export const refreshFaceVerify = (id) => {
  return request({
    url: "/api/blade-csc/customerCertificationRecord/refreshFaceVerify",
    method: "get",
    params: {
      id,
    },
  });
};


