import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-cmb/transferAccount/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-cmb/transferAccount/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-cmb/transferAccount/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-cmb/transferAccount/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-cmb/transferAccount/submit",
    method: "post",
    data: row,
  });
};
//企银支付业务查询
export const payQuery = (yurRef) => {
  return request({
    url: "/api/blade-cmb/transferAccount/payQuery",
    method: "post",
    params: {
      yurRef,
    },
  });
};
