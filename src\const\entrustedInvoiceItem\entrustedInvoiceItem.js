export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  menu: false,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "企业发票单id",
      prop: "entrustedInvoiceId",
      type: "input",
      // search: true,
      rules: [
        {
          required: true,
          message: "请输入企业发票单id",
          trigger: "blur",
        },
      ],
    },
    {
      label: "开票类型名称",
      prop: "itemName",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入开票类型名称",
          trigger: "blur",
        },
      ],
    },
    {
      label: "不含税金额",
      prop: "amount",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入不含税金额",
          trigger: "blur",
        },
      ],
    },
    {
      label: "税额",
      prop: "taxAmount",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入税额",
          trigger: "blur",
        },
      ],
    },
    {
      label: "价税合计金额",
      prop: "totalTaxAmount",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入价税合计金额",
          trigger: "blur",
        },
      ],
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
