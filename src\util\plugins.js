import Vue from "vue";
// 在vue上挂载一个指量 preventReClick防抖
const preventReClick = Vue.directive("preventReClick", {
  inserted: function(el, binding) {
    console.log("重复点击按钮");
    console.log(el.disabled);
    el.addEventListener("click", () => {
      console.log("点击时间");
      if (!el.disabled) {
        el.disabled = true;
        setTimeout(() => {
          el.disabled = false;
        }, binding.value || 2000);
        //binding.value可以自行设置。如果设置了则跟着设置的时间走
        //例如：v-preventReClick='500'
      }
    });
  },
});
export { preventReClick };
