<template>
  <div>
    <basic-container>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div>
          <el-page-header
            @back="goBack"
            :content="$route.name"
          ></el-page-header>
        </div>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
      ></avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
  </div>
</template>

<script>
// import {
//   getSvcInvoiceById,
//   ,
//   updateOrderStatus,
// } from "@/api/svc/svcinvoice";
import { getDetail } from "@/api/summaryInvoice/summaryInvoice";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      id: "", //数据id
      form: {},
      query: {},
      loading: false,
      //基础信息
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "180",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "详情信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "编码",
                prop: "code",
                type: "input",
                disabled: true,
              },
              {
                label: "名称",
                prop: "name",
                type: "input",
                disabled: true,
              },
              {
                label: "实付总额",
                prop: "invoiceAmount",
                type: "input",
                disabled: true,
              },
              {
                label: "纳税总额",
                prop: "taxAmount",
                type: "input",
                disabled: true,
              },
              {
                label: "代征发票金额",
                prop: "taxInvoiceTotalAmount",
                type: "input",
                disabled: true,
              },
              {
                label: "开票状态",
                prop: "invoiceStatus",
                type: "select",
                disabled: true,
                search: true,
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=summary_invoice_status",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
              },
              {
                label: "发票号码",
                prop: "invoiceCode",
                type: "input",
              },
              {
                label: "开票日期",
                prop: "invoiceDate",
                type: "date",
                format: "yyyy-MM-dd",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "代征人员数量",
                prop: "collectingNum",
                type: "input",
                disabled: true,
              },
              {
                label: "发票附件",
                prop: "invoiceUrl",
                type: "upload",
                listType: "picture-card",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
            ],
          },
        ],
      },
      data: [],
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail(); //获取详情
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    // 获取数据详情
    getDetail() {
      getDetail(this.id).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            this.form = data.data;
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
  },
};
</script>

<style></style>
