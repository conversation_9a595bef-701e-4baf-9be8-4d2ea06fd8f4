export const option1 = {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  addBtn: false,
  delBtn: false,
  dialogClickModal: false,
  emptyBtnIcon: " ",
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    // {
    //   label: "用户id",
    //   prop: "userId",
    //   type: "input",
    //   hide: true,
    // },
    {
      label: "编号",
      prop: "code",
      type: "input",
      width: 120,
    },
    {
      label: "姓名",
      prop: "name",
      type: "input",
      search: true,
    },
    {
      label: "手机号码",
      prop: "phone",
      type: "input",
      search: true,
    },
    {
      label: "微信ID",
      prop: "wechatId",
      type: "input",
      hide: false,
    },
    {
      label: "性别",
      prop: "sex",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=sex",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
    },
    {
      label: "民族",
      prop: "nationality",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=nationality",
      props: {
        label: "dictValue",
        value: "dictKey",
      },

      hide: true,
    },
    {
      label: "籍贯",
      prop: "nativePlace",
      type: "input",
      hide: true,
    },
    {
      label: "省",
      prop: "province",
      type: "select",
    },
    {
      label: "市",
      prop: "city",
      type: "select",
    },
    {
      label: "所属省",
      prop: "provinceCode",
      type: "select",
      props: {
        label: "name",
        value: "code",
      },
      cascader: ["cityCode"],
      dicUrl: "/api/blade-system/region/select",
      span: 6,
      hide: true,
      search: true,
    },
    {
      label: "所属市",
      prop: "cityCode",
      type: "select",
      props: {
        label: "name",
        value: "code",
      },
      cascader: ["districtCode"],
      dicFlag: false,
      dicUrl: "/api/blade-system/region/select?code={{key}}",
      span: 6,
      hide: true,
      search: true,
    },
    {
      label: "所属区",
      prop: "districtCode",
      type: "select",
      props: {
        label: "name",
        value: "code",
      },
      dicFlag: false,
      dicUrl: "/api/blade-system/region/select?code={{key}}",
      span: 6,
      hide: true,
    },
    {
      label: "毕业院校",
      prop: "graduateSchool",
      type: "input",
      hide: true,
    },
    {
      label: "最高学历",
      prop: "education",
      type: "input",
      hide: true,
    },
    {
      label: "医院",
      prop: "hospitalName",
      type: "input",
      search: true,
    },
    {
      label: "科室",
      prop: "departmentName",
      type: "input",
      search: true,
    },
    {
      label: "职称",
      prop: "professional",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=professional",
      props: {
        label: "dictValue",
        value: "dictKey",
      },

      hide: true,
    },
    {
      label: "职务",
      prop: "duty",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=duty",
      props: {
        label: "dictValue",
        value: "dictKey",
      },

      hide: true,
    },
    {
      label: "主治专长",
      prop: "speciality",
      type: "input",
      hide: true,
    },
    {
      label: "证件号码",
      prop: "idCardNumber",
      type: "input",
      search: true,
    },

    {
      label: "电子签章账户",
      prop: "eSignAccount",
      type: "input",
      hide: true,
    },
    {
      label: "电子签章状态",
      prop: "eSignStatus",
      type: "select",
      dicData: [
        {
          label: "未开通",
          value: 1,
        },
        {
          label: "已开通",
          value: 2,
        },
        {
          label: "已过期",
          value: 3,
        },
        {
          label: "已停用",
          value: 3,
        },
      ],
      hide: true,
    },
    {
      label: "电子签字有效期-开始",
      prop: "eSignStartTime",
      type: "input",
      hide: true,
    },
    {
      label: "电子签字有效期-结束",
      prop: "eSignEndTime",
      type: "input",
      hide: true,
    },
    {
      label: "电子签章",
      prop: "eSignUrl",
      type: "upload",
      listType: "picture-img",
      dataType: "string",
      action: "/api/blade-resource/oss/endpoint/put-file",
      propsHttp: {
        res: "data",
        url: "link",
      },
      hide: true,
    },

    {
      label: "认证状态",
      prop: "approvalResult",
      type: "select",
      dicData: [
        {
          label: "未认证",
          value: 0,
        },
        {
          label: "已认证",
          value: 1,
        },
      ],
      search: false,
    },
    {
      label: "结算类型",
      prop: "settleType",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=settle_type",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      align: "center",
      search: true,
    },
    {
      label: "注册时间",
      prop: "createTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
    },
    {
      label: "审核时间",
      prop: "approvalDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
    },
    {
      label: "会员认证核准人员",
      prop: "approvalOfficer",
      type: "input",
      hide: true,
    },
    {
      label: "会员认证核准组织",
      prop: "approvalOrganization",
      type: "input",
      hide: true,
    },
  ],
};

export const option2 = {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  menu: false,
  viewBtn: true,
  selection: true,
  addBtn: false,
  delBtn: false,
  dialogClickModal: false,

  emptyBtnIcon: " ",
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    // {
    //   label: "用户id",
    //   prop: "userId",
    //   type: "input",
    //   hide: true,
    // },
    {
      label: "编号",
      prop: "code",
      type: "input",
    },
    {
      label: "手机号码",
      prop: "phone",
      type: "input",
      search: true,
    },
    {
      label: "微信ID",
      prop: "wechatId",
      type: "input",
    },
    {
      label: "注册时间",
      prop: "createTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm",
      valueFormat: "yyyy-MM-dd HH:mm",
      search: true,
      searchRange: true,
    },

    {
      label: "核准时间",
      prop: "approvalDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm",
      valueFormat: "yyyy-MM-dd HH:mm",
      hide: true,
    },
    {
      label: "会员认证核准人员",
      prop: "approvalOfficer",
      type: "input",
      hide: true,
    },
    {
      label: "会员认证核准组织",
      prop: "approvalOrganization",
      type: "input",
      hide: true,
    },
  ],
};
