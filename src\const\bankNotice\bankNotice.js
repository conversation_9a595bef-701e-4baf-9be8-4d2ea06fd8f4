export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  align: "center",
  addBtn: false,
  editBtn: false,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "账号",
      prop: "accnbr",
      type: "input",
      placeholder: " ",
    },
    {
      label: "户名",
      prop: "accnam",
      type: "input",
      placeholder: " ",
    },
    {
      label: "交易日期",
      prop: "trsdat",
      type: "input",
      placeholder: " ",
    },
    {
      label: "交易时间",
      prop: "trstim",
      type: "input",
      placeholder: " ",
    },
    {
      label: "币种",
      prop: "cCcynbr",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "交易金额",
      prop: "cTrsamt",
      type: "input",
      placeholder: " ",
    },
    {
      label: "收/付方帐号",
      prop: "rpyacc",
      type: "input",
      placeholder: " ",
    },
    {
      label: "收/付方帐户",
      prop: "rpynam",
      type: "input",
      placeholder: " ",
    },
    {
      label: "余额",
      prop: "blvamt",
      type: "input",
      placeholder: " ",
    },
    {
      label: "流水号",
      prop: "refnbr",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "起息日",
      prop: "vltdat",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "交易类型",
      prop: "trscod",
      type: "input",
      placeholder: " ",
    },
    {
      label: "摘要",
      prop: "naryur",
      type: "input",
      placeholder: " ",
    },
    {
      label: "借贷标记",
      prop: "amtcdr",
      placeholder: " ",
      type: "select",
      dicData: [
        {
          label: "贷",
          value: "C",
        },
        {
          label: "借",
          value: "D",
        },
      ],
    },
    {
      label: "流程实例号",
      prop: "reqnbr",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "业务名称",
      prop: "busnam",
      type: "input",
      placeholder: " ",
    },
    {
      label: "用途",
      prop: "nusage",
      type: "input",
      placeholder: " ",
    },
    {
      label: "业务参考号",
      prop: "yurref",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "业务摘要",
      prop: "busnar",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "其它摘要",
      prop: "otrnar",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "收/付方开户地区分行号",
      prop: "rpybbk",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "收/付方开户行行号",
      prop: "rpybbn",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "收/付方开户行名",
      prop: "rpybnk",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "收/付方开户行地址",
      prop: "rpyadr",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "母/子公司所在地区分行",
      prop: "gsbbbk",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "母/子公司帐号",
      prop: "gsbacc",
      type: "input",
      placeholder: " ",
    },
    {
      label: "母/子公司名称",
      prop: "gsbnam",
      type: "input",
      placeholder: " ",
    },
    {
      label:
        "信息标志 为空表示付方帐号和子公司；为“1”表示收方帐号和子公司；为“2”表示收方帐号和母公司；为“3”表示原收方帐号和子公司",
      prop: "infflg",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "有否附件信息标志",
      prop: "athflg",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "票据号",
      prop: "chknbr",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "冲帐标志 *为冲帐，X为补帐",
      prop: "rsvflg",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "扩展摘要",
      prop: "narext",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "交易分析码",
      prop: "trsanl",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "商务支付订单号",
      prop: "refsub",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "企业识别码",
      prop: "frmcod",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "通知类型",
      prop: "msgtyp",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "用户编号",
      prop: "usrnbr",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "通知编号",
      prop: "notnbr",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态（是否关闭）",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
