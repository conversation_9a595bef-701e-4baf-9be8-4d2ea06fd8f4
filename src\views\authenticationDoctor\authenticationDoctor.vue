<template>
  <basic-container>
    <el-tabs v-model="approvalResult" @tab-click="handleClick">
      <el-tab-pane label="认证会员" name="1">
        <avue-crud
          v-if="approvalResult == 1"
          :option="option1"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :permission="permissionList"
          :before-open="beforeOpen"
          v-model="form"
          ref="crud"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoad"
        >
          <template slot="menuLeft">
            <el-button
              type="primary"
              v-if="permission.batchExportPhoto"
              @click="toExportPhoto()"
            >身份证照片批量导出</el-button
            >
          </template>
          <template slot="approvalResult" slot-scope="{ row }">
            <el-tag v-if="row.approvalResult == 0" type="info" size="small"
              >未认证</el-tag
            >
            <el-tag v-if="row.approvalResult == 1" type="success" size="small"
              >已认证</el-tag
            >
          </template>
        </avue-crud>
      </el-tab-pane>
      <el-tab-pane label="待认证会员" name="0"
        ><avue-crud
          v-if="approvalResult == 0"
          :option="option2"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :permission="permissionList"
          :before-open="beforeOpen"
          v-model="form"
          ref="crud"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoad"
        >
          <template slot="approvalResult" slot-scope="{ row }">
            <el-tag v-if="row.approvalResult == 0" type="info" size="small"
              >未认证</el-tag
            >
            <el-tag v-if="row.approvalResult == 1" type="success" size="small"
              >已认证</el-tag
            >
          </template>
        </avue-crud></el-tab-pane
      >
    </el-tabs>
    <el-dialog
      custom-class="upload-dialog"
      title="身份证照片批量导出"
      append-to-body
      :visible.sync="batchExportDialogVisible"
      width="500px"
    >
      <div class="tip">请先下载模板，填写会员信息后上传。</div>
      <el-button class="download-button" size="small" @click="downloadTemplate">下载模板</el-button>
      <el-upload
        drag
        action="#"
        :multiple="false"
        :auto-upload="true"
        :show-file-list="false"
        :http-request="chooseFile"
        class="uploader"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
    </el-dialog>
  </basic-container>
</template>

<script>
import {downloadIdCardPhotoTemplate, exportIDPhoto, getList} from "@/api/authenticationDoctor/authenticationDoctor";
import {
  option1,
  option2,
} from "@/const/authenticationDoctor/authenticationDoctor";
import { mapGetters } from "vuex";
import {downloadXls} from "@/util/util";
import {dateNow} from "@/util/date";

export default {
  data() {
    return {
      batchExportDialogVisible: false,
      approvalResult: "1",
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option1: option1,
      option2: option2,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.authenticationDoctor_add, false),
        viewBtn: this.vaildData(
          this.permission.authenticationDoctor_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.authenticationDoctor_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.authenticationDoctor_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    handleClick() {
      this.query = {};
      this.data = [];
      this.page.currentPage = 1;
      this.$refs.crud.searchReset();
    },

    beforeOpen(done, type) {
      if ("edit" == type) {
        this.toEdit(this.form);
      } else if (type == "view") {
        this.toView(this.form);
      } else {
        done();
      }
    },
    toView(row) {
      this.$router.push({
        path: `/authenticationDoctor/detail/${row.id}/1`,
      });
    },
    toEdit(row) {
      this.$router.push({
        path: `/authenticationDoctor/edit/${row.id}`,
      });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;

      //注册时间
      if (!this.validatenull(params.createTime)) {
        this.query.registerStart = params.createTime[0];
        this.query.registerEnd = params.createTime[1];
        delete this.query.createTime;
      }
      //审核时间
      if (!this.validatenull(params.approvalDate)) {
        this.query.approvalDateStart = params.approvalDate[0];
        this.query.approvalDateEnd = params.approvalDate[1];
        delete this.query.approvalDate;
      }

      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      params.approvalResult = this.approvalResult;
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    toExportPhoto() {
      this.batchExportDialogVisible = true;
    },
    downloadTemplate() {
      downloadIdCardPhotoTemplate().then(res => {
        downloadXls(res.data, `身份证照片批量导出模板${dateNow()}.xlsx`);
      })
    },
    chooseFile(param) {
      const formData = new FormData()
      formData.append('file', param.file)
      exportIDPhoto(formData).then((response) => {

        if (response.data.type === 'application/json') {
          const fileReader = new FileReader()
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result)
            // 后台信息
            this.$message({
              message: jsonData.msg,
              dangerouslyUseHTMLString: true,
              type: 'error',
              duration: 5000
            })
          }
          fileReader.readAsText(response.data)
          return
        }

        this.$message.success("操作成功");
        console.log(response)
        const content = response.data
        const blob = new Blob([content]) // 构造一个blob对象来处理数据

        const fileName = this.getFilenameFromHeaders(response.headers['content-disposition']) || `${Date.now()}.zip`
        if (window.navigator.msSaveOrOpenBlob) {
          // 其他浏览器
          navigator.msSaveBlob(blob, fileName)
        } else {
          // 支持a标签download的浏览器
          const link = document.createElement('a') // 创建a标签
          link.download = fileName // a标签添加属性
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click() // 执行下载
          URL.revokeObjectURL(link.href) // 释放url
          document.body.removeChild(link) // 释放标签
        }
      })
    },
    getFilenameFromHeaders(disposition) {
      if (!disposition) { return null  };

      // 尝试匹配文件名
      const filenameRegex = /filename=([^;]*)/;
      const matches = filenameRegex.exec(disposition);
      if (matches && matches[1]) {
        // 去掉可能存在的引号
        return  decodeURIComponent(matches[1]);
      }
      return null;
    }
  },
};
</script>

<style lang="scss" scoped >
.tip {
  padding: 8px 16px;
  background-color: #ecf8ff;
  border-radius: 4px;
  border-left: 5px solid #50bfff;
  margin: 0 0 10px 0;
}
:deep(.upload-dialog .el-dialog__body){
  padding-top: 0;
}
.download-button {
  margin-bottom: 10px;
}
</style>
