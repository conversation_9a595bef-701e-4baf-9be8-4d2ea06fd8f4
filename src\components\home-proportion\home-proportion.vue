<template>
  <div class="trend">
    <div class="subtext">单位:元</div>
    <div ref="chart" class="trend-canvas"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import "echarts/lib/chart/line";
import "echarts/lib/component/tooltip";
import "echarts/lib/component/legend";
import { getiServiceItems } from "@/api/home/<USER>";
export default {
  name: "homeProportion",
  data: () => {
    return {
      chartData: [],
    };
  },
  mounted() {
    this.getiServiceItems();
  },
  methods: {
    getiServiceItems() {
      getiServiceItems().then((res) => {
        res.data.data.enterpriseOrdersStatisticalList; //企业订单额
        res.data.data.map((data) => {
          this.chartData.push({
            name: data.serviceItemsName,
            value: data.serviceItemsProportion,
            limit: data.serviceItemsAmount,
          });
        });
        this.renderChart();
      });
    },
    renderChart() {
      const chartDom = this.$refs.chart;
      const myChart = echarts.init(chartDom);
      const option = {
        title: {
          text: "服务项目占比统计",
          top: 18,
          left: 22,
          textStyle: {
            color: "#000000",
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "item",
          formatter: (params) => {
            return (
              params.name +
              "<br/>" +
              "交易额" +
              " : " +
              params.data.limit +
              "元" +
              "<br/>"
            );
          },
          textStyle: {
            align: "left", // 设置文字居右显示
          },
        },
        legend: {
          orient: "vertical",
          left: "70%",
          y: "center",
          align: "left",
          data: this.chartData.map((data) => data.name),
          formatter: (name) => {
            var value = 0;
            var limit;
            for (var i = 0, l = this.chartData.length; i < l; i++) {
              if (this.chartData[i].name == name) {
                value += this.chartData[i].value;
                limit = this.chartData[i].limit;
              }
            }
            var arr = [
              "{a|" +
                name +
                " :}{b|" +
                +value +
                "%}\n{c|交易额:" +
                limit +
                "元}",
            ];
            return arr.join("\n");
          },

          textStyle: {
            rich: {
              a: {
                fontSize: 12,
                align: "left",
                color: "#333333",
                padding: [0, 0, 0, 5],
              },
              b: {
                fontSize: 12,
                align: "left",
                color: "#333333",
                padding: [0, 0, 0, 0],
              },
              c: {
                fontSize: 12,
                align: "left",
                color: "#333333",
                padding: [5, 0, 0, 5],
              },
            },
          },
        },

        series: {
          color: [
            "#4218EB",
            "#39EE15",
            "#F4DB1D",
            "#1D90A2",
            "#FFA91C",
            "#E86262",
          ],
          type: "pie",
          radius: ["0%", "65%"],
          center: ["30%", "50%"],
          clockwise: true,
          avoidLabelOverlap: true,
          hoverOffset: 15,
          hoverAnimation: false,
          data: this.chartData,
        },
      };

      myChart.setOption(option);
    },
  },

  beforeUnmount() {},
};
</script>

<style scoped lang="scss">
.trend {
  width: 100%;
  height: 401px;
  background: #ffffff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.subtext {
  position: absolute;
  top: 22px;
  right: 36px;
  z-index: 99;
  font-size: 12px;
  font-weight: 400;
  color: #000000;
  line-height: 17px;
}
.trend-canvas {
  width: 100%;
  height: 100%;
}
.trend-title {
  width: 102px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  line-height: 22px;
  margin: 18px 0 20px 22px;
}
.item-title {
  margin-top: 15px;
  font-size: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
}
</style>
