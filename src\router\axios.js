/**
 * 全站http配置
 *
 * axios参数说明
 * isSerialize是否开启form表单提交
 * isToken是否需要token
 */
import { Loading } from "element-ui";
import axios from "axios";
import store from "@/store/";
import router from "@/router/router";
import { serialize, isEquivalent } from "@/util/util";
import { getToken } from "@/util/auth";
import { Message } from "element-ui";
import { isURL } from "@/util/validate";
import website from "@/config/website";
import { Base64 } from "js-base64";
import { baseUrl } from "@/config/env";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import crypto from "@/util/crypto";

//遮罩
var loading;
var loadingUrl;
//默认超时时间
axios.defaults.timeout = 150000;
//返回其他状态码
axios.defaults.validateStatus = function (status) {
  return status >= 200 && status <= 500;
};
//跨域请求，允许保存cookie
axios.defaults.withCredentials = true;
// NProgress 配置
NProgress.configure({
  showSpinner: false,
});
//http request拦截
axios.interceptors.request.use(
  (config) => {
    // 去除搜索条件中的空格
    let listUrl = [
      //平台会员管理
      "/api/blade-csc/entrustedCompany/list",
      "/api/blade-authenticationDoctor/authenticationDoctor/list",
      "/api/blade-csc/customerCertificationRecord/list",
      //服务关系管理
      "/api/blade-csc/entrustedEmployee/list",
      "/api/blade-csc/doctorEmployeeRel/list",
      //服务活动管理
      "/api/blade-act/caseCollection/list",
      "/api/blade-act/medicationFeedback/list",
      "/api/blade-act/clinicalResearch/list",
      "/api/blade-act/meeting/list",
      "/api/blade-act/lecture/list",
      "/api/blade-act/meeting/page",
      "/api/blade-act/lecture/page",
      "/api/blade-act/lectureReport/list",
      "/api/blade-act/caseCollectionReport/list",
      "/api/blade-act/medicationFeedbackReport/list",
      "/api/blade-act/clinicalResearchReport/list",
      "/api/blade-act/professionalReview/list",
      // 服务订单管理
      "/api/blade-svc/orderDoctor/list",
      "/api/blade-svc/orderEntrusted/list",
      "/api/blade-svc/resultOrder/list",
      "/api/blade-svc/resultOrderDetail/list",
      // 服务结算管理
      "/api/blade-svc/settlementOrder/list",
      "/api/blade-settlementCycle/settlementCycle/list",
      "/api/blade-svc/settlementOrderDoctor/list",
      "/api/blade-svc/orderDoctorSettlement/list",
      "/api/blade-svc/summaryInvoice/list",
      "/api/blade-svc/summaryTaxCert/list",
      "/api/blade-svc/entrustedInvoice/list",
      // 资金支付管理
      "/api/blade-svc/servicePaymentOrder/list",
      "/api/blade-svc/orderDoctorPayment/page",
      "/api/blade-pay/doctorPaymentDetails/list",
      "/api/blade-svc/entrustedTaxPaymentOrder/list",
      "/api/blade-svc/platServiceFee/list",
      "/api/blade-handleApply/handleApply/list",
      // 服务项目配置
      "/api/blade-sys/baseProduct/list",
      "/api/blade-act/activityServiceProject/list",
      "/api/blade-act/caseCollectionTemplate/list",
      "/api/blade-act/caseCollectionTemplateAttr/list",
      "/api/blade-act/clinicalResearchTemplate/list",
      "/api/blade-act/clinicalResearchTemplateAttr/list",
      "/api/blade-clinicalResearchProject/clinicalResearchProject/list",
      // 税务溯源查询
      "/api/blade-authenticationDoctor/authenticationDoctor/list",
      "/api/blade-csc/entrustedCompany/list",
      "/api/blade-svc/orderDoctor/pageTraceabilityOrder",
      "/api/blade-svc/summaryTaxReturnsDetail/list",
    ];
    if (listUrl.includes(config.url.split("?")[0])) {
      if (JSON.stringify(config.params)) {
        let data = JSON.parse(JSON.stringify(config.params));
        for (let key in data) {
          // 执行循环体代码
          if (typeof data[key] === "string") {
            data[key] = data[key].trim();
          }
        }
        config.params = data;
      }
      if (JSON.stringify(config.data)) {
        let data = JSON.parse(JSON.stringify(config.data));
        for (let key in data) {
          // 执行循环体代码
          if (typeof data[key] === "string") {
            data[key] = data[key].trim();
          }
        }
        config.data = data;
      }
    }
    let data = config.params
      ? JSON.parse(JSON.stringify(config.params))
      : config.data
      ? JSON.parse(JSON.stringify(config.data))
      : null;
    let axiosUrl = window.localStorage.getItem("axiosUrl");
    let axiosData = JSON.parse(window.localStorage.getItem("axiosData"));
    let whitelistUrl = [
      "/api/blade-system/dict-biz/dictionary",
      "/api/blade-resource/oss/endpoint/put-file",
      "/api/blade-resource/oss/endpoint/put-file-attach",
      "/api/blade-system/region/select",
      "/api/blade-develop/model/select",
      "/api/blade-system/dict/dictionary",
    ];
    if (
      !whitelistUrl.includes(config.url.split("?")[0]) &&
      axiosUrl == config.url &&
      isEquivalent(axiosData, data)
    ) {
      Message({
        message: "请求中有相同请求,请勿重复点击!!!",
        type: "warning",
      });
      return Promise.reject("重复点击");
    }
    //开启 progress bar
    NProgress.start();
    //地址为已经配置状态则不添加前缀
    if (!isURL(config.url) && !config.url.startsWith(baseUrl)) {
      config.url = baseUrl + config.url;
    }
    //headers判断是否需要
    const authorization = config.authorization === false;
    if (!authorization) {
      config.headers["Authorization"] = `Basic ${Base64.encode(
        `${website.clientId}:${website.clientSecret}`
      )}`;
    }
    if (config.url == "/api/openapi/auth/webToken") {
      config.headers["Authorization"] = `Basic c2FiZXI6c2FiZXJfc2VjcmV0`;
    }
    //headers判断请求是否携带token
    const meta = config.meta || {};
    const isToken = meta.isToken === false;
    //headers传递token是否加密
    const cryptoToken = config.cryptoToken === true;
    let tokenSfe = localStorage.getItem("saber-access-token");
    const token = getToken() ? getToken() : tokenSfe;
    if (token && !isToken) {
      config.headers[website.tokenHeader] = cryptoToken
        ? "crypto " + crypto.encrypt(token)
        : "bearer " + token;
    }
    //headers中配置text请求
    if (config.text === true) {
      config.headers["Content-Type"] = "text/plain";
    }
    //headers中配置serialize为true开启序列化
    if (config.method === "post" && meta.isSerialize === true) {
      config.data = serialize(config.data);
    }
    // '/api/blade-svc/entrustedInvoice/invoiceCheck'
    if (config.url == "/api/blade-svc/entrustedInvoice/invoiceCheck") {
      loadingUrl = config.url;
      loading = Loading.service({
        lock: true,
        text: "正在查验发票请等待结果",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
    }
    window.localStorage.setItem("axiosUrl", config.url);
    window.localStorage.setItem("axiosData", JSON.stringify(data));
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
//http response 拦截
axios.interceptors.response.use(
  (res) => {
    window.localStorage.removeItem("axiosUrl");
    window.localStorage.removeItem("axiosData");
    if (res.config.url == "/api/blade-svc/entrustedInvoice/invoiceCheck") {
      loading.close();
    } else if (res.config.url == "/api/blade-svc/resultOrder/audit") {
      loading.close();
    }
    //关闭 progress bar
    NProgress.done();
    //获取状态码
    const status = res.data.code || res.status;
    const statusWhiteList = website.statusWhiteList || [];
    const message = res.data.msg || res.data.error_description || "未知错误";
    //如果在白名单里则自行catch逻辑处理
    if (statusWhiteList.includes(status)) return Promise.reject(res);
    //如果是401则跳转到登录页面
    if (status === 401)
      store.dispatch("FedLogOut").then(() => router.push({ path: "/login" }));
    // 如果请求为非200否者默认统一处理
    if (status !== 200) {
      Message({
        message: message,
        type: "error",
      });
      return Promise.reject(new Error(message));
    }
    return res;
  },
  (error) => {
    window.localStorage.removeItem("axiosUrl");
    window.localStorage.removeItem("axiosData");
    if (loadingUrl == "/api/blade-svc/entrustedInvoice/invoiceCheck") {
      loading.close();
      Message({
        message: "查验发票超时请稍后重试",
        type: "error",
      });
    } else if (loadingUrl == "/api/blade-svc/resultOrder/audit") {
      loading.close();
      Message({
        message: "审核超时请稍后重试",
        type: "error",
      });
    }
    NProgress.done();
    return Promise.reject(new Error(error));
  }
);

export default axios;
