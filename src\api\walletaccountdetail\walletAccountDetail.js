import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-pay/doctorPaymentDetails/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-pay/doctorPaymentDetails/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-pay/doctorPaymentDetails/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-pay/doctorPaymentDetails/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-pay/doctorPaymentDetails/submit",
    method: "post",
    data: row,
  });
};

export const getDoctorList = (doctorName) => {
  return request({
    url: "/api/blade-authenticationDoctor/authenticationDoctor/getDoctorList",
    method: "get",
    params: {
      doctorName,
    },
  });
};
