body {
    display: block;
    margin: 0px;
}

.meeting-page {
    min-height: 100vh !important;
    background: url("../img/background.png");
    background-repeat: no-repeat !important;
    background-size: cover !important;
    position: relative;
}

.toast_toastWrap {
    transition-duration: 1s;
    display: none;
    background: #444;
    border-radius: 4px;
    position: absolute;
    top: 50%;
    left: calc(50% - 100px);
    z-index: 99;
    width: 180px;
    height: 40px;
    font-size: 12px;
    color: #fff;
    line-height: 40px;
    text-align: center;
}


.meeting-page::before {
    content: "";
    display: table;
}

.meeting {
    display: block;
    margin: 0 auto;
    background: #f6f6f6;
    width: 375px;
    min-height: 645px;
    max-height: 724px;
    margin: auto;
    border-radius: 8px;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
}

@media (max-width: 435px) {
    .meeting-page {
        background: #f6f6f6;
    }

    .meeting {
        width: 100vw;
        height: 100vh;
        margin: 0;
    }
}


.meeting::before {
    content: "";
    display: table;
}

.meeting-border {
    width: 100%;
    max-width: 91.5%;
    height: 280px;
    margin: 40px auto;
    background: #ffffff;
    background-image: url("../img/meeting.png");
    background-size: contain;
    background-repeat: no-repeat;
    border-radius: 8px;
    box-shadow: 1px 2px 10px #ccc;

}

.meeting-border::before {
    content: "";
    display: table;
}

.meeting-content {
    width: 100%;
    margin-top: 100px;

}

.meeting-title {
    width: 90%;
    margin: 0 auto;
    height: 28px;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    text-align: center;
    font-style: normal;
    margin-bottom: 30px;
}

.meeting-date {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.meeting-date-left {
    position: absolute;
    left: 10px;
    top: 3px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.meeting-date-left-time {
    font-size: 28px;
    color: #000;
    line-height: 28px;
    display: block;
}

.meeting-date-left-date {
    display: inline-block;
    font-size: 12px;
    color: #000;
    line-height: 12px;
    margin-top: 8px;
}

.line {
    background: #d3d6db;
    border-radius: 0;
    width: 24px;
    height: 1px;
    margin-bottom: 0;
}

.meeting-date {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.meeting-date-top {
    border-radius: 2px;
    height: 18px;
    padding: 2px;
    font-size: 10px;
    color: #4e5461;
    text-align: center;
    line-height: 15px;
    margin-top: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.meeting-date-buttom {
    height: 12px;
    font-size: 12px;
    color: #7b818f;
    line-height: 12px;
    display: flex;
    justify-content: center;
}

.meeting-date-right {
    right: 10px;
    position: absolute;
    top: 3px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.meeting-date-right-time {
    font-size: 28px;
    color: #000;
    line-height: 28px;
    display: block;
}

.meeting-date-right-date {
    display: inline-block;
    font-size: 12px;
    color: #000;
    line-height: 12px;
    margin-top: 8px;
}

/* 状态提示 */
.wrap__ptVFv {
    display: none;
    text-align: center;
    margin-top: 100px;
    margin-bottom: 60px;
}

.wrap__ptVFv-img {
    width: 50.4px;
    height: 78.05px;
    background-image: url("../img/meeting-icon.png");
    background-size: cover;
    margin: 0 auto;
}

.wrap__ptVFv-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.wrap__ptVFv-container-line {
    display: inline-block;
    background: #d3d6db;
    border-radius: 0;
    width: 32px;
    height: 1px;
    margin-top: 20px;
}

.wrap__ptVFv-container-text {
    font-size: 18px;
    color: #fa4e32;
    letter-spacing: 0;
    text-align: center;
    line-height: 20px;
    margin: 0 20px;
    margin-top: 20px;
}

/* 按钮 */
.join-button_btn-wrap__pjyNt {
    width: 91.5%;
    margin: 0 auto;

}

.join-button_common-met-button__Yu8ye {
    border-radius: 8px;
    margin-top: 12px;
    width: 100%;
}

/* 文字 */
.footer_foot-txt__A8jHe {
    margin-top: 15px;
    font-size: 12px;
    color: #7b818f;
    letter-spacing: 0;
    line-height: 20px;
    text-align: center;
    box-sizing: border-box;
    background: transparent;
}

.footer_foot-txt__A8jHe .footer_copy-meeting-invitation-text__dQI1O {
    color: #06f;
    cursor: pointer;
    user-select: none;
}

/* 其他 */

.met-btn--weak {
    background-color: #fff;
    border-color: #d3d6db;
    color: #000;
}

.met-btn {
    height: 42px;
    min-width: 24px;
    padding: 4px 8px;
    background-color: #06f;
    color: #fff;
    border: 1px solid #06f;
    line-height: 22px;
    text-align: center;
    display: inline-block;
    cursor: pointer;
    outline: 0 none;
    box-sizing: border-box;
    text-decoration: none;
    font-size: 14px;
    vertical-align: middle;
    white-space: nowrap;
    border-radius: 6px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}