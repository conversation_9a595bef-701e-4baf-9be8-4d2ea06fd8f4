<template>
  <div>
    <basic-container
      ><el-page-header @back="goBack" :content="$route.name"></el-page-header
    ></basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form1)"
        ref="form1"
        :option="option1"
        v-model="form1"
        @submit="submit"
      >
        <template slot-scope="{}" slot="enterpriseName">
          <div class="to-view" @click="toView()">
            <a readonly>
              {{ form1.enterpriseName }}
            </a>
          </div>
        </template>
        <template slot-scope="{}" slot="settlementCode">
          <div class="to-view" @click="toViewSettlement()">
            <a readonly>
              {{ form1.settlementCode }}
            </a>
          </div>
        </template>
        <template slot-scope="{}" slot="invoiceCode">
          <div class="to-view" @click="toViewInvoice()">
            <a readonly>
              {{ form1.invoiceCode }}
            </a>
          </div>
        </template>
        <template slot-scope="{ disabled, size }" slot="taskOrderFile">
          <div style="cursor: pointer">
            <el-tag @click="preview(form1.taskOrderFile)">预览任务单</el-tag>
          </div>
        </template>
        <template slot-scope="{ disabled, size }" slot="resultOrderFile">
          <div style="cursor: pointer">
            <el-tag @click="preview(form1.resultOrderFile)">预览成果单</el-tag>
          </div>
        </template>
        <template slot-scope="{ disabled, size }" slot="settlementOrderFile">
          <div style="cursor: pointer">
            <el-tag @click="preview(form1.settlementOrderFile)"
              >预览结算单</el-tag
            >
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
      <!-- 务订单支付流水隐藏 -->
      <!--<avue-crud-->
        <!--:option="option2"-->
        <!--:table-loading="loading"-->
        <!--:data="data"-->
        <!--:page.sync="page"-->
        <!--:permission="permissionList"-->
        <!--:before-open="beforeOpen"-->
        <!--v-model="form"-->
        <!--ref="crud"-->
        <!--@refresh-change="refreshChange"-->
        <!--@on-load="onLoad"-->
      <!--&gt;-->
        <!--<template slot-scope="{ type, size, row, index }" slot="menu">-->
          <!--<el-button-->
            <!--v-if="-->
              <!--permission.servicePaymentOrder_confimPay &&-->
              <!--row.payAccountStatus == 0 &&-->
              <!--row.valid == 1-->
            <!--"-->
            <!--type="text"-->
            <!--@click="openPayAffirm(row)"-->
            <!--&gt;到账确认</el-button-->
          <!--&gt;-->
          <!--<el-button type="text" @click="openVoucherUrl(row.voucherUrl)"-->
            <!--&gt;查看</el-button-->
          <!--&gt;-->
          <!--<el-button-->
            <!--v-if="permission.servicePaymentOrder_uploadVoucherUrl"-->
            <!--type="text"-->
            <!--@click="open(row)"-->
            <!--&gt;上传凭证</el-button-->
          <!--&gt;-->
        <!--</template>-->
        <!--<template slot="menuLeft">-->
          <!--<el-button-->
            <!--type="primary"-->
            <!--v-if="permission.servicePaymentOrder_import"-->
            <!--@click="importShow = true"-->
            <!--&gt;导 入-->
          <!--</el-button>-->
        <!--</template>-->
      <!--</avue-crud>-->
    </basic-container>

    <el-dialog
      title="查看"
      :visible.sync="dialogVisible"
      append-to-body
      width="30%"
      :before-close="handleClose"
    >
      <el-image
        v-for="(item, index) in srcList"
        :key="index"
        :infinite="false"
        style="width: 200px; margin: 15px"
        :src="item"
        :zoom-rate="1.2"
        :preview-src-list="srcList"
      />
    </el-dialog>
    <el-dialog
      title="上传凭证"
      :visible.sync="uploadDialogVisible"
      append-to-body
      width="30%"
    >
      <avue-form
        :option="uploadOption"
        v-model="uploadForm"
        :upload-exceed="uploadExceed"
      >
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="uploadVoucherUrl()">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="导入服务订单支付流水"
      append-to-body
      :visible.sync="importShow"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDetail,
  getOrderList,
  confimPay,
  uploadVoucherUrl,
} from "@/api/servicepaymentorder/servicePaymentOrder";
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
export default {
  data() {
    return {
      runningWaterId: "",
      uploadDialogVisible: false,
      uploadForm: { voucherUrl: "" },
      uploadOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "凭证",
            prop: "voucherUrl",
            type: "upload",
            drag: true,
            loadText: "凭证上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
              url: "link",
            },
            limit: 5,
            listType: "picture-card",
            accept: "image/png, image/jpeg",
            tip: "请上传jpg/png格式文件",
            action: "/api/blade-resource/oss/endpoint/put-file",
          },
        ],
      },

      importShow: false,
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "支付明细表",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "数据上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
            },
            data: {
              isCovered: 1,
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: `${this.id}`,
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      dialogVisible: false,
      voucherUrl: "",
      srcList: [],
      id: "",
      form1: {},
      option1: {
        disabled: true,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: "170",
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",
            column: [
              {
                label: "支付单编码",
                prop: "code",
                type: "input",
              },
              {
                label: "客户名称",
                prop: "enterpriseName",
                type: "input",
              },

              {
                label: "企业结算单编号",
                prop: "settlementCode",
                type: "input",
              },
              {
                label: "企业结算单名称",
                prop: "settlementName",
                type: "input",
              },
              {
                label: "企业发票单编号",
                prop: "invoiceCode",
                type: "input",
              },
              {
                label: "企业发票单名称",
                prop: "invoiceName",
                type: "input",
              },
              // {
              //   label: "支付到账金额",
              //   prop: "settlementPaidAmount",
              //   type: "input",
              // },
              {
                label: "支付金额",
                prop: "settlementUnpaidAmount",
                type: "input",
              },
            ],
          },
        ],
      },
      option2: {
        title: "服务订单支付流水",
        maxHeight: "800",
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        addBtn: false,
        selection: true,
        editBtn: false,
        delBtn: false,
        menu: true,
        dialogClickModal: false,
        column: [
          {
            label: "服务订单支付单ID",
            prop: "paymentOrderId",
            type: "input",
            hide: true,
          },

          {
            label: "支付流水编号",
            prop: "payCode",
            type: "input",
          },
          {
            label: "支付日期",
            prop: "payDate",
            type: "input",
          },
          {
            label: "支付方式",
            prop: "payWay",
            type: "select",
            dicData: [
              {
                label: "线上公对公",
                value: 1,
              },
              {
                label: "线下公对公",
                value: 2,
              },
            ],
          },
          {
            label: "支付金额",
            prop: "payAmount",
            type: "input",
          },
          // {
          //   label: "支付凭证",
          //   prop: "voucherUrl",
          //   type: "input",
          // },
          {
            label: "支付到账状态",
            prop: "payAccountStatus",
            type: "select",
            dicData: [
              {
                label: "待确认",
                value: 0,
              },
              {
                label: "已到账",
                value: 1,
              },
            ],
          },
          {
            label: "到账确认日期",
            prop: "confirmDate",
            type: "input",
          },
          {
            label: "到账确认人员",
            prop: "confirmer",
            type: "input",
          },
          {
            label: "有效状态",
            prop: "valid",
            type: "select",
            dicData: [
              {
                label: "作废",
                value: 0,
              },
              {
                label: "有效",
                value: 1,
              },
            ],
          },
        ],
      },
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      data: [],
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
    this.uploadBefore();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    open(row) {
      this.runningWaterId = row.id;
      this.uploadForm.voucherUrl = row.voucherUrl;
      this.uploadDialogVisible = true;
    },
    uploadExceed() {
      this.$message.error("超出上传限制凭证数量");
    },
    uploadVoucherUrl() {
      if (this.uploadForm.voucherUrl instanceof Array) {
        this.uploadForm.voucherUrl = this.uploadForm.voucherUrl.join(",");
      }
      this.uploadForm.id = this.runningWaterId;
      uploadVoucherUrl(this.uploadForm).then(
        () => {
          this.onLoad(this.page);
          this.uploadDialogVisible = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
    uploadBefore() {
      this.excelOption.column[0].action = `/api/blade-svc/servicePaymentOrderDetail/import-paymentOrderDetail?isCovered=1&paymentOrderId=${this.id}`;
    },
    uploadAfter(res, done, loading, column) {
      window.console.log(column);
      this.importShow = false;
      this.onLoad(this.page);
      done();
    },
    handleTemplate() {
      exportBlob(
        `/api/blade-svc/servicePaymentOrderDetail/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, `服务订单支付流水模板.xlsx`);
      });
    },
    //去企业详情
    toView() {
      this.$router.push({
        path: `/entrustedcompany/detail/${this.form1.entrustedCompanyId}/1`,
      });
    },
    //去结算单详情
    toViewSettlement() {
      this.$router.push({
        path: `/settlementOrderDoctor/detail/${this.form1.settlementOrderId}`,
      });
    },
    //去发票单详情
    toViewInvoice() {
      this.$router.push({
        path: `/entrustedInvoice/detail/${this.form1.entrustedInvoiceId}`,
      });
    },
    //查看凭证
    openVoucherUrl(url) {
      this.dialogVisible = true;
      this.srcList = url ? url.split(",") : "";
    },
    // 到账确认
    openPayAffirm(row) {
      this.$confirm("是否确认到账?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return confimPay(row.id);
        })
        .then(() => {
          this.getDetail();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },

    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form1 = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    refreshChange() {
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.paymentOrderId = this.id;
      getOrderList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        // this.selectionClear();
      });
    },
  },
};
</script>

<style lang="scss">
.avue-crud > h2 {
  font-size: 16px !important;
  font-weight: 500 !important;
}
.my-button {
  position: relative;
  left: 89%;
  top: 30px;
  z-index: 999;
}
</style>
