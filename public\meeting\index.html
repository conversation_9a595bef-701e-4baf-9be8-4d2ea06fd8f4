<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腾讯会议</title>
    <link rel="stylesheet" type="text/css" href="./css/index.css?v=" +Math.random() />
</head>

<body>
    <div class="meeting-page">
        <span class="toast_toastWrap" id="toast">
            会议不存在
        </span>
        <div class="meeting">
            <div class="meeting-border">
                <div class="meeting-content">
                    <div class="meeting-title" id="meeting-title">会议</div>
                    <div class="meeting-date">
                        <div class="meeting-date-left">
                            <span class="meeting-date-left-time" id="meeting-date-left-time">00:00</span>
                            <span class="meeting-date-left-date" id="meeting-date-left-date"></span>
                        </div>
                        <span class="line"></span>
                        <div class="meeting-date-middle">
                            <div class="meeting-date-top" id="meeting-date-time">1小时</div>
                            <span class="meeting-date-buttom">（GMT+08:00）</span>
                        </div>
                        <span class="line"></span>
                        <div class="meeting-date-right">
                            <span class="meeting-date-right-time" id="meeting-date-right-time">00:00</span>
                            <span class="meeting-date-right-date" id="meeting-date-right-date"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="wrap__ptVFv" id="wrap__ptVFv">
                <div class="wrap__ptVFv-img"></div>
                <div class="wrap__ptVFv-container">
                    <span class="wrap__ptVFv-container-line"></span>
                    <div class="wrap__ptVFv-container-text" id="wrap__ptVFv-container-text">会议已取消</div>
                    <span class="wrap__ptVFv-container-line"></span>
                </div>
            </div>
            <div class="join-button_btn-wrap__pjyNt" id="join-button">
                <button class="met-btn met-btn--block join-button_common-met-button__Yu8ye" id="mpJoinBtnCtrl"
                    onclick="joinMeeting()">加入会议</button>
            </div>
            <!-- <div class="footer_foot-txt__A8jHe" id="copy-meeting-invitation-text">您可以 <span
                class="footer_copy-meeting-invitation-text__dQI1O">
                复制会议邀请</span>，发送至会议成员
        </div> -->
        </div>
    </div>
</body>
<script src="./js/index.js?v=" +Math.random()>
</script>
<style>

</style>

</html>