<template>
  <div>
    <basic-container
      ><el-page-header @back="goBack" :content="$route.name"></el-page-header
    ></basic-container>
    <basic-container>
      <!--临床项目合作-->
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
      >
        <!-- 疾病领域 -->
        <template slot-scope="{ disabled, size }" slot="diseaseDomain"
          ><el-tag
            :key="tag"
            v-for="tag in diseaseDomain"
            :disable-transitions="false"
          >
            {{ tag }}
          </el-tag>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
  </div>
</template>

<script>
import {
  getDetail,
  getClinicalCooperation,
} from "@/api/activityserviceproject/activityServiceProject";
export default {
  data() {
    return {
      id: "",
      //疾病领域
      inputVisible: false,
      inputValue: "",
      diseaseDomain: [],
      //end
      //参与目标科室
      inputVisible2: false,
      inputValue2: "",
      //end
      //项目收集方式
      inputVisible5: false,
      inputValue5: "",

      //end
      query: {},
      loading: false,
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      form: {
        chargingCost: "",
        diseaseDomain: "疾病领域",
      },
      clinicalCooperationList: [],
      option: {
        disabled: true,
        labelWidth: "170",
        submitBtn: false,
        emptyBtn: false,
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",
            column: [
              {
                label: "企业名称",
                prop: "entrustedCompanyName",
              },

              {
                label: "服务项目类型",
                prop: "projectType",
                type: "select",
                dicData: [],
                props: {
                  label: "projectTypeName",
                  value: "id",
                },

                rules: [
                  {
                    required: true,
                    message: "请选择服务项目类型",
                    trigger: ["blur", "change"],
                  },
                ],
                change: ({ item }) => {
                  if (item) {
                    this.projectTypeChange(item.actType);
                  }
                },
              },
              {
                label: "对应产品名称",
                prop: "productName",
                type: "input",
                display: false,
                rules: [
                  {
                    required: true,
                    message: "请输入涉及产品名称",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "项目方案名称",
                prop: "projectName",
                type: "input",
                maxlength: 50,
                showWordLimit: true,
                rules: [
                  {
                    required: true,
                    message: "请输入项目方案名称",
                    trigger: ["blur", "change"],
                  },
                ],
                span: 24,
              },
              {
                label: "项目开始日期",
                prop: "startDate",
                type: "date",
                format: "yyyy年MM月dd日",
                valueFormat: "yyyy-MM-dd",
                rules: [
                  {
                    required: true,
                    message: "请选择项目开始日期",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "项目截止日期",
                prop: "endDate",
                type: "date",
                format: "yyyy年MM月dd日",
                valueFormat: "yyyy-MM-dd",
                rules: [
                  {
                    required: true,
                    message: "请选择项目截止日期",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "涉及疾病领域",
                prop: "diseaseDomain",
                type: "input",
                rules: [
                  {
                    required: true,
                    message: "请输入涉及疾病领域",
                    trigger: ["blur", "change"],
                  },
                ],
                row: true,
                span: 24,
              },
              {
                label: "参与会员要求",
                prop: "doctorClaim",
                type: "input",
                maxlength: 100,
                showWordLimit: true,
                span: 24,
              },
              {
                label: "项目发起部门",
                prop: "orgDepartment",
                type: "input",
                maxlength: 20,
                showWordLimit: true,
                rules: [
                  {
                    required: true,
                    message: "请输入项目发起部门",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "项目负责人员",
                prop: "orgPersonnel",
                type: "input",
                maxlength: 20,
                showWordLimit: true,
                rules: [
                  {
                    required: true,
                    message: "请输入项目负责人员",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "项目验收部门",
                prop: "checkDepartment",
                type: "input",
                maxlength: 20,
                showWordLimit: true,
                rules: [
                  {
                    required: true,
                    message: "请输入项目验收部门",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "项目验收人员",
                prop: "checkPersonnel",
                type: "input",
                maxlength: 20,
                showWordLimit: true,
                rules: [
                  {
                    required: true,
                    message: "请输入项目验收人员",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "项目开展目的",
                prop: "projectPurpose",
                type: "textarea",
                minRows: 3,
                maxRows: 5,
                rules: [
                  {
                    required: true,
                    message: "请输入项目开展目的",
                    trigger: ["blur", "change"],
                  },
                ],
                span: 24,
                maxlength: 500,
                showWordLimit: true,
              },
              {
                label: "项目开展内容",
                prop: "projectContent",
                type: "textarea",
                minRows: 3,
                maxRows: 5,
                rules: [
                  {
                    required: true,
                    message: "请输入临床项目内容",
                    trigger: ["blur", "change"],
                  },
                ],
                span: 24,
                maxlength: 500,
                showWordLimit: true,
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getClinicalCooperation();
    this.getDetail();
  },
  methods: {
    getClinicalCooperation() {
      getClinicalCooperation().then((res) => {
        this.clinicalCooperationList = res.data.data;
        this.option.group[0].column[1].dicData = res.data.data;
      });
    },
    //服务项目类型修改
    projectTypeChange(actType) {
      this.actType = actType;
      if (actType == 5 || actType == 8) {
        this.option.group[0].column[2].display = true;
      } else {
        this.option.group[0].column[2].display = false;
      }
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          _this.diseaseDomain = _this.form.diseaseDomain
            .split(",")
            .filter((item) => item != ""); //疾病领域
          // _this.departmentName = _this.form.departmentName
          //   .split(",")
          //   .filter((item) => item != ""); //目标科室
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
  },
};
</script>

<style scope>
.avue-crud__menu {
  min-height: 0 !important;
}
h4 {
  margin: 5px 0 !important;
}
.first-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
