<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="taxCertStatus" slot-scope="{ row }">
        <el-tag v-if="row.taxCertStatus == 1" type="info" size="small"
          >未完税</el-tag
        >
        <el-tag v-if="row.taxCertStatus == 2" type="success" size="small"
          >已完税</el-tag
        >
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button
          v-if="permission.summaryTaxCert_summaryTaxCertDetail"
          :size="scope.size"
          :type="scope.type"
          @click="toDetail2(scope.row)"
          >完税明细</el-button
        >
        <el-button
          v-if="permission.summaryTaxCert_view"
          :size="scope.size"
          :type="scope.type"
          @click="toDetail(scope.row)"
          >查看</el-button
        >
        <el-button
          v-if="permission.summaryTaxCert_view"
          :size="scope.size"
          type="text"
          @click="toEdit(scope.row)"
          >编辑</el-button
        >
        <!-- <el-button
          v-if="permission.summaryTaxCert_view && scope.row.taxCertStatus != 2"
          :size="scope.size"
          type="text"
          @click="rowDel(scope.row)"
          >删除</el-button
        > -->
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  summaryTaxCertUpload,
} from "@/api/summarytaxcert/summaryTaxCert";
import option from "@/const/summarytaxcert/summaryTaxCert";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.summaryTaxCert_add, false),
        viewBtn: this.vaildData(this.permission.summaryTaxCert_view, false),
        delBtn: this.vaildData(this.permission.summaryTaxCert_delete, false),
        editBtn: this.vaildData(this.permission.summaryTaxCert_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    // 去详情
    toDetail(row) {
      this.$router.push({
        path: `/summarytaxcert/detail/${row.id}`,
      });
    },
    // 去完税明细
    toDetail2(row) {
      this.$router.push({
        path: `/summarytaxcertdetail/summaryTaxCertDetail`,
        query: {
          id: row.id,
        },
      });
    },
    // 去编辑
    toEdit(row) {
      this.$router.push({
        path: `/summarytaxcert/edit/${row.id}`,
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    open(row) {
      this.dialogVisible = true;
      this.invoiceId = row.id;
    },
    uploadExceed() {
      this.$message.error("每次只能上传一个文件，请先移除再上传");
    },
    submit() {
      this.invoiceForm.id = this.invoiceId;
      summaryTaxCertUpload(this.invoiceForm).then((res) => {
        let data = res.data;
        if (data.success) {
          this.onLoad(this.page);
          this.dialogVisible = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        }
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
