import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/settlementOrder/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/settlementOrder/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (id) => {
  return request({
    url: "/api/blade-svc/settlementOrder/remove",
    method: "post",
    params: {
      id,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-svc/settlementOrder/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/settlementOrder/submit",
    method: "post",
    data: row,
  });
};

//生成企业会员发票单
export const addEntrustedInvoice = (settlementOrderId) => {
  return request({
    url: "/api/blade-svc/entrustedInvoice/addEntrustedInvoice",
    method: "get",
    params: {
      settlementOrderId,
    },
  });
};

//验收审核
export const audit = (data) => {
  return request({
    url: "/api/blade-svc/settlementOrder/audit",
    method: "post",
    data: data,
  });
};

//获取结算项目明细列表
export const getSettlementOrderItemList = (current, size, resultOrderId) => {
  return request({
    url: "/api/blade-svc/resultOrderProjectDetail/list",
    method: "get",
    params: {
      current,
      size,
      resultOrderId,
    },
  });
};

//获取服务结算单应税项目明细列表
export const getSettlementOrderTaxItemList = (
  current,
  size,
  settlementOrderId
) => {
  return request({
    url: "/api/blade-svc/settlementOrderTaxItem/list",
    method: "get",
    params: {
      current,
      size,
      settlementOrderId,
    },
  });
};

//去结算
export const toSettle = (params) => {
  return request({
    url: "/api/blade-svc/settlementOrder/toSettle",
    method: "get",
    params,
  });
};

//确认结算
export const confim = (ids) => {
  return request({
    url: "/api/blade-svc/settlementOrder/confim",
    method: "post",
    params: {
      ids,
    },
  });
};

//确认推送
export const push = (id) => {
  return request({
    url: "/api/blade-svc/settlementOrder/confimMsgPool",
    method: "post",
    params: {
      id,
    },
  });
};
