import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/entrustedInvoicePoint/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/entrustedInvoicePoint/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (id) => {
  return request({
    url: "/api/blade-svc/entrustedInvoicePoint/remove",
    method: "post",
    params: {
      id,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-svc/entrustedInvoice/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/entrustedInvoice/update",
    method: "post",
    data: row,
  });
};
//发票提交(2开票中-->3已开票)
export const invoiceSubmit = (id, status) => {
  return request({
    url: "/api/blade-svc/entrustedInvoicePoint/invoiceSubmit",
    method: "post",
    params: {
      id,
      status,
    },
  });
};
export const getInvoiceItem = (current, size, entrustedInvoiceId) => {
  return request({
    url: "/api/blade-svc/entrustedInvoiceItem/list",
    method: "get",
    params: {
      entrustedInvoiceId,
      current,
      size,
    },
  });
};
//乐税发票查验
export const invoiceCheck = (row) => {
  return request({
    url: "/api/blade-svc/entrustedInvoice/invoiceCheck",
    method: "post",
    data: row,
  });
};
//上传企业会员发票信息
export const uploadInvoice = (data) => {
  return request({
    url: "/api/blade-svc/entrustedInvoiceDetailPoint/uploadInvoice",
    method: "post",
    data,
  });
};
//发票删除
export const removeInvoice = (id) => {
  return request({
    url: "/api/blade-svc/entrustedInvoiceDetailPoint/removeInvoice",
    method: "get",
    params: {
      id,
    },
  });
};

export const submitReview = (row) => {
  return request({
    url: "/api/blade-svc/entrustedInvoicePoint/submitReview",
    method: "post",
    data: row,
  });
};
