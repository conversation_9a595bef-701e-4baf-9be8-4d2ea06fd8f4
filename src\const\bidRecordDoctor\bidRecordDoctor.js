export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "服务招募ID",
      prop: "tenderId",
      type: "input",
    },
    {
      label: "企业名称ID",
      prop: "entrustedCompanyId",
      type: "input",
    },
    {
      label: "个人服务商ID",
      prop: "serviceProviderId",
      type: "input",
    },
    {
      label: "姓名(个人服务商)",
      prop: "serviceProviderName",
      type: "input",
      span: 24,
      search: true,
      rules: [
        {
          required: true,
          message: "请输入姓名(个人服务商)",
          trigger: "blur",
        },
      ],
    },
    {
      label: "手机号",
      prop: "doctorPhone",
      type: "input",
    },
    {
      label: "身份证号",
      prop: "idCardNumber",
      type: "input",
    },
    {
      label: "报名时间",
      prop: "bidTime",
      type: "input",
    },
    {
      label: "服务目标区域-省",
      prop: "serviceProvince",
      type: "input",
    },
    {
      label: "服务目标区域-市",
      prop: "serviceCity",
      type: "input",
    },
    {
      label: "报名说明",
      prop: "bidRemark",
      type: "input",
    },
    {
      label: "声明签字涵",
      prop: "signature",
      type: "input",
    },
    {
      label: "审核状态 1待审核 2 已确认 3驳回 4招募结束 字典bid_audit_status",
      prop: "auditStatus",
      type: "input",
    },
    {
      label: "审核内容",
      prop: "auditContext",
      type: "input",
    },
    {
      label: "审核人",
      prop: "auditor",
      type: "input",
    },
    {
      label: "审核时间",
      prop: "auditTime",
      type: "input",
    },

    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
