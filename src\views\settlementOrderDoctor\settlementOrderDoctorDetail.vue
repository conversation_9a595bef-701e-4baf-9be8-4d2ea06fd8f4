<template>
  <div>
    <basic-container>
      <el-row :gutter="20" style="display: flex; align-items: center">
        <el-col :span="10">
          <el-page-header @back="goBack" :content="$route.name">
          </el-page-header>
        </el-col>
        <el-col
          :span="14"
          v-if="$route.query.type != 'view'"
          style="text-align: right"
        >
          <!--<el-button-->
            <!--type="success"-->
            <!--v-if="-->
              <!--permission.settlementOrderDoctor_audit &&-->
              <!--form1.approvalStatus == 0-->
            <!--"-->
            <!--@click="onAudit(1)"-->
            <!--&gt;通过</el-button-->
          <!--&gt;-->
          <!--<el-button-->
            <!--type="danger"-->
            <!--v-if="-->
              <!--permission.settlementOrderDoctor_audit &&-->
              <!--form1.approvalStatus == 0-->
            <!--"-->
            <!--@click="onAudit(2)"-->
            <!--&gt;驳回</el-button-->
          <!--&gt;-->
          <el-button
            v-if="
              permission.settlementOrderDoctor_addEntrustedInvoice &&
              form1.entrustedinvoiceInvoice == 0
            "
            type="primary"
            :loading="loading"
            @click="addEntrustedInvoice()"
            >生成企业发票单</el-button
          >
        </el-col>
      </el-row>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form1)"
        ref="form1"
        :option="option1"
        v-model="form1"
        @submit="submit"
      >
        <!-- 客户成果单编号 -->
        <template slot-scope="{}" slot="resultOrderCode">
          <div class="to-view" @click="toResultOrderView()">
            <a readonly>
              {{ form1.resultOrderCode }}
            </a>
          </div>
        </template>
        <template slot-scope="{}" slot="entrustedCompanyName">
          <div class="to-view" @click="toEntrustedCompanyView()">
            <a readonly>
              {{ form1.entrustedCompanyName }}
            </a>
          </div>
        </template>
        <template slot-scope="{ disabled, size }" slot="settlementOrderFile">
          <div style="cursor: pointer">
            <el-tag @click="preview(form1.settlementOrderFile)"
              >《企业服务结算清单》</el-tag
            >
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
      <div style="height: 50px"></div>
      <!-- 结算项目明细 开始 -->
      <avue-crud
        :option="settlementOrderItemOption"
        :summary-method="summaryMethod"
        :table-loading="loading"
        :data="settlementOrderItemData"
        v-model="form"
        ref="crud"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @selection-change="selectionDoctorChange"
        @on-load="onLoad"
      >
      </avue-crud>
      <!-- 结算项目明细 结束 -->
      <div style="height: 80px"></div>
      <!-- 应税项目 开始 -->
      <avue-crud
        :table-loading="activityLoading"
        :summary-method="summaryMethod2"
        :data="activityData"
        :option="activityOption"
        @refresh-change="refreshChangeActivity"
      >
      </avue-crud>
      <!-- 应税项目 结束 -->
      <div style="height: 120px"></div>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/settlementOrder/settlementOrder";
import {
  addEntrustedInvoice,
  audit,
  getSettlementOrderItemList,
  getSettlementOrderTaxItemList,
} from "@/api/settlementOrderDoctor/settlementOrderDoctor";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      activityLoading: true,
      //弹窗
      dialogOption: {
        submitText: "完成",
        span: 24,
        column: [
          {
            label: "验收说明",
            prop: "auditContent",
            type: "textarea",
            maxlength: 500,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入验收说明",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },
      id: "",
      resultOrderId:"",
      form1: {},
      option1: {
        disabled: true,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: "170",
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",
            column: [
              {
                label: "结算单编号",
                prop: "code",
                type: "input",
              },

              {
                label: "结算单名称",
                prop: "name",
                type: "input",
              },
              {
                label: "客户成果单编号",
                prop: "resultOrderCode",
                type: "input",
              },
              {
                label: "客户名称",
                prop: "entrustedCompanyName",
                type: "input",
              },
              {
                label: "结算期间",
                prop: "accountPeriod",
              },
              {
                label: "服务开始日期",
                prop: "actStartTime",
                type: "date",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "服务结束日期",
                prop: "actEndTime",
                type: "date",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "服务活动结算金额(元)",
                prop: "settlementActivityAmount",
                type: "input",
              },
              {
                label: "平台服务管理费(元)",
                prop: "settlementManageAmount",
                type: "input",
              },
              {
                label: "服务代收代缴税费(元)",
                prop: "settlementTaxAmount",
                type: "input",
              },
              {
                label: "服务增值税金额(元)",
                prop: "settlementVatAmount",
                type: "input",
              },
              {
                label: "结算单总金额(元)",
                prop: "settlementTotalAmount",
                type: "input",
              },
              {
                label: "结算时间",
                prop: "createTime",
                type: "date",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                span: 8,
                label: "服务结算单报告",
                prop: "settlementOrderFile",
                type: "upload",
                // listType: 'picture-img',
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
            ],
          },
          // {
          //   label: "审核结果",
          //   arrow: true,
          //   prop: "group3",
          //   column: [
          //     {
          //       label: "审核时间",
          //       prop: "approvalDate",
          //       type: "datetime",
          //       format: "yyyy-MM-dd HH:mm:ss",
          //       valueFormat: "yyyy-MM-dd HH:mm:ss",
          //     },
          //     {
          //       label: "审核状态",
          //       prop: "approvalStatus",
          //       type: "select",
          //       dicData: [
          //         {
          //           label: "待审核",
          //           value: 0,
          //         },
          //         {
          //           label: "审核通过",
          //           value: 1,
          //         },
          //         {
          //           label: "审核驳回",
          //           value: 2,
          //         },
          //       ],
          //     },
          //     {
          //       label: "复审说明",
          //       prop: "approvalRemark",
          //       type: "input",
          //     },
          //     {
          //       label: "复审人",
          //       prop: "approvalOfficer",
          //       type: "input",
          //     },
          //   ],
          // },
        ],
      },
      form: {},
      query: {},
      loading: false,
      loadingTwo: false,
      loadingThree: false,
      data: [],

      settlementOrderItemOption: {
        maxHeight: "600",
        title: "结算项目明细",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        menu: false,
        selection: true,
        dialogClickModal: false,
        headerAlign: "center",
        align: "center",
        showSummary: true,
        sumColumnList: [
          {
            name: "resultNum",
            type: "sum",
          },
          {
            name: "doctorPaymentAmount",
            type: "sum",
          },
          {
            name: "customPaymentAmount",
            type: "sum",
          },
        ],
        column: [
          {
            label: "服务项目编号",
            prop: "projectCode",
            type: "input",
          },
          {
            label: "服务项目名称",
            prop: "projectTypeName",
            type: "input",
          },
          {
            label: "服务活动应税项目",
            prop: "projectTaxItem",
            type: "input",
          },
          {
            label: "验收服务活动数量",
            prop: "checkNumber",
            type: "input",
          },
          {
            label: "验收服务活动金额(元)",
            prop: "checkAmount",
            type: "input",
          },
          // {
          //   label: "客户结算金额(元)",
          //   prop: "customPaymentAmount",
          //   type: "input",
          // },
        ],
      },
      settlementOrderItemData: [],
      page: {
        pageSize: 10000,
        currentPage: 1,
        total: 0,
      },
      activityData: [],
      activityOption: {
        maxHeight: "600",
        title: "应税项目明细",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        menu: false,
        dialogClickModal: false,
        showSummary: true,
        sumColumnList: [
          {
            name: "settlementAmount",
            type: "sum",
          },
          {
            name: "proportion",
            type: "sum",
          },
        ],
        column: [
          {
            label: "服务结算应税项目",
            prop: "serviceTaxItem",
            type: "input",
          },
          {
            label: "服务活动金额(元)",
            prop: "settlementActivityAmount",
            type: "input",
          },
          {
            label: "平台服务费(元)",
            prop: "settlementManageAmount",
            type: "input",
          },
          {
            label: "服务增值税(元)",
            prop: "settlementVatAmount",
            type: "input",
          },
          {
            label: "会员个税金额(元)",
            prop: "settlementTaxAmount",
            type: "input",
          },
          {
            label: "客户结算金额(元)",
            prop: "settlementAmount",
            type: "input",
          },
          {
            label: "所占比例(%)",
            prop: "proportion",
            type: "input",
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission", "createEvidenceFileStatus"]),
  },
  methods: {
    summaryMethod({ columns }) {
      const sums = [];
      if (columns.length > 0) {
        columns.forEach((column, index) => {
          let prop = column.property;
          if (
            [
              "projectCode",
              "checkNumber",
              "checkAmount",
              "customPaymentAmount",
            ].includes(prop)
          ) {
            let values = this.settlementOrderItemData.map((item) =>
              Number(item[prop] || 0)
            );
            values.length !== 0
              ? (sums[index] = values.reduce((a, b) => {
                  return a + b;
                }))
              : 0;
            if (prop == "projectCode") {
              sums[index] = "合计";
            }
            if (prop == "checkNumber") {
              sums[index] = sums[index] ? sums[index].toFixed(2) : sums[index];
            }
            if (prop == "checkAmount") {
              sums[index] = sums[index] ? sums[index].toFixed(2) : sums[index];
            }
            if (prop == "customPaymentAmount") {
              sums[index] = sums[index] ? sums[index].toFixed(2) : sums[index];
            }
          } else {
            sums[index] = "-";
          }
        });
      }
      return sums;
    },
    summaryMethod2({ columns }) {
      const sums = [];
      if (columns.length > 0) {
        columns.forEach((column, index) => {
          let prop = column.property;
          if (
            ["serviceTaxItem", "settlementActivityAmount", "settlementManageAmount", "settlementVatAmount", "settlementTaxAmount", "settlementAmount", "proportion"].includes(prop)
          ) {
            let values = this.activityData.map((item) =>
              Number(item[prop] || 0)
            );
            values.length !== 0
              ? (sums[index] = values.reduce((a, b) => {
                  return a + b;
                }))
              : 0;
            if (prop == "serviceTaxItem") {
              sums[index] = "合计";
            }
            if (prop == "settlementActivityAmount") {
              sums[index] = sums[index] ? sums[index].toFixed(2) : sums[index];
            }
            if (prop == "settlementManageAmount") {
              sums[index] = sums[index] ? sums[index].toFixed(2) : sums[index];
            }
            if (prop == "settlementVatAmount") {
              sums[index] = sums[index] ? sums[index].toFixed(2) : sums[index];
            }
            if (prop == "settlementTaxAmount") {
              sums[index] = sums[index] ? sums[index].toFixed(2) : sums[index];
            }
            if (prop == "settlementAmount") {
              sums[index] = sums[index] ? sums[index].toFixed(2) : sums[index];
            }
            if (prop == "proportion") {
              // sums[index] = sums[index] ? sums[index].toFixed(2) : sums[index];
              sums[index] = 100.00;
            }
          } else {
            sums[index] = "-";
          }
        });
      }
      return sums;
    },

    //验收
    onAudit(auditStatus) {
      let _this = this;
      if (auditStatus == 1) {
        this.$confirm("确定将该条数据审核通过?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let data = {
              auditStatus: auditStatus,
              id: _this.id,
              auditContent: "同意",
            };
            return audit(data);
          })
          .then(() => {
            this.getDetail();
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      } else {
        _this.$DialogForm.show({
          title: "审核驳回",
          width: "30%",
          menuPosition: "right",
          option: this.dialogOption,
          beforeClose: (done) => {
            setTimeout(() => {
              done();
            }, 100);
          },
          callback: (res) => {
            res.data.auditStatus = auditStatus;
            res.data.id = _this.id;
            audit(res.data).then(
              () => {
                this.getDetail();
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
                res.close();
              },
              (error) => {
                res.done();
                console.log(error);
              }
            );
          },
        });
      }
    },
    // 企业成果单
    toResultOrderView() {
      this.$router.push({
        path: `/resultorder/detail/${this.form1.resultOrderId}`,
      });
    },

    //去企业详情
    toEntrustedCompanyView() {
      this.$router.push({
        path: `/entrustedcompany/detail/${this.form1.entrustedCompanyId}/1`,
      });
    },
    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },

    getDetail() {
      let _this = this;
      _this.activityLoading = true;
      getDetail(this.id).then((res) => {
        _this.activityLoading = false;
        if (res.data.success) {
          _this.form1 = res.data.data;
          _this.resultOrderId = this.form1.resultOrderId
          _this.onLoad(this.page);
          _this.onTaxLoad();
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    refreshChange() {
      this.onLoad(this.page);
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    onLoad(page) {
      getSettlementOrderItemList(page.currentPage, page.pageSize, this.resultOrderId).then(
        (res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.settlementOrderItemData = data.records;
          this.loading = false;
        }
      );
    },
    // 应税查询
    onTaxLoad() {
      getSettlementOrderTaxItemList(0, 10000, this.id).then((res) => {
        const data = res.data.data;
        data.records.map((item) => {
          item.proportion = (item.proportion * 100).toFixed(2);
        });
        this.activityData = data.records;
        this.loading = false;
      });
    },
    refreshChangeActivity() {
      this.onTaxLoad();
    },
    //生成企业发票单
    addEntrustedInvoice() {
      this.loading = true;
      this.$message({
        message: "正在生成中,请稍后!",
        duration: 0,
      });
      addEntrustedInvoice(this.id).then(
        (res) => {
          if (res.data.success) {
            this.loading = false;
            this.$message.closeAll();
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.getDetail();
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (err) => {
          console.log(err);
          setTimeout(() => {
            this.loading = false;
            this.$message.closeAll();
          }, 2000);
        }
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.avue-crud > h2 {
  font-size: 16px !important;
  font-weight: 500 !important;
}
</style>
