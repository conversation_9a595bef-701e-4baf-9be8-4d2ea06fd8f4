import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-csc/entrustedDept/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const lazyList = (parentId, params) => {
  return request({
    url: "/api/blade-csc/entrustedDept/lazy-list",
    method: "get",
    params: {
      parentId,
      ...params,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-csc/entrustedDept/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-csc/entrustedDept/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-csc/entrustedDept/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-csc/entrustedDept/submit",
    method: "post",
    data: row,
  });
};
//获取树形结构
export const tree = () => {
  return request({
    url: "/api/blade-csc/entrustedDept/tree",
    method: "get",
  });
};
