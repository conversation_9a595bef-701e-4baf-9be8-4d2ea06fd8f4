export default {
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: false,
  viewBtn: false,
  delBtn: false,
  editBtn: false,
  addBtn: false,
  selection: false,
  dialogClickModal: false,
  menuWidth: 100,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "活动ID（SFE）",
      prop: "businessActivityId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "活动名称",
      prop: "name",
      type: "input",
      width: 200,
      search: true,
    },
    {
      label: "产品名",
      prop: "baseProductName",
      type: "input",
      search: true,
    },
    {
      label: "协议签署状态",
      prop: "agreementStatus",
      type: "select",
      search: true,
      searchLabelWidth: 100,
      dicData: [
        {
          label: "待签署",
          value: 0,
        },
        {
          label: "同意",
          value: 1,
        },
        {
          label: "拒绝",
          value: 2,
        },
        {
          label: "作废",
          value: 3,
        },
      ],
    },
    {
      label: "活动编号",
      prop: "code",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "业务代表工号",
      prop: "baseEmployeeNumber",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "业务代表姓名",
      prop: "baseEmployeeName",
      type: "input",
      width: 120,
    },
    {
      label: "业务代表部门名称",
      prop: "baseDepartmentName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "产品线",
      prop: "baseProductLine",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "会议类型",
      prop: "type",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "协议附件地址",
      prop: "contractUrl",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "生成图片地址",
      prop: "imageUrl",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "签署后附件地址",
      prop: "fileUrl",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },

    {
      label: "企业客户ID",
      prop: "entrustedCompanyId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "企业客户名称",
      prop: "entrustedCompanyName",
      type: "input",
    },
    {
      label: "会议目的",
      prop: "purpose",
      type: "input",
    },
    {
      label: "会议举办形式",
      prop: "format",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "会议详细地址",
      prop: "address",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "讲者姓名",
      prop: "doctorName",
      type: "input",
    },
    {
      label: "讲者身份证号",
      prop: "doctorIdCard",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "讲者手机号",
      prop: "phone",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "协议签署时间",
      prop: "agreementTime",
      type: "date",
      width: 150,
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
    },
    {
      label: "活动计划开始时间",
      prop: "invitationStartDate",
      type: "date",
      searchLabelWidth: 135,
      width: 150,
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      searchRange: true,
      search: true,
    },
    {
      label: "活动计划结束时间",
      prop: "invitationEndDate",
      type: "date",
      searchLabelWidth: 135,
      width: 150,
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      searchRange: true,
      search: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
