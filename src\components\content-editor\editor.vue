<template>
  <div class="content-editor">
    <el-input
      type="textarea"
      :rows="3"
      placeholder="请编辑内容？"
      :value="content"
      @input="handleContentChange"
    />
    <content-render
      :value="value"
      :readonly="false"
      @remove="remove"
    />
    <div class="editor-btn-container">
      <span class="editor-btn" @click="fileUpload('image')">
        <i class="el-icon-camera"/>
        <span>图片</span>
      </span>
      <span class="editor-btn" @click="fileUpload('video')">
        <i class="el-icon-video-play"/>
        <span>视频</span>
      </span>
      <span class="editor-btn" @click="fileUpload('audio')">
        <i class="el-icon-microphone"/>
        <span>音频</span>
      </span>
      <span class="editor-btn" @click="fileUpload('attachment')">
        <i class="el-icon-document"/>
        <span>附件</span>
      </span>
    </div>
    <input
      ref="fileInput"
      type="file"
      value=""
      @change="handleFileChange"
      :accept="acceptMapping[fileType]"
      style="display: none"
    />
  </div>
</template>

<script>
import {nextTick} from "vue";

const ACCEPT_MAPPING = {
  'image': 'image/*',
  'video': 'video/*',
  'audio': 'audio/*',
  'attachment': '.doc,.docx,.xls,.xlsx'
}
import request from '@/router/axios';
import ContentRender from "@/components/content-editor/render.vue";
export default {
  name: 'ContentEditor',
  components: {ContentRender},
  data() {
    return {
      acceptMapping: ACCEPT_MAPPING,
      fileType: 'image',
    }
  },
  props: {
    value: {
    }
  },
  computed: {
    content() {
      if(!this.value) {
        return null
      }
      return this.value.content || ''
    },
  },
  methods: {
    fileUpload(fileType) {
      this.fileType = fileType
      nextTick(() => {
        this.$refs.fileInput.click()
      })
    },
    // 处理文件选择
    handleFileChange(event){
      const target = event.target
      const file = target.files[0]

      if(!file) {
        return
      }

      try {
        // 创建 FormData
        const formData = new FormData()
        formData.append('file', file)

        const loading = this.$loading({
          lock: true,
          text: "上传中...",
          spinner: "el-icon-loading",
        });
        // 发送请求
         request({
          url: '/api/blade-resource/oss/endpoint/put-file',
          method: "post",
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }).then(res => {
          this.setContent(this.fileType, res.data.data)
        }).finally(() => {
          loading.close()
        })

      } catch (error) {
        console.error('Upload failed:', error)
      }
    },
    handleContentChange(value) {
      const newValue = {
        ...this.value,
        content: value
      }
      this.emitChange(newValue)
    },
    remove(type, index) {
      const content = this.getContent(type)
      content.splice(index, 1)

      const newValue = {
        ...this.value,
        [type]: content
      }
      this.emitChange(newValue)
    },
    getContent(type) {
      if(!this.value) {
        return []
      }
      return this.value[type] ? this.value[type] : []
    },
    setContent(type, data) {
      const content = this.getContent(type) || []
      content.push(data)
      const newValue = {
        ...this.value,
        [type]: content
      }
      this.emitChange(newValue)
    },
    emitChange(newValue) {
      if(!newValue.content) {
        delete newValue.content
      }
      if(!newValue.image || newValue.image.length === 0) {
        delete newValue.image
      }
      if(!newValue.video || newValue.video.length === 0) {
        delete newValue.video
      }
      if(!newValue.audio || newValue.audio.length === 0) {
        delete newValue.audio
      }
      if(!newValue.attachment || newValue.attachment.length === 0) {
        delete newValue.attachment
      }

      newValue = Object.keys(newValue).length > 0 ? newValue : null
      console.log(newValue)
      this.$emit('input', newValue)
      this.$emit('change', newValue)
    }
  }
}
</script>

<style lang="scss" scoped>
.content-editor {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.editor-btn-container {
  display: flex;
  justify-content: flex-start;
  gap: 50px;
}
.editor-btn {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
  color: #838383;
  cursor: pointer;
  i {
    font-size: 24px;
  }
  span {
    font-size: 12px;
    line-height: 12px;
  }
}


.content-render {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  .render-image-wrapper {
    position: relative;
  }
  .render-video-wrapper {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    video {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%
    }
  }
  .render-audio-wrapper {
    position: relative;
  }
  .render-a-wrapper {
    position: relative;
  }
  .remove {
    position: absolute;
    right: 8px;
    top: 8px;
  }
  .remove-inline {
    top: 50%;
    transform: translateY(-50%);
    right: 20px;
  }
}
</style>
