<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ type, size, row, index }" slot="doctorName">
        <div
          class="to-view"
          @click="toDetail2(row.doctorId)"
          v-if="row.doctorName"
        >
          <a readonly>
            {{ row.doctorName }}
          </a>
        </div>
        <div v-else>无</div>
      </template>
      <template slot="menuLeft">
        <!-- <el-button
          v-if="permission.tenderDoctorProtocols_printOrDownload"
          type="primary"
          @click="viewpdf"
          >打印下载
        </el-button> -->
      </template>
      <template slot-scope="scope" slot="menu"> </template>
      <template slot="protocolStatus" slot-scope="{ row }">
        <el-tag v-if="row.protocolStatus == 1" size="small">初始化起草</el-tag>
        <el-tag v-if="row.protocolStatus == 2" type="success" size="small"
          >已生效（签署完成）</el-tag
        >
        <el-tag v-if="row.protocolStatus == 3" type="info" size="small"
          >已作废</el-tag
        >
        <el-tag v-if="row.protocolStatus == 4" type="info" size="small"
          >已过期</el-tag
        >
      </template>
    </avue-crud>
    <!-- 打印下载弹窗 -->
    <el-dialog
      fullscreen
      custom-class="pdf"
      title="协议预览"
      append-to-body
      width="50%"
      :visible.sync="pdfVisible"
    >
      <iframe
        :src="pdfUrl"
        width="100%"
        :height="iframeHeight"
        title="协议"
        frameBorder="no"
        border="0"
        marginWidth="0"
        marginHeight="0"
        scrolling="no"
        allowTransparency="yes"
      ></iframe>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove,
  getProtocolFile,
} from "@/api/tenderDoctorProtocols/tenderDoctorProtocols";
import option from "@/const/tenderDoctorProtocols/tenderDoctorProtocols";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      iframeHeight: window.innerHeight - 180,
      pdfVisible: false, //预览弹窗
      pdfUrl: "", //pdf路径
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.tenderDoctorProtocols_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.tenderDoctorProtocols_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.tenderDoctorProtocols_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.tenderDoctorProtocols_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    // 去详情
    toDetail(row) {
      this.$router.push({
        path: `/tenderDoctorProtocols/detail/${row.id}`,
      });
    },
    // 去详情
    toDetail2(id) {
      this.$router.push({
        path: `/authenticationDoctor/detail/${id}/1`,
      });
    },
    // 打印下载
    viewpdf() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择一条要操作的数据");
        return;
      }
      if (this.selectionList.length > 1) {
        this.$message.error("只能选择一条数据");
        return;
      }
      getProtocolFile(this.ids).then((res) => {
        if (res.data.success) {
          this.pdfUrl = res.data.data;
          this.pdfVisible = true;
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.toDetail(this.form);
      } else if (type == "edit") {
        this.toEdit(this.form);
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
