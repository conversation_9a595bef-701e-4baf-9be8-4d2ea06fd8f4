<template>
  <div>
    <basic-container
      ><el-page-header @back="goBack" :content="$route.name"></el-page-header
    ></basic-container>
    <basic-container>
      <!--企业信息-->
      <avue-form
        v-if="!validatenull(form1)"
        ref="form1"
        :option="option1"
        v-model="form1"
        @submit="submit"
      >
        <template slot-scope="{}" slot="name">
          <el-row>
            <el-col :span="18">
              <el-input
                v-model="form1.name"
                placeholder="请输入公司全称"
              ></el-input>
            </el-col>
            <el-col :span="6">
              <div class="getTycBaseInfo" @click="getTycBaseInfo">
                天眼查查询
              </div></el-col
            >
          </el-row>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
  </div>
</template>

<script>
import {
  getDetail,
  updateEntrustedCompany,
  getTycBaseInfo,
} from "@/api/entrustedcompany/entrustedCompany";
export default {
  data() {
    // 验证统一信用代码
    const validateSocialCreditCode = (rule, value, callback) => {
      let reg = /^[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/;
      if (value === "") {
        callback(new Error("请输入信用代码"));
      } else {
        if (!reg.test(value)) {
          callback(new Error("请输入正确的信用代码格式"));
        } else {
          callback();
        }
      }
    };

    // 手机号验证
    const validateLinkTel = (rule, value, callback) => {
      let reg = /^(1\d{10})$/;
      if (value === "") {
        callback(new Error("请输入手机号"));
      } else {
        if (!reg.test(value)) {
          callback(new Error("请输入合法的手机号"));
        } else {
          callback();
        }
      }
    };
    // 验证身份证号
    const validateIdCard = (rule, value, callback) => {
      let reg =
        /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
      if (value === "") {
        callback(new Error("请输入身份证号"));
      } else {
        if (!reg.test(value)) {
          callback(new Error("请输入合法身份证"));
        } else {
          callback();
        }
      }
    };

    return {
      id: "",
      query: {},
      loading: false,
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      form1: {},
      option1: {
        submitText: "保存并提交审核",
        disabled: false,
        labelWidth: "170",
        submitIcon: " ",
        emptyIcon: " ",
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",

            column: [
              {
                label: "全称",
                prop: "name",
                rules: [
                  {
                    required: true,
                    message: "请输入公司全称",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "简称",
                prop: "shortName",
              },
              {
                label: "企业类型",
                prop: "regType",
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=reg_type",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                rules: [
                  {
                    required: true,
                    message: "请选择企业类型",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "统一社会信用代码",
                prop: "socialCreditCode",
                rules: [
                  {
                    required: true,
                    message: "请输入统一社会信用代码",
                    trigger: ["blur", "change"],
                  },
                  {
                    validator: validateSocialCreditCode,
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "法定代表人",
                prop: "lawPerson",
                rules: [
                  {
                    required: true,
                    message: "请输入法定代表人",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "法人身份证号",
                prop: "lawPersonIdNum",
                maxlength: 18,
                rules: [
                  {
                    required: true,
                    message: "请输入法人身份证号",
                    trigger: ["blur", "change"],
                  },
                  {
                    validator: validateIdCard,
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "成立时间",
                prop: "regDate",
                type: "date",
                format: "yyyy年MM月dd日",
                valueFormat: "yyyy-MM-dd",
                rules: [
                  {
                    required: true,
                    message: "请选择成立时间",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "注册资本/万元",
                prop: "regCapital",
                rules: [
                  {
                    required: true,
                    message: "请输入注册资本",
                    trigger: ["blur", "change"],
                  },
                ],
              },

              {
                label: "企业邮箱",
                prop: "mail",
              },

              {
                label: "注册地址",
                prop: "regAddress",
                rules: [
                  {
                    required: true,
                    message: "请输入企业注册地址",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "企业电话",
                prop: "telphone",
                maxlength: 20,
              },
            ],
          },
          {
            label: "业务信息",
            prop: "detailInfo",
            column: [
              {
                label: "经营状态",
                prop: "operatStatus",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=operat_status",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
                rules: [
                  {
                    required: true,
                    message: "请选择经营状态",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "行业性质",
                prop: "industryNature",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=industry_nature",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "string",
                rules: [
                  {
                    required: true,
                    message: "请选择行业性质",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "纳税规模",
                prop: "taxScale",
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=tax_scale",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
                rules: [
                  {
                    required: true,
                    message: "请选择纳税规模",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "经营范围",
                prop: "bizScope",
                type: "textarea",
                rules: [
                  {
                    required: true,
                    message: "请选择经营范围",
                    trigger: ["blur", "change"],
                  },
                ],
              },
            ],
          },
          {
            label: "开票信息",
            prop: "dutyInfo",
            column: [
              {
                label: "开户行",
                prop: "bankName",
                rules: [
                  {
                    required: true,
                    message: "请输入开户行",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "银行账号",
                prop: "bankAccount",
                rules: [
                  {
                    required: true,
                    message: "请输入银行账号",
                    trigger: ["blur", "change"],
                  },
                ],
              },
            ],
          },
          {
            label: "联系信息",
            prop: "dutyInfoss",
            column: [
              {
                label: "省份",
                prop: "province",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["city"],
                dicUrl: "/api/blade-system/region/select",
                span: 8,
                rules: [
                  {
                    required: true,
                    message: "请选择省份",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "城市",
                prop: "city",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["district"],
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 8,
                rules: [
                  {
                    required: true,
                    message: "请选择城市",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "地区",
                prop: "district",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                rules: [
                  {
                    required: true,
                    message: "请选择地区",
                    trigger: ["blur", "change"],
                  },
                ],
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 8,
              },
              {
                label: "详细地址",
                prop: "address",
                type: "textarea",
              },
            ],
          },
          {
            label: "认证信息",
            prop: "authenticationInfo",
            column: [
              {
                span: 6,
                label: "身份证正面",
                prop: "atta1",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                rules: [
                  {
                    required: true,
                    message: "请上传身份证正面",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                span: 6,
                label: "身份证反面",
                prop: "atta2",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                rules: [
                  {
                    required: true,
                    message: "请上传身份证反面",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                span: 6,
                label: "营业执照",
                prop: "atta3",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                rules: [
                  {
                    required: true,
                    message: "请上传营业执照",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                span: 6,
                label: "开户许可证",
                prop: "atta4",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                rules: [
                  {
                    required: true,
                    message: "请上传开户许可证",
                    trigger: ["blur", "change"],
                  },
                ],
              },
            ],
          },
          {
            label: "联系人",
            prop: "comInfo",
            column: [
              {
                label: "联系人/授权代表",
                prop: "linkMan",
                rules: [
                  {
                    required: true,
                    message: "请输入人员姓名",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "联系电话",
                prop: "linkTel",
                maxlength: 11,
                rules: [
                  {
                    required: true,
                    message: "请输入人员手机号",
                    trigger: ["blur", "change"],
                  },
                  {
                    validator: validateLinkTel,
                    trigger: ["blur", "change"],
                  },
                ],
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  methods: {
    getTycBaseInfo() {
      if (!this.form1.name) {
        this.$message({
          type: "error",
          message: "请填写公司全称",
        });
        return;
      }
      getTycBaseInfo(this.form1.name).then((res) => {
        let data = res.data;
        let obj = data.data;
        for (let key in obj) {
          if (
            obj.hasOwnProperty(key) &&
            (obj[key] === null || obj[key] === undefined || obj[key] === "")
          ) {
            delete obj[key];
          }
        }
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.form1 = Object.assign(this.form1, obj);
      });
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          if (res.data.data.auditStatus == 3) {
            this.option1.group[0].column[0].disabled = true;
            this.option1.group[0].column[3].disabled = true;
          } else {
            this.option1.group[0].column[0].disabled = false;
            this.option1.group[0].column[3].disabled = false;
          }
          _this.form1 = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    submit(form, done) {
      this.$refs.form1.validate((valid) => {
        if (valid) {
          updateEntrustedCompany(form).then(
            () => {
              this.goBack();
              this.$message({
                type: "success",
                message: "更新成功!",
              });
              done();
            },
            (error) => {
              done();
              console.log(error);
            }
          );
        }
      });
    },
    openSignature() {},
  },
};
</script>

<style>
.avue-crud__menu {
  min-height: 0 !important;
}

h4 {
  margin: 5px 0 !important;
}

.first-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.getTycBaseInfo {
  border: 1px solid #e6e6e6;
  text-align: center;
  cursor: pointer;
}
</style>
