import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-pay/entrustedAccount/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-pay/entrustedAccount/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-pay/entrustedAccount/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-pay/entrustedAccount/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-pay/entrustedAccount/submit",
    method: "post",
    data: row,
  });
};

//余额充值
export const recharge = (data) => {
  return request({
    url: "/api/blade-pay/entrustedAccount/recharge",
    method: "post",
    data,
  });
};
