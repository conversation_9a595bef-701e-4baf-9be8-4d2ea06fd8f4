import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/platServiceFee/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/platServiceFee/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-svc/platServiceFee/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-svc/platServiceFee/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/platServiceFee/submit",
    method: "post",
    data: row,
  });
};

//平台服务净收入
export const transfer = (platServiceFeeId) => {
  return request({
    url: "/api/blade-cmb/dmanInternalTransfer/transfer",
    method: "post",
    params: { platServiceFeeId },
  });
};
