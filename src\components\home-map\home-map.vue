<template>
  <div class="mapdrilling-inner">
    <div id="chart" class="chart"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { getGeoJson } from "./map";
import { getRegionalSummary } from "@/api/home/<USER>";
export default {
  name: "homeMap",
  data: () => {
    return {
      partData: [],
      mapChart: null, // echarts实例
      mapStack: [], // 存储地图数据
      timer: null,
    };
  },
  mounted() {
    this.getRegionalSummary();
  },
  methods: {
    getRegionalSummary() {
      getRegionalSummary().then((res) => {
        this.partData = res.data.data;
        this.initChart();
      });
    },
    async initChart() {
      // 初始化echarts实例
      this.mapChart = echarts.init(document.getElementById("chart"));
      this.addChartEvent();

      // 获取渲染地图的相关数据
      this.mapChart.showLoading();

      const { geoJson } = await this.getGeoAndMapData();
      this.mapChart.hideLoading();

      // 注册渲染地图
      const specialAreas = {
        澳门: {
          left: 113,
          top: 20.5,
          width: 0.7,
        },
        香港: {
          left: 115,
          top: 21.3,
          width: 2,
        },
      };

      this.registeRenderMap("china", this.partData, geoJson, specialAreas);
    },

    // 添加绑定事件
    addChartEvent() {
      this.bindResizeWindow(); // 监听屏幕大小改变
    },

    // 监听屏幕大小改变
    bindResizeWindow() {
      window.addEventListener("resize", () => {
        if (this.timer) return;

        this.timer = setTimeout(() => {
          this.mapChart.resize();
          this.timer = null;
        }, 100);
      });
    },

    /**
     * @description: 获取渲染地图的相关数据 geoJson
     */
    async getGeoAndMapData() {
      const [{ data: geoJson }] = await Promise.all([getGeoJson()]);
      return { geoJson };
    },

    /**
     * @description: 注册渲染地图
     * @param {String} mapName 地图名称 同 option/series/map 一致
     * @param {Array} partData 地图部分数据内容
     * @param {Object} geoJson 地图geoJson
     * @param {Object} specialAreas 特殊区域
     */
    registeRenderMap(mapName, partData, geoJson, specialAreas = {}) {
      // 注册地图
      echarts.registerMap(mapName, geoJson, specialAreas);
      // 绘制地图
      this.renderMap(mapName, partData, geoJson);
    },

    /**
     * @description: 绘制地图
     * @param {String} mapName 地图名称 同注册地图方法registerMap 的第一个参数一致
     * @param {Array} partData 地图部分数据内容
     * @param {Object} geoJson 地图geoJson
     */
    renderMap(mapName, partData, geoJson) {
      const seriesData = this.getSeriesDataByPart(partData, geoJson);
      const visualMapMax = this.getVisualMapMax(seriesData);
      const option = {
        title: {
          text: "会员数量地域汇总",
          top: 18,
          left: 22,
          textStyle: {
            color: "#000000",
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "item",
          formatter: (params) => {
            return `<div style='text-align: left;'>
              <div style='font-size: 16px;font-weight: 400;color: #000000;'>${params.name}</div>
            <div style='margin-top:5px;font-size: 16px;font-weight: 400;color: #000000;'><span style='color: #666666;'>人数 : </span>${params.data.value}</div>
            <div style='margin-top:5px;font-size: 16px;font-weight: 400;color: #000000;'><span style='color: #666666;'>交易额 : </span>${params.data.turnover}万</div>
              </div>`;
          },
          backgroundColor: "#FFFFFF",
          textStyle: {
            color: "#000000",
            fontSize: 12,
          },
        },
        // 视觉映射
        visualMap: {
          show: false,
          min: 0,
          max: visualMapMax,
          text: ["高", "低"],
          realtime: true,
          calculable: true,
          inRange: {
            color: ["#F1F7F8", "#E4EFF1", "#84BDC6"], // 色阶范围
          },
          textStyle: {
            color: "#b7def9",
          },
        },
        series: [
          {
            name: mapName, // 系列名称
            selectedMode: "map", //设置不选中
            type: "map",
            map: mapName, // 同 registerMap 方法的第一个参数一致
            zoom: 1.15, // 当前视角的缩放比例
            zlevel: 1, // 用于 Canvas 分层，不同zlevel值的图形会放置在不同的 Canvas 中
            width: "70%",
            top: "20%",
            label: {
              // 非高亮状态下的文本样式
              normal: {
                show: true,
                position: "inside", // 文本标签显示的位置
                textStyle: {
                  color: "#343434", // 文本颜色
                  fontSize: 14,
                },
                // formatter: '{b}\n{c}', // 文本上显示的值  data:[{name: "地名", value: 数据}],  {b}表示label信息,{c}代表value
              },
              // 高亮状态下的文本样式
              emphasis: {
                textStyle: {
                  color: "#FFA91C", // 文本颜色
                },
              },
            },
            itemStyle: {
              // 非高亮状态下的地图块样式
              normal: {
                borderColor: "#84BDC6",
              },
              // 高亮状态下的地图块样式
              emphasis: {
                areaColor: "#1D90A2",
              },
            },

            data: seriesData,
          },
        ],
        animation: true,
        animationDuration: 1000,
        animationDurationUpdate: 600,
        animationEasingUpdate: "cubicInOut",
      };

      // 绘制图表
      this.mapChart.setOption(option);
    },

    /**
     * @description: 根据partData 和 geoJson 生成 seriesData
     * @param {Array} partData 地图部分数据内容
     * @param {Object} geoJson 地图geoJson
     */
    getSeriesDataByPart(partData, geoJson) {
      let data = geoJson.features.map(({ properties }) => ({
        adcode: properties.adcode,
        name: properties.name,
        value: 0,
        turnover: 0,
      }));
      if (geoJson.attach) {
        const attachData = geoJson.attach.map((item) => ({
          adcode: item.adcode,
          name: item.name,
          value: 0,
          turnover: 0,
        }));
        data.push(...attachData);
      }
      data.forEach((item) => {
        const currData = partData.find((i) => i.adCode == item.adcode);
        if (currData) {
          item.value = currData.numberPeople;
          item.turnover = currData.turnover ? currData.turnover : 0;
        }
      });
      return data;
    },

    /**
     * @description: 根据seriesData 动态计算生成 visualMap 的最大值
     * @param {Array} seriesData 地图数据内容
     */
    getVisualMapMax(seriesData) {
      const maxValue = Math.max(...seriesData.map((item) => item.value));
      return parseInt(maxValue) + 50;
    },
  },

  beforeUnmount() {
    // 销毁地图实例
    this.mapChart.dispose();
  },
};
</script>

<style scoped lang="scss">
.mapdrilling-inner {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 894px;
  .chart {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background: #ffffff;
  }
}
</style>
