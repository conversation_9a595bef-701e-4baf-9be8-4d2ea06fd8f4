<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
        @submit="submit"
      >
        <template slot-scope="{}" slot="entrustedCompanyName">
          <div class="to-view" @click="toView()">
            <a readonly>
              {{ form.entrustedCompanyName }}
            </a>
          </div>
        </template>
        <template slot-scope="{}" slot="doctorName">
          <div class="to-view" @click="toViewDoctor()">
            <a readonly>
              {{ form.doctorName }}
            </a>
          </div>
        </template>
        <template slot-scope="{ disabled, size }" slot="activityAgendafile">
          <div style="cursor: pointer">
            <el-tag @click="downloadFile(form.activityAgendafile, 1)"
              >点击下载</el-tag
            >
          </div>
        </template>
        <template slot-scope="{ disabled, size }" slot="speakerCoursewareFile">
          <div style="cursor: pointer">
            <el-tag @click="downloadFile(form.speakerCoursewareFile, 2)"
              >点击下载</el-tag
            >
          </div>
        </template>
        <template slot-scope="{ disabled, size }" slot="speakerAgreementFile">
          <div style="cursor: pointer">
            <el-tag @click="preview(form.speakerAgreementFile)"
              >预览讲者协议签署文件</el-tag
            >
          </div>
        </template>
        <template slot-scope="{}" slot="flieList">
          <el-row :gutter="20">
            <el-col v-for="(i, index) in form.files" :key="index" :span="4">
              <el-card class="box-card2">
                <el-popover
                  placement="top-start"
                  width="200"
                  trigger="hover"
                  :content="form.files[index].originalName"
                >
                  <div style="display: flex" slot="reference">
                    <div class="names" style="flex: 1">名称:</div>
                    <div
                      class="names"
                      style="
                        flex: 4;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      "
                    >
                      {{ i.originalName }}
                    </div>
                  </div>
                </el-popover>

                <div
                  v-if="i.type == 'image'"
                  style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  "
                >
                  <el-image
                    style="margin: 20px 0"
                    class="imgs"
                    :src="i.url"
                    :preview-src-list="[i.url]"
                  >
                  </el-image>
                </div>
                <div v-if="i.type == 'video'" class="w-center">
                  <span
                    @click="toopen(i.url)"
                    class="el-icon-video-camera"
                  ></span>
                </div>
                <div v-if="i.type == 'file'" class="w-center">
                  <span
                    @click="toopen(i.url)"
                    class="el-icon-notebook-2"
                  ></span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </template>

        <template
          slot-scope="{}"
          slot="evaluationInfo2"
          v-if="form.evaluationTemplate != 'null'"
        >
          <div
            class="evaluationInfo questionnaire"
            v-for="(item, index) in evaluationInfo"
            :key="index"
          >
            <div v-if="item.projectType == 2">
              <div class="evaluationInfo-title">
                <el-tag>单选</el-tag>
                <div style="margin-left: 15px">{{ item.projectName }}</div>
              </div>
              <el-radio-group v-model="item.radio">
                <el-radio
                  :disabled="true"
                  v-for="(item2, index2) in item.optionVOList"
                  :label="index2"
                  :key="item2"
                  >{{ item2.optionName }}</el-radio
                >
              </el-radio-group>
            </div>
            <div v-if="item.projectType == 3">
              <div class="evaluationInfo-title">
                <el-tag>评价</el-tag>
                <div style="margin-left: 15px">{{ item.projectName }}</div>
              </div>
              <el-rate :disabled="true" v-model="item.appraiseScore"></el-rate>
            </div>
            <div v-if="item.projectType == 1">
              <div class="evaluationInfo-title">
                <el-tag>简答</el-tag>
                <div style="margin-left: 15px">{{ item.projectName }}</div>
              </div>
              <el-input
                :disabled="true"
                type="textarea"
                :rows="2"
                v-model="item.appraiseText"
              >
              </el-input>
            </div>
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
      <el-dialog
        title="预览"
        :visible.sync="previewDialog"
        append-to-body
        width="50%"
      >
        <iframe :src="fileUrl" width="100%" height="600px"></iframe>
        <span slot="footer" class="dialog-footer"> </span>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/patientEducationReport/patientEducationReport";
// import { getPersonalServiceProviders } from "@/api/csc/personalserviceprovider";
import { mapGetters } from "vuex";
import { downloadFileBlob } from "@/util/util";
export default {
  data() {
    return {
      //预览
      previewDialog: false,
      fileUrl: "",
      id: "",
      form: {},
      evaluationInfo: [],
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "基本信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "企业名称",
                prop: "entrustedCompanyName",
                type: "input",
              },

              {
                label: "上游企业名称",
                prop: "enterpriseName",
                type: "input",
              },

              {
                label: "业务人员名称",
                prop: "baseEmployeeName",
                type: "input",
              },

              {
                label: "代表部门名称",
                prop: "baseDepartmentName",
                type: "input",
              },

              {
                label: "认证会员姓名",
                prop: "doctorName",
                type: "input",
              },
              {
                label: "会员所属医院",
                prop: "hospitalName",
                type: "input",
              },
              {
                label: "会员所属科室",
                prop: "departmentName",
                type: "input",
              },
            ],
          },
          {
            label: "活动信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "患教活动主题",
                prop: "activityTheme",
                type: "input",
                span: 24,
                search: true,
              },
              {
                label: "患教目的",
                prop: "patientEducationPurpose",
                type: "input",
                span: 24,
              },
              {
                label: "患教活动类型",
                prop: "activityType",
                type: "input",
              },
              {
                label: "活动组织形式",
                prop: "activityOrganizationForm",
                type: "input",
              },
              {
                label: "活动举办形式",
                prop: "activityForm",
                type: "input",
              },
              {
                label: "涉及产品名称",
                prop: "baseProductName",
                type: "input",
              },
              {
                label: "活动时长(分钟)",
                prop: "activityDuration",
                type: "input",
              },
              {
                label: "计划开始时间",
                prop: "planStartTime",
                type: "input",
              },

              {
                label: "计划结束时间",
                prop: "planEndTime",
                type: "input",
              },
              {
                label: "实际开始时间",
                prop: "actuallyStartDate",
                type: "input",
              },
              {
                label: "实际结束时间",
                prop: "actuallyEndDate",
                type: "input",
              },

              {
                label: "活动省份名称",
                prop: "provinceName",
                type: "input",
              },

              {
                label: "活动城市名称",
                prop: "cityName",
                type: "input",
              },
              {
                label: "活动地点",
                prop: "address",
                type: "input",
              },
              {
                label: "腾讯会议号",
                prop: "tencentCode",
                type: "input",
              },
              {
                label: "腾讯会议地址",
                prop: "tencentLiveUrl",
                type: "input",
              },
              {
                label: "讲者姓名",
                prop: "speakerName",
                type: "input",
              },
              {
                label: "会议组织人员",
                prop: "enterpriseCollaborator",
                type: "input",
              },
              {
                label: "组织部门/服务商",
                prop: "organizationalDepartment",
                type: "input",
              },
              {
                label: "患教活动组织",
                prop: "educationActivitiesOrganization",
                type: "input",
                span: 24,
              },
              {
                label: "患教关键内容",
                prop: "patientTeachingKeyContent",
                type: "input",
                span: 24,
              },
              {
                label: "患教内容说明",
                prop: "contentDescription",
                type: "input",
                span: 24,
              },
              {
                label: "活动议程表",
                prop: "activityAgendafile",
                type: "input",
              },
              {
                label: "讲者课件",
                prop: "speakerCoursewareFile",
                type: "input",
              },

              {
                label: "活动会议状态",
                prop: "activityStatus",
                type: "select",
                dicData: [
                  {
                    label: "未开始",
                    value: 0,
                  },
                  {
                    label: "进行中",
                    value: 1,
                  },
                  {
                    label: "已结束",
                    value: 2,
                  },
                ],
              },
              {
                span: 24,
                label: "附件",
                prop: "flieList",
                type: "input",
              },
            ],
          },
          {
            label: "协议信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "讲者协议签署文件",
                prop: "speakerAgreementFile",
                type: "input",
              },
              {
                label: "讲者协议签署签名",
                prop: "speakerAgreementSignature",
                listType: "picture-img",
                type: "upload",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },

              {
                label: "讲者协议签署时间",
                prop: "speakerAgreementDate",
                type: "input",
              },
              {
                label: "讲者协议签署状态",
                prop: "speakerAgreementStatus",
                type: "select",
                dicData: [
                  {
                    label: "待签署",
                    value: 0,
                  },
                  {
                    label: "已签署",
                    value: 1,
                  },
                  {
                    label: "作废",
                    value: 3,
                  },
                ],
              },
            ],
          },
          {
            label: "签到信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "讲者签到状态",
                prop: "speakerSignStatus",
                type: "select",
                dicData: [
                  {
                    label: "待签到",
                    value: 0,
                  },
                  {
                    label: "已签到",
                    value: 1,
                  },
                ],
              },
              {
                label: "讲者签到地址",
                prop: "speakerSignAddress",
                type: "input",
              },
              {
                label: "讲者签到时间",
                prop: "speakerSignDate",
                type: "input",
              },
              {
                label: "讲者签到经纬度",
                prop: "speakerSignLogLat",
                type: "input",
              },
              {
                label: "讲者签到签名",
                prop: "speakerSignSignature",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
            ],
          },
          {
            label: "评价信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "评价状态",
                prop: "evaluationStatus",
                type: "select",
                dicData: [
                  {
                    label: "待评价",
                    value: 0,
                  },
                  {
                    label: "已评价",
                    value: 1,
                  },
                ],
              },
              {
                label: "评价时间",
                prop: "evaluationDate",
                type: "input",
              },
              {
                label: "评价内容",
                prop: "evaluationInfo",
                type: "input",
                display: false,
              },
              {
                labelPosition: "top",
                label: "",
                prop: "evaluationInfo2",
                type: "input",
                display: false,
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    //去企业详情
    toView() {
      this.$router.push({
        path: `/entrustedcompany/detail/${this.form.entrustedCompanyId}/1`,
      });
    },
    //去会员详情
    toViewDoctor() {
      this.$router.push({
        path: `/authenticationDoctor/detail/${this.form.doctorId}/1`,
      });
    },
    // 打开新页面
    toopen(i) {
      window.open(i, "_blank");
    },
    downloadFile(url, type) {
      if (!url) {
        this.$message.error("文件为空无法下载");
        return;
      }
      if (type == 1) {
        url.split(",").forEach((item) => {
          downloadFileBlob(item, "活动议程表");
        });
      } else {
        downloadFileBlob(url, "讲者课件");
      }
    },
    preview(url) {
      if (url) {
        this.previewDialog = true;
        this.fileUrl = url;
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    handleClick() {},
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          if (
            res.data.data.evaluationTemplate != "null" &&
            JSON.parse(res.data.data.evaluationInfo)
          ) {
            this.option.group[4].column[2].display = false;
            this.option.group[4].column[3].display = true;
            this.evaluationInfo = JSON.parse(res.data.data.evaluationInfo);

            //projectType 1简答 2 单选 3评星
            this.evaluationInfo.forEach((item) => {
              //简答
              if (item.projectType == 2) {
                item.optionVOList.forEach((item2, index) => {
                  if (item2.isCheck == 1) item.radio = index;
                });
              }
            });
          } else {
            this.option.group[4].column[2].display = true;
          }
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    submit() {},
  },
};
</script>

<style scoped>
.evaluationInfo {
  margin-left: 120px;
}
.evaluationInfo-title {
  margin: 20px 0;
  display: flex;
  align-items: center;
}
</style>
