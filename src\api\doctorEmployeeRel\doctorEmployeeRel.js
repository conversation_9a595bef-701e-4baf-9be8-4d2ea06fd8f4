import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-csc/doctorEmployeeRel/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-csc/doctorEmployeeRel/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-csc/doctorEmployeeRel/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-csc/doctorEmployeeRel/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-csc/doctorEmployeeRel/submit',
    method: 'post',
    data: row
  })
}

