import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-csc/entrustedCompanyMember/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-csc/entrustedCompanyMember/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-csc/entrustedCompanyMember/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-csc/entrustedCompanyMember/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-csc/entrustedCompanyMember/submit",
    method: "post",
    data: row,
  });
};

// 获取人员管理列表
export const getEntrustedCompanyMemberList = (current, size, params) => {
  return request({
    url: "/api/blade-csc/entrustedCompanyMember/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

// 停用启用切换
export const enable = (id) => {
  return request({
    url: "/api/blade-csc/entrustedCompanyMember/enable",
    method: "post",
    params: {
      id,
    },
  });
};

//人员管理-新增人员
export const addMember = (row) => {
  return request({
    url: "/api/blade-csc/entrustedCompanyMember/saveMember",
    method: "post",
    data: row,
  });
};
//人员管理-修改人员
export const editMember = (row) => {
  return request({
    url: "/api/blade-csc/entrustedCompanyMember/update",
    method: "post",
    data: row,
  });
};
//人员管理-删除人员
export const delMember = (id) => {
  return request({
    url: "/api/blade-csc/entrustedCompanyMember/remove",
    method: "post",
    params: {
      id,
    },
  });
};
