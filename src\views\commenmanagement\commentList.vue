<template>
  <div class="comment-list" :class="{ indent: level === 1}">
    <template v-for="item in list">
      <div class="comment-card" >
        <div class="comment-header">
          <el-avatar :size="24">{{ (item.createUserName || '').charAt(0) }}</el-avatar>
          <div class="username">
            <span>{{ item.createUserName }}</span>
            <template v-if="parent">
              <i class="el-icon-caret-right"></i>
              <span>{{ parent.createUserName }}</span>
            </template>
          </div>
        </div>
        <div class="comment-content-container">
          <div class="comment-content">
            {{ item.content }}
            <template v-if="item.status == '1'">
              <el-tag style="margin: 0 5px" type="danger">已下架</el-tag>
              {{ item.downReason }}
            </template>
          </div>
          <div class="other-info">
            <div class="time">{{ item.createTime }}</div>
            <div class="like">
              <i class="el-icon-thumb"></i>{{ item.likeNum || 0 }}
            </div>
          </div>
        </div>
      </div>
      <comment-list :list="item.children" :level="level + 1" :parent="item"/>
    </template>
  </div>
</template>

<script>
import 'nprogress/nprogress.css';

export default {
    name: 'CommentList',
    props: {
      parent: {},
      list: {
        type: Array
      },
      level: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {};
    },
    computed: {
    },
    created() {},
    methods: {}
  };
</script>

<style>
.indent {
  margin-left: 32px;
}

.comment-card {
  margin-bottom: 12px;
}


.comment-header {
  display: flex;
  align-items: center;
}


.username {
  font-weight: bold;
  font-size: 14px;
  color: #999;
  margin-left: 12px;
}

.other-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
.like {
  align-items: center;
  gap: 8px;
  i { margin-right: 8px }
}

.comment-content-container {
  margin-left: 32px;
  color: #666;
  font-size: 14px;
  padding: 8px;
}
.comment-content {
  line-height: 1.5;
}
</style>
