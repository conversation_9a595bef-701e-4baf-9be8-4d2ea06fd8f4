export const option1 = {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  addBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  emptyBtnIcon: " ",
  selection: true,
  dialogClickModal: false,

  column: [
    {
      label: "平台名称",
      prop: "platformName",
      type: "input",
    },
    {
      label: "会员编码",
      prop: "code",
      type: "input",
    },
    {
      label: "会员姓名",
      prop: "name",
      type: "input",
      search: true,
    },
    {
      label: "证件类型",
      prop: "credentialType",
      type: "select",
      dataType: "number",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=credential_type",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
    },
    {
      label: "证件号码",
      prop: "idCardNumber",
      type: "input",
      search: true,
    },
    {
      label: "联系地址",
      prop: "idCardAddress",
      type: "input",
    },
    {
      label: "联系电话",
      prop: "phone",
      type: "input",
      search: true,
    },
    {
      label: "是否实名验证",
      prop: "approvalResult",
      type: "select",
      dicData: [
        {
          label: "待认证",
          value: 0,
        },
        {
          label: "已认证",
          value: 1,
        },
      ],
    },
    {
      label: "会员银行卡开户行",
      prop: "bankName",
      type: "input",
      width: 150,
    },
    {
      label: "会员银行卡号",
      prop: "bankAccount",
      type: "input",
    },
    {
      label: "注册时间",
      prop: "createTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
    },
    {
      label: "会员注销日期",
      prop: "cancellDate",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
    },
    {
      label: "审核时间",
      prop: "approvalDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
    },
  ],
};

export const option2 = {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  addBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  emptyBtnIcon: " ",
  selection: true,
  dialogClickModal: false,
  searchLabelWidth: 130,
  column: [
    {
      label: "平台名称",
      prop: "platformName",
      type: "input",
      width: 150,
    },
    {
      label: "会员编码",
      prop: "code",
      type: "input",
    },
    {
      label: "客户名称",
      prop: "name",
      type: "input",
      search: true,
    },
    {
      label: "统一社会信用代码",
      prop: "socialCreditCode",
      type: "input",
      width: 150,

      search: true,
    },
    {
      label: "法定代表人",
      prop: "lawPerson",
      type: "input",
    },
    {
      label: "法人身份证",
      prop: "lawPersonIdNum",
      type: "input",
    },
    {
      label: "成立时间",
      prop: "regDate",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
    },
    {
      label: "注册地址",
      prop: "regAddress",
      type: "input",
    },
    {
      label: "企业电话",
      prop: "telphone",
      type: "input",
    },
    {
      label: "银行卡开户行",
      prop: "bankName",
      type: "input",
      width: 120,
    },
    {
      label: "银行卡号",
      prop: "bankAccount",
      type: "input",
    },
    {
      label: "注册时间",
      prop: "createTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
    },
    {
      label: "会员注销日期",
      prop: "cancellDate",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      width: 120,
    },
    {
      label: "审核时间",
      prop: "auditTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
    },
  ],
};
