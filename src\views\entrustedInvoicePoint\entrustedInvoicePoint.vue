<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="confirmStatus" slot-scope="{ row }">
        <el-tag v-if="row.confirmStatus == 0" type="info" size="small">待提交 </el-tag>
        <el-tag v-if="row.confirmStatus == 1" size="small">待审核</el-tag>
        <el-tag v-if="row.confirmStatus == 2" type="success" size="small">通过</el-tag>
        <el-tag v-if="row.confirmStatus == 3" type="danger" size="small">驳回</el-tag>
      </template>
      <template slot="orderStatus" slot-scope="{ row }">
        <el-tag v-if="row.orderStatus == 1" type="info" size="small">已申请</el-tag>
        <el-tag v-if="row.orderStatus == 2" type="warning" size="small">开票中</el-tag>
        <el-tag v-if="row.orderStatus == 3" type="success" size="small">已完成</el-tag>
      </template>
      <template slot="menuLeft">
        <el-button
          v-if="permission.point_entrustedInvoice_entrustedInvoiceItem"
          type="primary"
          @click="toDetail"
          >查看结算单</el-button
        >
      </template>
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button
          :size="option.size"
          type="text"
          v-if="
            permission.point_entrustedInvoice_impor &&
            row.orderStatus != 3 && (row.confirmStatus == 0 || row.confirmStatus == 3)
          "
          @click="toEdit(row)"
          >上传发票</el-button
        >
        <el-button
          :size="option.size"
          type="text"
          v-if="permission.point_entrustedInvoice_view"
          @click="toView(row)"
          >查看</el-button
        >
        <el-button
          :size="option.size"
          :loading="submitStatus"
          type="text"
          v-if="permission.point_entrustedInvoice_submit && row.orderStatus == 2 && (row.confirmStatus == 0 || row.confirmStatus == 3)"
          @click="submit(row)"
          >提交发票</el-button
        >
        <el-button
          :size="option.size"
          type="text"
          v-if="
            permission.point_entrustedInvoice_delete &&
            row.orderStatus != 3 && row.confirmStatus == 0
          "
          @click="rowDel(row)"
          >删除</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove,
} from "@/api/entrustedInvoicePoint/entrustedInvoicePoint";
import option from "@/const/entrustedInvoice/entrustedInvoice";
import { mapGetters } from "vuex";
import { invoiceSubmit } from "../../api/entrustedInvoicePoint/entrustedInvoicePoint";

export default {
  data() {
    return {
      importShow: false,
      form: {},
      id: "",
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      option2: {
        submitBtn: false,
        emptyBtn: false,
        group: [
          {
            label: "发票信息",
            arrow: true,
            disabled: true,
            prop: "group2",
            column: [
              {
                label: "发票代码",
                prop: "invoiceDataCode",
                rules: [
                  {
                    required: true,
                    message: "请输入发票代码",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "发票号码",
                prop: "invoiceNumber",
                rules: [
                  {
                    required: true,
                    message: "请输入发票号码",
                    trigger: ["blur", "change"],
                  },
                ],
              },

              {
                label: "校验码",
                prop: "checkCode",
                rules: [
                  {
                    required: true,
                    message: "请输入效验码",
                    trigger: ["blur", "change"],
                  },
                ],
              },

              {
                label: "开票日期",
                prop: "billingTime",
                type: "date",
                format: "yyyy-MM-dd",
                valueFormat: "yyyy-MM-dd",
                rules: [
                  {
                    required: true,
                    message: "请选择开票日期",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "机器码",
                prop: "taxDiskCode",
                disabled: true,
                rules: [
                  {
                    required: true,
                    message: "机器码不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "发票类型不能为空",
                disabled: true,
                prop: "invoiceTypeName",
                rules: [
                  {
                    required: true,
                    message: "发票类型不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                disabled: true,
                label: "购方名称",
                prop: "purchaserName",
                rules: [
                  {
                    required: true,
                    message: "购方名称不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                disabled: true,
                label: "购方纳税人识别号",
                prop: "taxpayerNumber",
                rules: [
                  {
                    required: true,
                    message: "购方纳税人识别号不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                disabled: true,
                label: "购方开户行及账号",
                prop: "taxpayerBankAccount",
                rules: [
                  {
                    required: true,
                    message: "购方开户行及账号不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                disabled: true,
                label: "购方地址、电话",
                prop: "taxpayerAddressOrPhone",
                rules: [
                  {
                    required: true,
                    message: "购方地址、电话不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                disabled: true,
                label: "销方名称",
                prop: "salesName",
                rules: [
                  {
                    required: true,
                    message: "销方名称不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                disabled: true,
                label: "销方纳税人识别号",
                prop: "salesTaxpayerNum",
                rules: [
                  {
                    required: true,
                    message: "销方纳税人识别号不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                disabled: true,
                label: "销方开户行及账号",
                prop: "salesTaxpayerBankAccount",
                rules: [
                  {
                    required: true,
                    message: "销方开户行及账号不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                disabled: true,
                label: "销方地址、电话",
                prop: "salesTaxpayerAddress",
                rules: [
                  {
                    required: true,
                    message: "销方地址、电话不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                disabled: true,
                label: "金额",
                prop: "amount",
                append: "元",
                rules: [
                  {
                    required: true,
                    message: "金额不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                disabled: true,
                label: "税额",
                prop: "taxAmount",
                append: "元",
                rules: [
                  {
                    required: true,
                    message: "税额不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                disabled: true,
                label: "价税合计",
                prop: "totalTaxAmount",
                append: "元",
                rules: [
                  {
                    required: true,
                    message: "价税合计不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                span: 8,
                label: "附件",
                prop: "invoiceFileLink",
                type: "upload",
                listType: "picture-img",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                rules: [
                  {
                    required: true,
                    message: "附件不能为空",
                    trigger: ["blur", "change"],
                  },
                ],
              },
            ],
          },
        ],
      },
      data: [],
      submitStatus: false, //提交审核状态
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.point_entrustedInvoice_add, false),
        viewBtn: this.vaildData(this.permission.point_entrustedInvoice_view, false),
        delBtn: this.vaildData(this.permission.point_entrustedInvoice_delete, false),
        editBtn: this.vaildData(this.permission.point_entrustedInvoice_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    toDetail() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList.length > 1) {
        this.$message.error("只能选择一条数据");
        return;
      }
      if (this.selectionList[0].businessType == 1) {
        this.$router.push({
          path: `/settlementOrder/detail/${this.selectionList[0].settlementOrderId}`,
        });
      } else {
        this.$router.push({
          path: `/settlementOrderDoctorPoint/detail/${this.selectionList[0].settlementOrderId}`,
        });
      }

      // this.$router.push({
      //   path: `/entrustedInvoiceItem/entrustedInvoiceItem`,
      //   query: {
      //     id: this.ids
      //   }
      // });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },

    toView(row) {
      this.$router.push({
        path: `/entrustedInvoicePoint/detail/${row.id}`,
      });
    },
    toEdit(row) {
      this.$router.push({
        path: `/entrustedInvoicePoint/edit/${row.id}`,
      });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/entrustedInvoicePoint/detail/${this.form.id}`,
        });
      } else if (type == "edit") {
        this.$router.push({
          path: `/entrustedInvoicePoint/edit/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    //提交审核
    submit(row) {
      console.log(row);
      this.submitStatus = true;
      invoiceSubmit(row.id, 2).then(
        () => {
          this.submitStatus = false;
          this.refreshChange();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          console.log(error);
          this.submitStatus = false;
        }
      );
    },
  },
};
</script>

<style></style>
