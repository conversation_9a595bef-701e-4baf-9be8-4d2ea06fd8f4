export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  labelWidth: 150,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  selection: true,
  addBtn: false,
  dialogClickModal: false,
  searchLabelWidth: 130,
  column: [
    {
      label: "发票单编号",
      prop: "code",
      type: "input",
    },
    {
      label: "发票单名称",
      prop: "name",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入企业发票单名称",
          trigger: "blur",
        },
      ],
    },
    {
      label: "客户名称",
      prop: "entrustedCompanyName",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入客户名称",
          trigger: "blur",
        },
      ],
    },
    {
      label: "发票单金额",
      prop: "totalTaxAmount",
      type: "input",
      rules: [
        {
          required: true,
          message: "发票单金额",
          trigger: "blur",
        },
      ],
    },
    {
      label: "发票单状态",
      prop: "orderStatus",
      type: "select",
      search: true,
      dicUrl:
        "/api/blade-system/dict-biz/dictionary?code=entrusted_invoice_status",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      rules: [
        {
          required: true,
          message: "请选择发票单状态",
          trigger: "blur",
        },
      ],
    },
    {
      label: "验收状态",
      prop: "confirmStatus",
      type: "select",
      search: true,

      dicData: [
        {
          label: "待提交",
          value: 0,
        },
        {
          label: "待审核",
          value: 1,
        },
        {
          label: "通过",
          value: 2,
        },
        {
          label: "驳回",
          value: 3,
        },
      ],
      rules: [
        {
          required: true,
          message: "请选择验收状态",
          trigger: "blur",
        },
      ],
    },

    {
      label: "创建时间",
      prop: "createTime",
      type: "date",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
    },
  ],
};
