export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  labelWidth: 150,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "招募名称",
      search: true,
      prop: "tenderInfoName",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "企业名称",
      search: true,
      prop: "entrustedCompanyName",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "自然人名称",
      prop: "serviceProviderName",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },

    {
      label: "自然人手机号",
      prop: "serviceProviderPhone",
      search: true,
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "身份证号",
      prop: "idCardNumber",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },

    {
      label: "报名时间",
      prop: "bidTime",
      span: 12,
      type: "datetime",
      format: "yyyy-MM-dd HH:mm",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "报名备注",
      prop: "bidRemark",
      type: "input",
    },

    {
      label: "确认时间",
      prop: "bidWiningTime",
      span: 12,
      type: "datetime",
      format: "yyyy-MM-dd HH:mm",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      rules: [
        {
          required: true,
          message: "请输入",
          trigger: "blur",
        },
      ],
    },
    {
      label: "报名状态",
      prop: "bidStatus",
      type: "select",
      editDisabled: true, //编辑的时候不能修改
      addDisplay: false, //新增时是否显示
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=apply_status",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
    },
    {
      label: "确认备注",
      prop: "winingRemark",
      type: "input",
    },

    {
      label: "数据来源平台",
      prop: "platformSource",
      editDisabled: true, //编辑的时候不能修改
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=data_source_platform",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
