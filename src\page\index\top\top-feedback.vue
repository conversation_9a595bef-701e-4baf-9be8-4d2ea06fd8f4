<template>
  <div class="top-menu">
    <el-menu :default-active="activeIndex" mode="horizontal" text-color="#333">
      <el-menu-item index="0" @click.native="toFeedback()" key="0">
        <template slot="title">
          <i :class="itemHome.source"></i>
          <span>{{ generateTitle(itemHome) }}</span>
        </template>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "top-menu",
  data() {
    return {
      itemHome: {
        name: "意见反馈",
        source: "el-icon-edit-outline",
      },
      activeIndex: "0",
      items: [],
    };
  },
  inject: ["index"],
  created() {
    this.getMenu();
  },
  computed: {
    ...mapGetters(["tagCurrent", "menu"]),
  },
  methods: {
    toFeedback() {
      this.$router.push({
        path: `/feedback/feedbackAdd`,
      });
    },
    openMenu(item) {
      this.index.openMenu(item);
    },
    getMenu() {
      this.$store.dispatch("GetTopMenu").then((res) => {
        this.items = res;
      });
    },
    generateTitle(item) {
      return this.$router.$avueRouter.generateTitle(
        item.name,
        (item.meta || {}).i18n
      );
    },
  },
};
</script>
