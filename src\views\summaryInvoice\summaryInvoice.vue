<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="invoiceStatus" slot-scope="{ row }">
        <el-tag v-if="row.invoiceStatus == 1" type="info" size="small"
          >待开票</el-tag
        >
        <el-tag v-if="row.invoiceStatus == 2" type="success" size="small"
          >已开票</el-tag
        >
      </template>

      <template slot="menu" slot-scope="scope">
        <el-button
          v-if="
            permission.summaryInvoice_delete && scope.row.invoiceStatus == 1
          "
          :type="scope.type"
          :size="scope.size"
          @click="$refs.crud.rowDel(scope.row)"
          >删除</el-button
        >
        <el-button
          v-if="permission.summaryInvoice_summaryInvoiceDetail"
          :type="scope.type"
          :size="scope.size"
          @click="toDetail(scope.row)"
          >发票明细</el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove,
  summaryInvoiceUpload,
} from "@/api/summaryInvoice/summaryInvoice";
import option from "@/const/summaryInvoice/summaryInvoice";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      dialogVisible: false,
      invoiceId: "",
      invoiceForm: {
        invoiceCode: "",
        invoiceDate: "",
        invoiceUrl: "",
        id: "",
      },
      option2: {
        submitBtn: true,
        emptyBtn: false,
        // disabled: true,
        column: [
          {
            label: "发票代码",
            prop: "invoiceCode",
            rules: [
              {
                required: true,
                message: "请选择发票代码",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "开票日期",
            prop: "invoiceDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
            rules: [
              {
                required: true,
                message: "请选择开票日期",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "发票附件",
            prop: "invoiceUrl",
            type: "upload",
            listType: "picture-card",
            limit: 1,
            dataType: "string",
            action: "/api/blade-resource/oss/endpoint/put-file",
            tip: "建议文件大小不超过2M，png格式",
            propsHttp: {
              res: "data",
              url: "link",
            },
            rules: [
              {
                required: true,
                message: "请上传发票附件，建议大小在2M内，png格式",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },

      formRule: {
        invoiceCode: [
          {
            required: true,
            message: "发票代码不能为空",
            trigger: ["blur", "change"],
          },
        ],
        invoiceDate: [
          {
            required: true,
            message: "开票日期不能为空",
            trigger: ["blur", "change"],
          },
        ],
      },
      form: {},
      form2: [],
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      loading2: true,
      page2: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,

      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.summaryInvoice_add, false),
        viewBtn: this.vaildData(this.permission.summaryInvoice_view, false),
        delBtn: this.vaildData(this.permission.summaryInvoice_delete, false),
        editBtn: this.vaildData(this.permission.summaryInvoice_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    toDetail(row) {
      this.$router.push({
        path: `/summaryInvoicedetail/summaryInvoiceDetail`,
        query: {
          id: row.id,
        },
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    open(row) {
      this.dialogVisible = true;
      this.invoiceId = row.id;
    },
    uploadExceed() {
      this.$message.error("每次只能上传一个文件，请先移除再上传");
    },

    submit() {
      this.invoiceForm.id = this.invoiceId;
      summaryInvoiceUpload(this.invoiceForm).then((res) => {
        let data = res.data;
        if (data.success) {
          this.onLoad(this.page);
          this.dialogVisible = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        }
      });
    },

    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/summaryInvoice/detail/${this.form.id}`,
        });
      } else if (type == "edit") {
        this.$router.push({
          path: `/summaryInvoice/edit/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    refreshChange2() {
      this.page.currentPage = 1;
      this.getList2();
    },
    currentChange2(currentPage) {
      this.page2.currentPage = currentPage;
      this.getList2();
    },
    sizeChange2(pageSize) {
      this.page2.pageSize = pageSize;
      this.getList2();
    },
    searchReset2() {
      this.getList2();
    },
    searchChange2(params, done) {
      this.page2.currentPage = 1;
      this.getList2(params);
      done();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
