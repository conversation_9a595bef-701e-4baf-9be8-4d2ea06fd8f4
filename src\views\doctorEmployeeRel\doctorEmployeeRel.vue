<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
    </avue-crud>
    <el-dialog
      title="查看"
      append-to-body
      :visible.sync="viewDialogVisible"
      width="60%"
    >
      <avue-form :option="viewDialogOption" v-model="viewDialogForm">
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from "@/api/doctorEmployeeRel/doctorEmployeeRel";
import option from "@/const/doctorEmployeeRel/doctorEmployeeRel";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      //查看
      viewDialogVisible: false,
      viewDialogForm: {},
      viewDialogOption: {
        disabled: true,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: "120",
        column: [
          {
            label: "会员姓名",
            prop: "doctorName",
            type: "input",
            search: true,
            addDisplay: false,
            editDisplay: false,
          },

          {
            label: "代表姓名",
            prop: "name",
            search: true,
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入代表姓名",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "手机号码",
            prop: "phone",
            type: "input",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入手机号码",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "代表部门",
            prop: "dept",
            type: "tree",
            dicUrl: "/api/blade-csc/entrustedDept/tree",
            hide: true,
            props: {
              label: "title",
              value: "id",
            },
            rules: [
              {
                required: true,
                message: "请输入所在部门",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "产品名称",
            prop: "baseProductName",
            type: "textarea",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入产品名称",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },
      //end
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.doctorEmployeeRel_add, false),
        viewBtn: this.vaildData(this.permission.doctorEmployeeRel_view, false),
        delBtn: this.vaildData(this.permission.doctorEmployeeRel_delete, false),
        editBtn: this.vaildData(this.permission.doctorEmployeeRel_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        getDetail(this.form.id).then((res) => {
          this.viewDialogVisible = true;
          this.viewDialogForm = res.data.data;
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
