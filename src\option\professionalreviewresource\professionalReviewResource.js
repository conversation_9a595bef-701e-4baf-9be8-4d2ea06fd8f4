export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "资源编号",
      prop: "code",
      type: "input",
    },
    {
      label: "标题/观点/文题",
      prop: "title",
      type: "input",
    },
    {
      label: "资源类型（1文章-默认）",
      prop: "type",
      type: "input",
    },
    {
      label: "资源路径",
      prop: "path",
      type: "input",
    },
    {
      label: "作者姓名",
      prop: "authorName",
      type: "input",
    },
    {
      label: "作者单位",
      prop: "authorUnitName",
      type: "input",
    },
    {
      label: "创作提交时间",
      prop: "submitTime",
      type: "input",
    },
    {
      label: "评审使用次数",
      prop: "usesNumber",
      type: "input",
    },
    {
      label: "使用医师id",
      prop: "usesDoctorId",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态 ",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
    },
  ]
}
