export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  labelWidth: 110,
  searchLabelWidth: 130,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "问卷题目编码",
      prop: "attrCode",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "问卷题目名称",
      prop: "attrName",
      searchLabelWidth: 105,
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入问卷题目名称",
          trigger: ["blur", "change"],
        },
      ],
      span: 24,
      row: true,
    },
    {
      label: "问卷题目分类",
      prop: "attrClassifyKey",
      type: "select",
      search: true,
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=attr_classify",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      rules: [
        {
          required: true,
          message: "请选择题目分类",
          trigger: "blur",
        },
      ],
    },
    {
      label: "问卷题目类型",
      prop: "attrType",
      type: "select",
      rules: [
        {
          required: true,
          message: "请选择问卷题目类型",
          trigger: ["blur", "change"],
        },
      ],
      dicData: [
        {
          label: "单选",
          value: 1,
        },
        {
          label: "多选",
          value: 2,
        },
        {
          label: "文本",
          value: 3,
        },
      ],
    },
    {
      label: "问卷题目选项",
      prop: "attrValue",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入问卷题目选项",
          trigger: ["blur"],
        },
      ],
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态 ",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
