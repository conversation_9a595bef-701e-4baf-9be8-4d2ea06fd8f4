export default {
  title: "元圈",
  logoutTip: "Exit the system, do you want to continue?",
  submitText: "submit",
  cancelText: "cancel",
  search: "Please input search content",
  menuTip: "none menu list",
  common: {
    condition: "condition",
    display: "display",
    hide: "hide",
  },
  tip: {
    select: "Please select",
    input: "Please input",
  },
  upload: {
    upload: "upload",
    tip: "Drag files here，/",
  },
  date: {
    start: "Start date",
    end: "End date",
    t: "today",
    y: "yesterday",
    n: "nearly 7",
    a: "whole",
  },
  form: {
    printBtn: "print",
    mockBtn: "mock",
    submitBtn: "submit",
    emptyBtn: "empty",
  },
  crud: {
    excel: {
      name: "File Name",
      type: "Data",
      typeDic: {
        true: "Data on this page (all of the data on this page)",
        false: "Selected Data (the selected data on this page)",
      },
      prop: "Field",
      params: "Parameters",
      paramsDic: {
        header: "Table Header",
        data: "Data Source",
        headers: "Complex Table Header",
        sum: "Total",
      },
    },
    filter: {
      addBtn: "Add",
      clearBtn: "Clear",
      resetBtn: "Reset",
      cancelBtn: "Cancel",
      submitBtn: "Submit",
    },
    column: {
      name: "Name",
      hide: "Hide",
      fixed: "Fixed",
      filters: "Filter",
      sortable: "Sort",
      index: "Index",
      width: "Width",
    },
    emptyText: "No Data",
    tipStartTitle: "Selected",
    tipEndTitle: "Items　",
    editTitle: "Edit",
    copyTitle: "Copy",
    addTitle: "Add",
    viewTitle: "View",
    filterTitle: "Filter Conditions",
    showTitle: "Column Display",
    menu: "Menu",
    addBtn: "Add",
    show: "Show",
    hide: "Hide",
    open: "Open",
    shrink: "Shrink",
    printBtn: "Print",
    excelBtn: "Export",
    updateBtn: "Update",
    cancelBtn: "Cancel",
    searchBtn: "Search",
    emptyBtn: "Empty",
    menuBtn: "Menu",
    saveBtn: "Save",
    viewBtn: "View",
    editBtn: "Edit",
    copyBtn: "Copy",
    delBtn: "Delete",
  },
  login: {
    title: "Login ",
    info: "BladeX Development Platform",
    tenantId: "Please input tenantId",
    username: "Please input username",
    password: "Please input a password",
    wechat: "Wechat",
    qq: "QQ",
    github: "github",
    gitee: "gitee",
    phone: "Please input a phone",
    code: "Please input a code",
    submit: "Login",
    userLogin: "userLogin",
    phoneLogin: "phoneLogin",
    thirdLogin: "thirdLogin",
    ssoLogin: "ssoLogin",
    msgText: "send code",
    msgSuccess: "reissued code",
  },
  navbar: {
    info: "info",
    logOut: "logout",
    userinfo: "userinfo",
    switchDept: "switch dept",
    dashboard: "dashboard",
    lock: "lock",
    bug: "none bug",
    bugs: "bug",
    screenfullF: "exit screenfull",
    screenfull: "screenfull",
    language: "language",
    notice: "notice",
    theme: "theme",
    color: "color",
  },
  tagsView: {
    search: "Search",
    menu: "menu",
    clearCache: "Clear Cache",
    closeOthers: "Close Others",
    closeAll: "Close All",
  },
};
