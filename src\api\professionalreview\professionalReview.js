import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/professionalReview/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getPointList = (current, size, params) => {
  return request({
    url: "/api/blade-act/professionalReview/pointList",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/professionalReview/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/professionalReview/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/professionalReview/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/professionalReview/submit",
    method: "post",
    data: row,
  });
};

//开始结束计划
export const updateStatus = (id, status) => {
  return request({
    url: "/api/blade-act/professionalReview/updateStatus",
    method: "get",
    params: {
      id,
      status,
    },
  });
};

//重发合同
export const startPlanAgain = (id) => {
  return request({
    url: "/api/blade-act/professionalReview/startPlanAgain",
    method: "get",
    params: {
      id,
    },
  });
};

//获取已经邀请会员
export const getDoctorList = (current, size, params) => {
  return request({
    url: "/api/blade-act/professionalReviewDoctor/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
