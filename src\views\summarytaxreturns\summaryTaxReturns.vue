<template>
  <basic-container>
    <avue-crud
      :option="option"
      :search.sync="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="taxCertStatus" slot-scope="{ row }">
        <el-tag v-if="row.taxCertStatus == 1" type="info" size="small"
          >未完税</el-tag
        >
        <el-tag v-if="row.taxCertStatus == 2" type="success" size="small"
          >已完税</el-tag
        >
      </template>
      <template slot="menuLeft">
        <el-button
          type="primary"
          v-if="permission.summaryTaxReturns_createSummaryTaxReturns"
          @click="createSummaryTaxReturns()"
          >生成月度纳税申报单</el-button
        >
      </template>
      <template slot-scope="{ row }" slot="menu">
        <el-button
          v-if="permission.summaryTaxReturns_edit && row.taxCertStatus == 1"
          :size="option.size"
          type="text"
          @click="rowDel(row)"
          >删除</el-button
        >
        <el-button
          type="text"
          v-if="permission.summaryTaxReturns_edit && row.taxCertStatus == 1"
          @click="open(row)"
          >上传完税证明</el-button
        >
      </template>
    </avue-crud>
    <el-dialog
      title="上传凭证"
      :visible.sync="uploadDialogVisible"
      append-to-body
      width="30%"
    >
      <avue-form
        :option="uploadOption"
        v-model="uploadForm"
        :upload-exceed="uploadExceed"
      >
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="updateTaxCertStatus()"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  createSummaryTaxReturns,
  updateTaxCertStatus,
} from "@/api/summarytaxreturns/summaryTaxReturns";
import option from "@/const/summarytaxreturns/summaryTaxReturns";
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from "nprogress";
import "nprogress/nprogress.css";

export default {
  data() {
    return {
      //上传
      runningWaterId: "",
      uploadDialogVisible: false,
      uploadForm: { taxCertFile: "" },
      uploadOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "凭证",
            prop: "taxCertFile",
            type: "upload",
            drag: true,
            loadText: "凭证上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
              url: "link",
            },
            limit: 5,
            listType: "picture-card",
            accept: "image/png, image/jpeg",
            tip: "请上传jpg/png格式文件",
            action: "/api/blade-resource/oss/endpoint/put-file",
          },
        ],
      },
      //end
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.summaryTaxReturns_add, false),
        viewBtn: this.vaildData(this.permission.summaryTaxReturns_view, false),
        delBtn: this.vaildData(this.permission.summaryTaxReturns_delete, false),
        editBtn: this.vaildData(this.permission.summaryTaxReturns_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    createSummaryTaxReturns() {
      createSummaryTaxReturns().then((res) => {
        let data = res.data;
        if (data.success) {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        }
      });
    },
    //上传完税证明
    open(row) {
      console.log(row);
      this.runningWaterId = row.id;
      this.uploadForm.taxCertFile = row.taxCertFile;
      this.uploadDialogVisible = true;
    },
    updateTaxCertStatus() {
      this.uploadForm.id = this.runningWaterId;
      if (this.uploadForm.taxCertFile instanceof Array) {
        this.uploadForm.taxCertFile = this.uploadForm.taxCertFile.join(",");
      }
      updateTaxCertStatus(this.uploadForm).then(
        () => {
          this.onLoad(this.page);
          this.uploadDialogVisible = false;
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleExport(data) {
      let downloadUrl = `/api/blade-svc/summaryTaxReturnsDetail/export?${
        this.website.tokenHeader
      }=${getToken()}&summaryTaxReturnsId=${data.id}`;

      let values = {};
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `月度纳税申报单明细${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/summarytaxreturns/detail/${this.form.id}`,
        });
      } else if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;

      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
