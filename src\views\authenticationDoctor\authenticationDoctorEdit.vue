<template>
  <div>
    <basic-container
      ><el-page-header @back="goBack" :content="$route.name"></el-page-header
    ></basic-container>
    <basic-container>
      <!--基本信息-->
      <avue-form
        ref="form1"
        :option="baseOption"
        v-model="form1"
        @submit="submit"
        v-if="!validatenull(form1)"
      />
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
  </div>
</template>

<script>
import {
  getDetail,
  update,
} from "@/api/authenticationDoctor/authenticationDoctor";
export default {
  data() {
    return {
      id: "",
      query: {},
      loading: false,
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      form1: {},
      baseOption: {
        disabled: false,
        labelWidth: "170",
        submitIcon: " ",
        emptyIcon: " ",
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",
            column: [
              {
                label: "姓名",
                prop: "name",
                disabled: true,
                rules: [
                  {
                    required: true,
                    message: "请输入姓名",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "医院名称",
                prop: "hospitalName",
                disabled: true,
                rules: [
                  {
                    required: true,
                    message: "请输入医院名称",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "所在科室",
                prop: "departmentName",
                disabled: true,
                rules: [
                  {
                    required: true,
                    message: "请输入所在科室",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "证件号码",
                prop: "idCardNumber",
                disabled: true,
                rules: [
                  {
                    required: true,
                    message: "请输入证件号码",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "手机号码",
                prop: "phone",
                disabled: true,
                rules: [
                  {
                    required: true,
                    message: "请输入手机号码",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "微信ID",
                prop: "wechatId",
                disabled: true,
              },
              {
                label: "职称",
                prop: "professional",
                disabled: true,
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=professional",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                rules: [
                  {
                    required: true,
                    message: "请选择职称",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "职务",
                prop: "duty",
                disabled: true,
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=duty",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                rules: [
                  {
                    required: true,
                    message: "请选择职务",
                    trigger: ["blur", "change"],
                  },
                ],
              },

              {
                label: "性别",
                prop: "sex",
                type: "select",
                dicUrl: "/api/blade-system/dict-biz/dictionary?code=sex",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },
              {
                label: "民族",
                prop: "nationality",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=nationality",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
              },
              {
                label: "籍贯",
                prop: "nativePlace",
                row: true,
              },
              {
                label: "所属省",
                prop: "provinceCode",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["cityCode"],
                dicUrl: "/api/blade-system/region/select",
                span: 6,
              },
              {
                label: "所属市",
                prop: "cityCode",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                cascader: ["districtCode"],
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 6,
              },
              {
                label: "所属区",
                prop: "districtCode",
                type: "select",
                props: {
                  label: "name",
                  value: "code",
                },
                dicFlag: false,
                dicUrl: "/api/blade-system/region/select?code={{key}}",
                span: 6,
              },
              {
                label: "联系地址",
                prop: "address",
                maxlength: 100,
                showWordLimit: true,
              },
              {
                label: "毕业院校",
                prop: "graduateSchool",
                maxlength: 100,
                showWordLimit: true,
              },
              {
                label: "最高学历",
                prop: "education",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=education_state",
                props: {
                  label: "dictValue",
                  value: "dictValue",
                },
              },
              {
                label: "主治专长",
                prop: "speciality",
                type: "textarea",
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  methods: {
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form1 = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    submit(form, done) {
      this.$refs.form1.validate((valid) => {
        if (valid) {
          form.provinceCode = this.form1.provinceCode;
          form.cityCode = this.form1.cityCode;
          form.districtCode = this.form1.districtCode;
          form.province = this.form1.$provinceCode;
          form.city = this.form1.$cityCode;
          form.district = this.form1.$districtCode;
          form.nationality = this.form1.$nationality;
          form.sex = this.form1.$sex;
          form.professional = this.form1.$professional;
          form.duty = this.form1.$duty;
          update(form).then(
            () => {
              this.goBack();
              this.$message({
                type: "success",
                message: "更新成功!",
              });
              done();
            },
            (error) => {
              console.log(error);
            }
          );
        }
      });
    },
    openSignature() {},
  },
};
</script>

<style>
.avue-crud__menu {
  min-height: 0 !important;
}

h4 {
  margin: 5px 0 !important;
}

.first-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
</style>
