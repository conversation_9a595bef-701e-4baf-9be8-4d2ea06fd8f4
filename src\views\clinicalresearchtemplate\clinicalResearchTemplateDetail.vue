<template>
  <div>
    <basic-container>
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <avue-form ref="form" :option="option" v-model="form">
        <template slot-scope="{ type, disabled }" slot="issueList">
          <div>
            <div class="title">已关联的临床调研问卷题目</div>

            <div style="width: 95%; margin: 0 auto">
              <avue-crud :option="topicOption" :data="selectPreinstallList">
                <template slot="attrType" slot-scope="{ row }">
                  <el-tag v-if="row.attrType == 1" type="warning" size="small"
                    >单选</el-tag
                  >
                  <el-tag v-if="row.attrType == 2" type="success" size="small"
                    >多选</el-tag
                  >
                  <el-tag v-if="row.attrType == 3" size="small">文本</el-tag>
                  <el-tag v-if="row.attrType == 4" size="small">图片</el-tag>
                </template>
                <template slot="requiredStatus" slot-scope="{ row }">
                  <el-tag
                    v-if="row.requiredStatus == 1"
                    type="success"
                    size="small"
                    >是</el-tag
                  >
                  <el-tag
                    v-if="row.requiredStatus == 0"
                    type="info"
                    size="small"
                    >否</el-tag
                  >
                </template>
              </avue-crud>
            </div>
          </div>
        </template>
      </avue-form>
    </basic-container>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getDetail } from "@/api/clinicalresearchtemplate/clinicalResearchTemplate";
export default {
  data() {
    return {
      //关联产品名称
      selectOption: {}, //选中的对象
      options: [],
      selectLoading: false,
      //选中的问题列表
      selectPreinstallList: [],
      idList: [],
      //预设问题
      preinstallList: [],
      id: "",
      //基础信息
      option: {
        labelWidth: "150",
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        column: [
          {
            label: "企业名称",
            prop: "entrustedCompanyName",
          },
          // {
          //   label: "对应产品名称",
          //   prop: "templateProductName",
          //   type: "input",
          //   rules: [
          //     {
          //       required: true,
          //       message: "请选择对应产品名称",
          //       trigger: ["blur", "change"],
          //     },
          //   ],
          // },

          {
            label: "模版名称/标题",
            prop: "templateName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入模版名称/标题",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "服务项目名称",
            prop: "projectTypeName",
          },
          {
            label: "价格",
            prop: "unitPrice",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入价格",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            span: 24,
            labelPosition: "top",
            label: "",
            prop: "issueList",
            type: "input",
          },
        ],
      },
      form: {},
      data: [],
      topicOption: {
        height: "400",
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        refreshBtn: false,
        columnBtn: false,
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        column: [
          {
            label: "问卷题目",
            prop: "attrName",
          },
          {
            label: "题目分类",
            prop: "attrClassifyKey",
            type: "select",
            dicUrl: "/api/blade-system/dict-biz/dictionary?code=attr_classify",
            props: {
              label: "dictValue",
              value: "dictKey",
            },
            dataType: "number",
            rules: [
              {
                required: true,
                message: "请选择题目分类",
                trigger: "blur",
              },
            ],
          },
          {
            label: "题目类型",
            prop: "attrType",
            type: "select",
            dicData: [
              {
                label: "单选",
                value: 1,
              },
              {
                label: "多选",
                value: 2,
              },
              {
                label: "文本",
                value: 3,
              },
            ],
          },
          {
            label: "作答选项",
            prop: "attrValue",
            type: "input",
          },
          {
            label: "是否必填",
            prop: "requiredStatus",
            type: "switch",
            dicData: [
              {
                label: "否",
                value: 0,
              },
              {
                label: "是",
                value: 1,
              },
            ],
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  methods: {
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
          _this.form.unitPrice=_this.form.unitPrice==null?'':Number(_this.form.unitPrice).toFixed(0)
          _this.form.projectTypeName = "临床调研";
          this.selectPreinstallList = JSON.parse(
            JSON.stringify(res.data.data.attrVOList)
          );
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped lang="scss">
.title {
  margin: 15px 30px;
  font-size: 18px;
  font-weight: 600;
}
</style>
