<template>
  <div>
    <basic-container
      ><el-page-header @back="goBack" :content="$route.name"></el-page-header
    ></basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form1)"
        ref="form1"
        :option="option1"
        v-model="form1"
      />
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/feedback/feedback";
// import { getPersonalServiceProviders } from "@/api/csc/personalserviceprovider";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      form1: {},
      option1: {
        disabled: true,
        labelWidth: "170",
        submitBtn: false,
        emptyBtn: false,
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",

            column: [
              {
                label: "意见反馈类型",
                search: true,
                prop: "type",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=feedback_type",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
              },
              {
                label: "客户端类型",
                prop: "clientType",
                type: "input",
                search: true,
              },
              // {
              //   label: "反馈用户",
              //   prop: "userId",
              //   type: "input",
              // },
              {
                label: "联系人姓名",
                prop: "name",
                type: "input",
                search: false,
              },
              {
                label: "联系电话",
                prop: "phone",
                type: "input",
                search: true,
              },
              {
                label: "备用电话",
                prop: "alternativePhone",
                type: "input",
                search: true,
              },
              {
                label: "内容",
                prop: "content",
                type: "textarea",
              },
              {
                label: "反馈材料",
                prop: "feedbackFileUrl",
                type: "upload",
                span: 24,
                listType: "picture-card",
                accept: "image/png, image/jpeg, video/mp4",
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form1 = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
</style>
