export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  delBtn: false,
  editBtn: false,
  addBtn: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  border: true,
  index: true,
  viewBtn: true,
  menu: false,
  selection: true,
  dialogClickModal: false,
  labelWidth: "120",
  searchLabelWidth: "120",
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "会员id",
      prop: "doctorId",
      type: "input",
      hide: true,
      viewDisplay: false,
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "会员姓名",
      prop: "doctorName",
      type: "input",
      search: true,
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "合作代表id",
      prop: "employeeId",
      type: "input",
      hide: true,
    },
    {
      label: "代表姓名",
      prop: "name",
      search: true,
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入合作代表姓名",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "手机号码",
      prop: "phone",
      type: "input",
      disabled: true,
      hide: true,
      rules: [
        {
          required: true,
          message: "请输入手机号码",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "代表部门",
      prop: "baseDepartmentName",
      disabled: true,
      type: "tree",
      dicUrl: "/api/blade-csc/entrustedDept/tree",
      addDisabled: false,
      props: {
        label: "title",
        value: "id",
      },
      rules: [
        {
          required: true,
          message: "请输入所在部门",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "产品名称",
      prop: "baseProductName",
      type: "textarea",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入产品名称",
          trigger: ["blur", "change"],
        },
      ],
    },
  ],
};
