export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuPosition: "right",
  border: true,
  index: true,
  addBtn: false,
  viewBtn: true,
  viewBtnIcon: " ",
  editBtn: false,
  delBtn: false,
  selection: true,
  dialogClickModal: false,
  menuWidth: 140,
  searchLabelWidth: "130",
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "病例征集计划id",
      prop: "caseCollectionId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "客户名称id",
      prop: "entrustedCompanyId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "提交编码",
      prop: "codeNumber",
      type: "input",
      width: "140",
      overHidden: true,
    },
    {
      label: "活动编号",
      prop: "code",
      type: "input",
      search: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "支付方式",
      prop: "payType",
      type: "select",
      search: true,
      searchOrder: 1,
      dicData: [
        {
          label: "积分",
          value: 1,
        },
        {
          label: "非积分",
          value: 2,
        },
      ],
      width: "120",
      overHidden: true,
    },
    {
      label: "统一社会信用代码",
      prop: "socialCreditCode",
      type: "input",
      // search: true,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "业务人员ID",
      prop: "baseEmployeeId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "认证会员ID",
      prop: "doctorId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "会员姓名",
      prop: "doctorName",
      type: "input",
      search: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "产品名",
      prop: "baseProductName",
      type: "input",
      search: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "产品线",
      prop: "baseProductLine",
      type: "input",
      width: "100",
      overHidden: true,
    },
    {
      label: "会员手机号",
      prop: "phone",
      type: "input",
      hide: true,
      // search: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "会员身份证",
      prop: "idCardNumber",
      type: "input",
      hide: true,
      // search: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "所属医院",
      prop: "hospitalName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      // search: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "所属科室",
      prop: "departmentName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "活动负责人",
      prop: "baseEmployeeName",
      type: "input",
      search: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "提交方式",
      prop: "submitType",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "模板id",
      prop: "templateId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "模板收集数据",
      prop: "collectData",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "业务人员工号",
      prop: "baseEmployeeNumber",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "负责人部门",
      prop: "baseDepartmentName",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      // hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "客户名称",
      prop: "entrustedCompanyName",
      type: "input",
      span: 24,
      width: "120",
      overHidden: true,
    },
    {
      label: "提交时间",
      prop: "submitTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "初审时间",
      prop: "approvalDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "初审人",
      prop: "approvalOfficer",
      type: "input",
      search: true,

      overHidden: true,
    },
    {
      fixed: "right",
      label: "初审状态",
      prop: "approvalStatus",
      type: "select",
      search: true,
      searchOrder: 3,
      dicData: [
        {
          label: "待审核",
          value: 0,
        },
        {
          label: "通过",
          value: 1,
        },
        {
          label: "驳回",
          value: 2,
        },
      ],
      width: "120",
      overHidden: true,
    },
    {
      label: "复审时间",
      prop: "confirmDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      width: "120",
      overHidden: true,
    },
    {
      label: "复审人",
      prop: "confirmer",
      type: "input",
      search: true,

      overHidden: true,
    },
    {
      fixed: "right",
      label: "复审状态",
      prop: "confirmStatus",
      type: "select",
      search: true,
      searchOrder: 2,
      dicData: [
        {
          label: "待审核",
          value: 0,
        },
        {
          label: "通过",
          value: 1,
        },
        {
          label: "驳回",
          value: 2,
        },
      ],
      width: "120",
      overHidden: true,
    },
    {
      label: "业务部门",
      prop: "baseDepartment",
      search: true,
      type: "tree",
      hide: true,
      dicData: [],
      multiple: true,
      checkStrictly: true,
      leafOnly: false,
      addDisabled: false,
      props: {
        label: "title",
        value: "id",
        width: "120",
        overHidden: true,
      },
      width: "120",
      overHidden: true,
    },
    {
      label: "验收说明",
      prop: "confirmResult",
      type: "input",
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      fixed: "right",
      label: "是否元圈复审",
      prop: "isOldData",
      type: "select",
      search: true,
      dicData: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },  
      ],
      hide: true,
    },
  ],
};
