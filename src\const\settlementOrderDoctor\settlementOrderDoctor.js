export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  labelWidth: 150,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  selection: true,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  searchLabelWidth: 130,
  column: [
    {
      label: "结算单编号",
      prop: "code",
      type: "input",
      search: true,
      width: "140",
    },
    {
      label: "客户成果单编号",
      prop: "resultOrderCode",
      type: "input",
      search: true,
      width: "140",
    },
    {
      label: "客户名称",
      prop: "entrustedCompanyName",
      type: "input",
      search: true,
    },
    // {
    //   label: "结算单名称",
    //   prop: "name",
    //   type: "input",
    //   search: false,
    // },
    {
      label: "服务结算期间",
      prop: "accountPeriod",
    },
    {
      label: "服务开始日期",
      prop: "actStartTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: false,
      searchRange: true,
    },
    {
      label: "服务结束日期",
      prop: "actEndTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: false,
      searchRange: true,
    },
    {
      label: "结算单总金额",
      prop: "settlementTotalAmount",
      type: "input",
      width: "165",
    },
    {
      label: "服务结算时间",
      prop: "createTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: false,
      searchRange: true,
    },
    // {
    //   label: "审核时间",
    //   prop: "approvalDate",
    //   type: "datetime",
    //   format: "yyyy-MM-dd HH:mm:ss",
    //   valueFormat: "yyyy-MM-dd HH:mm:ss",
    //   search: false,
    //   searchRange: true,
    // },
    {
      label: "推送状态",
      prop: "pushStatus",
      type: "select",
      dicData: [
        {
          label: "待推送",
          value: 0,
        },
        {
          label: "已推送",
          value: 1,
        },
      ],
    },
    {
      label: "确认状态",
      prop: "approvalStatus",
      type: "select",
      dicData: [
        {
          label: "待确认",
          value: 0,
        },
        {
          label: "已确认",
          value: 1,
        },
      ],
    },
    {
      label: "验收说明",
      prop: "approvalRemark",
      type: "input",
      width: "165",
    },
  ],
};
