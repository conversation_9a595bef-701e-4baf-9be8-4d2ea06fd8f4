export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "活动id",
      prop: "lectureId",
      type: "input",
    },
    {
      label: "用户id",
      prop: "userId",
      type: "input",
    },
    {
      label: "平台会员id （认证医师id）",
      prop: "doctorId",
      type: "input",
    },
    {
      label: "会员姓名",
      prop: "doctorName",
      type: "input",
    },
    {
      label: "工作单位",
      prop: "hospitalName",
      type: "input",
    },
    {
      label: "部门",
      prop: "departmentName",
      type: "input",
    },
    {
      label: "职务",
      prop: "duty",
      type: "input",
    },
    {
      label: "联系电话",
      prop: "phone",
      type: "input",
    },
    {
      label: "讲者级别 （文本SFE传入）",
      prop: "doctorLevel",
      type: "input",
    },
    {
      label: "讲课费用（结算劳务费）",
      prop: "doctorFee",
      type: "input",
    },
    {
      label: "讲课主题",
      prop: "topic",
      type: "input",
    },
    {
      label: "讲者协议（亿辰与讲者）",
      prop: "doctorAgreement",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态 ",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
    },
  ],
};
