import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-points/getAccountPage',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetailList = (current, size, params) => {
  return request({
    url: '/api/blade-points/getYuanbaoDetailPage',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


