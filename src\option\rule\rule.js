import {baseUrl} from "@/config/env";

const typeDicData =  [
  {value: 0, label: '开通账号',},
  {value: 1, label: '账号认证',},
  {value: 2, label: '发布内容',},
  {value: 3, label: '发布评论',},
  {value: 4, label: '浏览内容',},
  {value: 5, label: '分享内容',},
  {value: 6, label: '点赞内容',},
]

export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  selection: true,
  dialogClickModal: false,
  typeDicData,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "会员动作",
      prop: "type",
      type: "select",
      dicData: typeDicData,
      search: true,
      hide: true,
    },
    {
      label: "规则状态",
      prop: "status1",
      type: "select",
        dicData: [
            {
                label: "生效",
                value: 0,
            },
            {
                label: "未生效",
                value: 1,
            }
        ],
        search: true,
        hide: true,
    },
    {
      label: "会员动作",
      prop: "typeName",
      type: "input",
    },
    {
      label: "获取学识数量",
      prop: "pointNum",
      type: "input",
    },
    {
      label: "规则状态",
      prop: "status",
      type: "input",
    },
    {
      label: "最后修改人",
      prop: "updateUserName",
      type: "input",
    },
    {
      label: "最后修改时间",
      prop: "updateTime",
      type: "input",
    },

  ]
}
