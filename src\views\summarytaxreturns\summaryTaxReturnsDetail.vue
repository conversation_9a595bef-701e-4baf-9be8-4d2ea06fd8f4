<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      <div class="button">
        <!--<el-button-->
          <!--type="primary"-->
          <!--v-if="-->
            <!--form.settlementStatus == 0 && permission.summaryTaxReturns_create-->
          <!--"-->
          <!--@click="create()"-->
          <!--&gt;客户结算</el-button-->
        <!--&gt;-->
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
      >
        <!-- 任务编号 -->
        <template slot-scope="{}" slot="orderEntrustedCode">
          <div class="to-view" @click="toOrderEntrustedView()">
            <a readonly>
              {{ form.orderEntrustedCode }}
            </a>
          </div>
        </template>
        <template slot="doctorInfoFile">
          <el-button
            v-if="form.doctorInfoFile == 0"
            type="primary"
            @click="downloadIdCardInfo"
          >
            点击生成会员证件压缩包
          </el-button>
          <el-button
            disabled
            v-else-if="form.doctorInfoFile == 1"
            type="primary"
          >
            生成中,请稍后查看
          </el-button>
          <el-button v-else type="primary" @click="downloadImage">
            点击下载
          </el-button>
          <p
            v-if="
              typeof form.doctorInfoFile == 'string' &&
              form.doctorInfoFile.includes('http')
            "
          >
            {{ form.doctorInfoFile }}
          </p>
        </template>
          <template slot="doctorInfoFileSeal">
              <el-button
                v-if="form.doctorInfoFileSeal == 0"
                type="primary"
                @click="downloadIdCardSealInfo"
              >
                点击生成会员证件压缩包
              </el-button>
              <el-button
                disabled
                v-else-if="form.doctorInfoFileSeal == 1"
                type="primary"
              >
                生成中,请稍后查看
              </el-button>
              <el-button v-else type="primary" @click="downloadImage('isSeal')">
                  点击下载
              </el-button>
              <p
                 v-if="typeof form.doctorInfoFileSeal == 'string' &&
                form.doctorInfoFileSeal.includes('http')"
              >
                  {{ form.doctorInfoFileSeal }}
              </p>
          </template>
      </avue-form>
      <template v-else> <el-skeleton :rows="10" animated /> </template>
      <div style="height: 50px"></div>
      <avue-crud
        :option="crudOption"
        :table-loading="loading"
        :data="data"
        :page.sync="page"
        :permission="permissionList"
        :before-open="beforeOpen"
        v-model="curdForm"
        ref="crud"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
      >
        <!-- 初审状态 -->
        <template slot="approvalStatus" slot-scope="{ row }">
          <el-tag v-if="row.approvalStatus == 0" size="small">待审核</el-tag>
          <el-tag v-if="row.approvalStatus == 1" type="success" size="small"
            >通过</el-tag
          >
          <el-tag v-if="row.approvalStatus == 2" type="danger" size="small"
            >驳回</el-tag
          >
        </template>
        <!-- 验收状态 -->
        <template slot="confirmStatus" slot-scope="{ row }">
          <el-tag v-if="row.confirmStatus == 0" size="small">待验收</el-tag>
          <el-tag v-if="row.confirmStatus == 1" type="success" size="small"
            >通过</el-tag
          >
          <el-tag v-if="row.confirmStatus == 2" type="danger" size="small"
            >驳回</el-tag
          >
        </template>
        <template slot="menuLeft">
          <el-button
            type="primary"
            v-if="
              permission.taxReturnsDetail_idCardDispose &&
              idCardDisposeBtn
            "
            @click="idCardDisposeInfo()"
            >生成会员身份证件</el-button
          >
          <el-button
            type="primary"
            @click="handleExport()"
          >下载证件明细</el-button
          >
        </template>
        <template slot="menuLeft"> </template>
        <template slot-scope="{ type, size, row, index }" slot="codeNumber">
          <div class="to-view" @click="toView(row.id)" v-if="row.codeNumber">
            <a readonly>
              {{ row.codeNumber }}
            </a>
          </div>
          <div v-else>无</div>
        </template>
      </avue-crud>
    </basic-container>
    <div style="height: 5vh"></div>
  </div>
</template>

<script>
import { getDetail, create } from "@/api/summarytaxreturns/summaryTaxReturns";
import {
  getList,
  downloadIdCard,
  downloadIdCardSeal,
  idCardDispose,
} from "@/api/summarytaxreturnsdetail/summaryTaxReturnsDetail";
import { mapGetters } from "vuex";
import NProgress from "nprogress";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
export default {
  data() {
    return {
      idCardDisposeBtn: true,
      iframeHeight: window.innerHeight - 180,
      option: {
        labelWidth: "150",
        submitBtn: false,
        emptyBtn: false,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        editBtn: false,
        disabled: true,

        column: [
          {
            label: "编号",
            prop: "code",
            type: "input",
          },
          {
            label: "税款所属期",
            prop: "taxPeriod",
            type: "input",
          },
          {
            label: "收入性质",
            prop: "natureOfIncome",
            type: "select",
            dicData: [
              {
                label: "经营所得",
                value: 1,
              },
              {
                label: "劳务报酬",
                value: 2,
              },
            ],
          },
          {
            label: "申报所属期起",
            prop: "beginDate",
            type: "input",
          },
          {
            label: "申报所属期止",
            prop: "endDate",
            type: "input",
          },
          {
            label: "服务总金额",
            prop: "totalAmount",
            type: "input",
          },
          {
            label: "纳税总金额",
            prop: "payableTax",
            type: "input",
          },
          {
            label: "完税状态",
            prop: "taxCertStatus",
            type: "select",
            dicData: [
              {
                label: "未完税",
                value: 1,
              },
              {
                label: "已完税",
                value: 2,
              },
            ],
          },
          {
            label: "完税证明",
            prop: "taxCertFile",
            type: "upload",
            drag: true,
            loadText: "凭证上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
              url: "link",
            },
            limit: 5,
            listType: "picture-card",
            accept: "image/png, image/jpeg",
            tip: "请上传jpg/png格式文件",
            action: "/api/blade-resource/oss/endpoint/put-file",
          },
          {
            label: "会员身份证件",
            prop: "doctorInfoFile",
            type: "input",
          },
          {
              label: "会员身份证件带公章",
              prop: "doctorInfoFileSeal",
              type: "input",
              span: 24,
          },
        ],
      },
      form: {},
      crudForm: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      crudOption: {
        height: "auto",
        calcHeight: 135,
        title: "纳税会员列表",
        tip: false,
        searchShow: true,
        searchMenuPosition: "right",
        border: true,
        index: true,
        addBtn: false,
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        menu: false,
        searchLabelWidth: "120",
        column: [
          {
            label: "会员姓名",
            prop: "doctorName",
            type: "input",
          },
          {
            label: "证件号",
            prop: "idCardNumber",
            type: "input",
          },
          {
            label: "联系电话",
            prop: "doctorPhone",
            type: "input",
          },
          {
            label: "当期服务金额",
            prop: "settlementAmount",
            type: "input",
          },
          {
            label: "增值税",
            prop: "zzsTaxAmount",
            type: "input",
          },
          {
            label: "增值附加税",
            prop: "fjsTaxAmount",
            type: "input",
          },
          {
            label: "个人税",
            prop: "grsTaxAmount",
            type: "input",
          },
          {
            label: "当期纳税总额",
            prop: "sumTaxAmount",
            type: "input",
          },
        ],
      },
      data: [],
      orgData: [], //业务部门id
      id: "",
      actType: 8,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.caseCollectionReport_add, false),
        viewBtn: this.vaildData(
          this.permission.caseCollectionReport_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.caseCollectionReport_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.caseCollectionReport_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.id = this.$route.params.id;

    this.getDetail();
  },
  methods: {
    toView(id) {
      if (this.actType == 8) {
        this.$router.push({
          path: `/caseCollectionReport/detail/${id}`,
        });
      } else if (this.actType == 5) {
        this.$router.push({
          path: `/medicationFeedbackReport/detail/${id}`,
        });
      } else if (this.actType == 4) {
        this.$router.push({
          path: `/clinicalresearchreport/detail/${id}`,
        });
      }
    },
    // 客户结算
    create() {
      create(this.form.id).then((res) => {
        if (res.data.success) {
          this.getDetail();
          this.$message({
            type: "success",
            message: "操作成功",
          });
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    downloadIdCardInfo() {
      downloadIdCard(this.form.id).then((res) => {
        if (res.data.success) {
          this.getDetail();
          this.$message({
            type: "success",
            message: "请求成功，生成压缩包，请5到10分钟后查看下载结果!",
          });
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    downloadIdCardSealInfo() {
      downloadIdCardSeal(this.form.id).then((res) => {
        if (res.data.success) {
          this.getDetail();
          this.$message({
            type: "success",
            message: "请求成功，生成压缩包，请5到10分钟后查看下载结果!",
          });
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    idCardDisposeInfo() {
      idCardDispose(this.form.id).then((res) => {
        if (res.data.success) {
          this.getDetail();
          this.$message({
            type: "success",
            message: "请求成功，正在生成会员身份证件PDF,请两个小时候后查看!",
          });
          this.idCardDisposeBtn = false;
        } else {
          this.$message({
            type: "error",
            message: "请求失败，请联系管理员!",
          });
        }
      });
    },
    handleExport() {
      let id = this.form.id
      let downloadUrl = `/api/blade-svc/summaryTaxReturnsDetail/export?${
        this.website.tokenHeader
      }=${getToken()}&summaryTaxReturnsId=${id}`;

      let values = {};
      this.$confirm("是否下载数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `月度纳税申报单明细${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.summaryTaxReturnsId = this.id;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    // 下载
    downloadImage(flag) {
        if (flag === 'isSeal') {
            window.open(this.form.doctorInfoFileSeal);
        } else {
            window.open(this.form.doctorInfoFile);
        }
    },

    //去企业详情
    toOrderEntrustedView() {
      this.$router.push({
        path: `/orderentrusted/detail/${this.form.orderEntrustedId}`,
      });
    },
    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },

    // 获取详情
    getDetail() {
      getDetail(this.id).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            data.data.orderStatus =
              data.data.orderStatus == 1
                ? "已接单"
                : data.data.orderStatus == 2
                ? "待验收"
                : data.data.orderStatus == 3
                ? "待结算"
                : data.data.orderStatus == 4
                ? "已完成"
                : "已作废";
            data.data.packageAcceptanceAmount = parseFloat(
              data.data.taskUnitPrice * data.data.planNum
            );
            this.form = data.data;
            this.actType = this.form.actType;
            this.onLoad(this.page);
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
  },
};
</script>

<style></style>
