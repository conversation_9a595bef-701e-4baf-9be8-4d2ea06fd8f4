export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  addBtn: true,
  viewBtn: true,
  addBtnIcon: " ",
  viewBtnIcon: " ",
  editBtnIcon: " ",
  delBtnIcon: " ",
  editBtn: true,
  delBtn: true,
  selection: true,
  dialogClickModal: false,
  menuWidth: 240,
  searchLabelWidth: "120",
  column: [
    {
      label: "主键id",
      prop: "id",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "项目编号",
      prop: "code",
      addDisplay: false,
      editDisplay: true,
      viewDisplay: true,
      hide: false,
      width: "140",
      overHidden: true,
    },
    {
      label: "项目名称",
      prop: "name",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      search: true,
      hide: false,
      width: "180",
      overHidden: true,
    },
    {
      label: "项目开始日期",
      prop: "startDate",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      hide: false,
      width: "160",
      overHidden: true,
    },
    {
      label: "项目截止日期",
      prop: "endDate",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      hide: false,
      width: "160",
      overHidden: true,
    },
    {
      label: "项目关联调研模版",
      prop: "researchTemplateId",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "模版名称",
      prop: "researchTemplateName",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      search: true,
      hide: false,
      width: "160",
      overHidden: true,
    },
    {
      label: "计划收集份数",
      prop: "planNum",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "实际收集份数",
      prop: "actualityNum",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "项目负责人",
      prop: "organizer",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "项目开展目的",
      prop: "purpose",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "收集完成状态",
      prop: "isFinish",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: false,
      width: "120",
      overHidden: true,
    },
    {
      label: "项目启用状态",
      prop: "enable",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: false,
      width: "140",
      overHidden: true,
      fixed: "right",
    },
    {
      label: "模版确认状态",
      prop: "confirmStatus",
      type: "select",
      width: "120",
      rules: [
        {
          required: true,
          message: "请选择模版确认状态",
          trigger: ["blur", "change"],
        },
      ],
      dicData: [
        {
          label: "待确认",
          value: 0,
        },
        {
          label: "已确认",
          value: 1,
        },
        {
          label: "已拒绝",
          value: 2,
        },
      ],
      fixed: "right",
    },
    {
      label: "创建人",
      prop: "createUser",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "状态 ",
      prop: "status",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      width: "120",
      overHidden: true,
    },
  ],
};
