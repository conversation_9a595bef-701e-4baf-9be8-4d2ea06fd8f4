<template>
  <div class="trend">
    <div ref="chart" class="trend-canvas"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import "echarts/lib/chart/line";
import "echarts/lib/component/tooltip";
import "echarts/lib/component/legend";
import { getMonthlyStatisticalLineCharts } from "@/api/home/<USER>";
export default {
  name: "homeTrend",
  data: () => {
    return {
      chartData: [
        {
          name: "企业订单金额",
          values: [],
          color: "#1D90A2",
        },
        {
          name: "个人订单金额",
          values: [],
          color: "#FFA91C",
        },
        {
          name: "纳税总额  ",
          values: [],
          color: "#E86262",
        },
      ],
    };
  },
  mounted() {
    this.getMonthlyStatisticalLineCharts();
  },
  methods: {
    getMonthlyStatisticalLineCharts() {
      getMonthlyStatisticalLineCharts().then((res) => {
        res.data.data.enterpriseOrdersStatisticalList; //企业订单额
        this.chartData[0].values = [];
        this.chartData[1].values = [];
        this.chartData[2].values = [];
        //企业订单额
        res.data.data.enterpriseOrdersStatisticalList.map((item) => {
          this.chartData[0].values.push(item.amount);
        });
        //个人订单金额
        res.data.data.personalOrdersStatisticalList.map((item) => {
          this.chartData[1].values.push(item.amount);
        });
        //纳税总额
        res.data.data.taxStatisticalList.map((item) => {
          this.chartData[2].values.push(item.amount);
        });
        this.renderChart();
      });
    },
    renderChart() {
      const chartDom = this.$refs.chart;
      const myChart = echarts.init(chartDom);

      const option = {
        grid: {
          left: 90,
          right: 50,
          top: 80,
          bottom: 50,
        },
        title: {
          text: "月度订单趋势",
          top: 18,
          left: 22,
          textStyle: {
            color: "#000000",
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "axis",
          formatter: (params) => {
            let text = params[0].axisValue + "<br/>";

            params.map((item) => {
              text += item.seriesName + " : " + item.value + "<br/>";
            });
            return text;
          },
          textStyle: {
            align: "left", // 设置文字居右显示
          },
        },
        legend: {
          data: this.chartData.map((data) => data.name),
          top: 18,
          right: 25,
        },
        xAxis: {
          type: "category",
          data: [
            "一月",
            "二月",
            "三月",
            "四月",
            "五月",
            "六月",
            "七月",
            "八月",
            "九月",
            "十月",
            "十一月",
            "十二月",
          ],

          axisLabel: {
            interval: 0, // 坐标轴刻度标签的显示间隔
            rotate: 40, // 标签倾斜的角度
            show: true, // 设置为true强制显示所有标签
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            formatter: "{value}元",
          },
        },
        series: this.chartData.map((data) => ({
          color: data.color,
          name: data.name,
          type: "line",
          data: data.values,
        })),
      };
      myChart.setOption(option);
    },
  },

  beforeUnmount() {},
};
</script>

<style scoped lang="scss">
.trend {
  width: 100%;
  height: 452px;
  border-radius: 8px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.trend-canvas {
  width: 100%;
  height: 100%;
}
.trend-title {
  width: 102px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  line-height: 22px;
  margin: 18px 0 20px 22px;
  margin-top: 18px;
  margin-bottom: 20px;
}
.item-title {
  margin-top: 15px;
  font-size: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
}
</style>
