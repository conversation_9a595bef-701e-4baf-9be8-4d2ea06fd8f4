import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/meetingDoctor/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/demo/meetingDoctor/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/demo/meetingDoctor/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/demo/meetingDoctor/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/demo/meetingDoctor/submit",
    method: "post",
    data: row,
  });
};


//获取已经邀请会员
export const getDoctorList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/meetingDoctor/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

//删除已经邀请会员
export const deleteDoctor = (id) => {
  return request({
    url: "/api/blade-act/demo/meetingDoctor/deleteDoctor",
    method: "post",
    params: {
      id,
    },
  });
};
//获取已经选择会员id
export const getExistDoctorIds = (meetingId) => {
  return request({
    url: "/api/blade-act/demo/meetingDoctor/getExistDoctorIds",
    method: "get",
    params: {
      meetingId,
    },
  });
};

//修改计划收集份数
export const savePlanSearchNum = (id, planSearchNum) => {
  return request({
    url: "/api/blade-act/demo/meetingDoctor/savePlanSearchNum",
    method: "post",
    params: {
      id,
      planSearchNum,
    },
  });
};

export const saveList = (data) => {
  return request({
    url: "/api/blade-act/demo/meetingDoctor/saveList",
    method: "post",
    data,
  });
};

export const updateByDoctor = (data) => {
  return request({
    url: "/api/blade-act/demo/meetingDoctor/updateByDoctor",
    method: "post",
    data,
  });
};
