export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  selection: true,
  addBtnText: "查询代发明细对账单",
  editBtn: false,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "开始日期",
      prop: "begdat",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      rules: [
        {
          required: true,
          message: "请选择开始日期",
          trigger: "blur",
        },
      ],
    },
    {
      label: "结束日期",
      prop: "enddat",
      type: "date",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      endPlaceholder: new Date(),
      rules: [
        {
          required: true,
          message: "请选择结束日期",
          trigger: "blur",
        },
      ],
    },
    {
      label: "业务参考号",
      prop: "ptyref",
      type: "input",
    },
    {
      label: "文件获取地址",
      prop: "fileUrl",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "文件生成时间",
      prop: "fintim",
      type: "date",
      addDisplay: false,
      editDisplay: false,
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
    },
    {
      label: "总笔数",
      prop: "total",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "查询标记",
      prop: "begidx",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "打印任务编号",
      prop: "printid",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
