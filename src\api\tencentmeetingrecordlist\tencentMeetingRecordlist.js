import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-sys/tencentMeetingRecordlist/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-sys/tencentMeetingRecordlist/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-sys/tencentMeetingRecordlist/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-sys/tencentMeetingRecordlist/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-sys/tencentMeetingRecordlist/submit',
    method: 'post',
    data: row
  })
}

