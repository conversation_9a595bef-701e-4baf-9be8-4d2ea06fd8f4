<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      <div
        class="button"
      >
        <el-button
          type="primary"
          @click="previewClick"
        >预览</el-button>
        <!--  平台内容直接发布  会员内容审核后发布-->
        <el-button
          type="primary"
          @click="publishClick(type == '2' ? 2 : 1)"
        >发布</el-button>
        <el-button
          @click="publishClick(0)"
        >存为草稿</el-button>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        ref="form"
        :option="option"
        v-model="form"
        @submit="submit"
      >
        <template #content>
<!--          <avue-ueditor-->
<!--            v-model="form.content"-->
<!--            :options="{ action: '/api/blade-resource/oss/endpoint/put-file', props: { url: 'link', res: 'data' }}"-->
<!--            @blur="$refs.form.validateField('content')"-->
<!--          >-->
<!--          </avue-ueditor>-->
          <content-editor v-model="form.content" @change="$refs.form.validateField('content')"/>
        </template>
      </avue-form>
      <el-dialog
        title="预览"
        append-to-body
        :visible.sync="previewDialogVisible"
        width="455px"
      >
        <h3>{{form.title}}</h3>
        <content-render :value="form.content"/>
        <span slot="footer" class="dialog-footer">
          <el-button @click="previewDialogVisible = false">关 闭</el-button>
        </span>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import {getDetail} from "@/api/contentManagement/contentManagement";
import {add} from "@/api/contentManagement/contentManagement";
import ContentEditor from "@/components/content-editor/editor.vue";
import ContentRender from "@/components/content-editor/render.vue";



export default {
  components: {ContentRender, ContentEditor},
  data() {
    return {
      previewDialogVisible: false,
      type: 2,
      //状态 0 草稿 1待审核 2已发布 3已驳回 4已下架 5已通过
      status: 0,
      form: {
        title: "",
        content: null,
        cover: "",
        readAuthority: 0,
        isComment: 0,
        initBrowseNum: 0,
        initLikeNum: 0,
        isRecommend: 0,
        isTimingPublish: 1 ,
        publishTime: ""
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.type = this.$route.query.type;
    if(this.id) {
      this.getDetail();
    }
  },
  watch: {
  },
  computed: {
    option() {
      const _option =  {
        submitBtn: false,
          emptyBtn: false,
          submitIcon: " ",
          emptyIcon: " ",
          disabled: false,
          labelWidth: "150",
          column: [
          {
            span: 12,
            row: true,
            label: "标题",
            prop: "title",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入标题",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            span: 12,
            label: "内容",
            prop: "content",
            rules: [
              {
                required: true,
                message: "请输入内容",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            span: 24,
            label: "封面",
            prop: "cover",
            type: "upload",
            listType: "picture-img",
            dataType: "string",
            action: "/api/blade-resource/oss/endpoint/put-file",
            propsHttp: {
              res: "data",
              url: "link",
            },
            rules: [
              {
                required: true,
                message: "请上传封面",
                trigger: ["blur", "change"],
              },
            ],
          },
          // {
          //   span: 24,
          //   label: "阅读权限",
          //   prop: "readAuthority",
          //   type: "radio",
          //   value: 0,
          //   dicData: [
          //     {
          //       label: "公开",
          //       value: 0,
          //     },
          //     {
          //       label: "仅会员可见",
          //       value: 1,
          //     },
          //   ],
          // },
          {
            span: 24,
            label: "开放评论",
            prop: "isComment",
            type: "radio",
            value: 0,
            dicData: [
              {
                label: "是",
                value: 0,
              },
              {
                label: "否",
                value: 1,
              },
            ],
          },
          {
            span: 8,
            label: "浏览量",
            prop: "initBrowseNum",
            type: "number",
          },
          {
            span: 8,
            label: "点赞量",
            prop: "initLikeNum",
            type: "number",
          },
          {
            span: 24,
            label: "是否推荐",
            prop: "isRecommend",
            type: "radio",
            value: 0,
            dicData: [
              {
                label: "是",
                value: 0,
              },
              {
                label: "否",
                value: 1,
              },
            ],
          },
          {
            span: 24,
            label: "定时发布",
            prop: "isTimingPublish",
            type: "radio",
            value: 1,
            dicData: [
              {
                label: "是",
                value: 0,
              },
              {
                label: "否",
                value: 1,
              },
            ],
          },
        ],
      }
      if(this.form.isTimingPublish == 0) {
        _option.column.push({
          label: "发布时间",
          prop: "publishTime",
          type: "datetime",
          format: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          rules: [
            {
              required: true,
              message: "请输入发布时间",
              trigger: ["blur", "change"],
            },
          ],
        })
      }
      return _option
    },
  },
  methods: {
    goBack() {
      this.$router.$avueRouter.closeTag();
      window.sessionStorage.setItem("activeContentType", this.type);
      this.$router.go(-1);
    },
    getDetail() {
      let _this = this;
      getDetail(_this.id).then((res) => {
        const data = res.data
        this.form ={
          ...data.data,
          content: JSON.parse(data.data.content)
        }

      });
    },
    submit(form, done) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          add({ ...form, readAuthority: 0, type:this.type, content: Base64.encode(JSON.stringify(form.content)), status: this.status }).then(() => {
              this.$message({
                type: "success",
                message: "成功!",
              });
              done();
              this.goBack();
            },
            (error) => {
              done();
              console.log(error);
            })
        }
      });
    },
    previewClick() {
      this.previewDialogVisible = true
    },
    publishClick(status) {
      this.status = status
      this.$refs.form.submit()
    }
  },
};
</script>

<style scoped lang="scss">
.el-card__body {
  position: relative;
}
.button {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 99;
}
</style>
