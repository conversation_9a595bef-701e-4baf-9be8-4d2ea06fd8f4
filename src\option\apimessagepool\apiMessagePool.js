export default {
  height: "auto",
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  searchLabelWidth: "120",
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "消息编号",
      prop: "messageNumber",
      type: "input",
      search: true,
      searchOrder: 3,
      overHidden: true,
      fixed: true,
    },
    {
      label: "消息场景",
      prop: "businessScenario",
      type: "input",
      search: true,
      searchOrder: 2,
      overHidden: true,
      fixed: true,
    },
    {
      label: "消息查询参数",
      prop: "parameters",
      type: "input",
      width: "200",
      overHidden: true,
      search: true,
    },
    {
      label: "消息状态",
      prop: "messageStatus",
      type: "select",
      search: true,
      searchOrder: 1,
      dicData: [
        {
          label: "未处理",
          value: "RECEIVED",
        },
        {
          label: "已处理",
          value: "PROCESSED",
        },
        {
          label: "处理失败",
          value: "FAILED",
        },
      ],
      overHidden: true,
      fixed: "right",
    },
    {
      label: "消费时间",
      prop: "spendTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      overHidden: true,
    },
    {
      label: "消息所属客户Id",
      prop: "entrustedId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "消息所属客户名称",
      prop: "entrustedName",
      type: "input",
      overHidden: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
