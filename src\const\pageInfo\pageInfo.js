export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  delBtn: false,
  editBtn: false,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "模板编号",
      prop: "code",
      type: "input",
      addDisplay: false, //新增时是否显示
      editDisabled: true, //编辑的时候不能修改
      rules: [
        {
          required: true,
          message: "请输入模板编号",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "模板名称",
      prop: "name",
      type: "input",
      span: 24,
      rules: [
        {
          required: true,
          message: "请输入模板名称",
          trigger: ["blur", "change"],
        },
      ],
    },

    {
      label: "主页背景图",
      prop: "backgroundImage",
      type: "upload",
      listType: "picture-img",
      dataType: "string",
      action: "/api/blade-resource/oss/endpoint/put-file",
      propsHttp: {
        res: "data",
        url: "link",
      },
      rules: [
        {
          required: true,
          message: "请上传主页背景图",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "导航栏背景图",
      prop: "barBackgroundImage",
      type: "upload",
      listType: "picture-img",
      dataType: "string",
      action: "/api/blade-resource/oss/endpoint/put-file",
      propsHttp: {
        res: "data",
        url: "link",
      },
      rules: [
        {
          required: true,
          message: "请上传导航栏背景图",
          trigger: ["blur", "change"],
        },
      ],
    },

    {
      label: "生效状态",
      prop: "status",
      type: "select",
      align: "center",
      addDisplay: false, //新增时是否显示
      width: 100,
      dicData: [
        {
          label: "否",
          value: 0,
        },
        {
          label: "是",
          value: 1,
        },
      ],
      value: 0,
      slot: true,
      editDisplay: false,
    },

    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
