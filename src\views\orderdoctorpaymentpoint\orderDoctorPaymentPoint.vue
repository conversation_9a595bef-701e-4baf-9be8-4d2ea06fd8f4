<template>
  <basic-container>
    <avue-crud
      :option="option"
      :search.sync="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- 会员订单编号 -->
      <template slot-scope="{ type, size, row, index }" slot="orderDoctorCode">
        <div
          class="to-view"
          @click="toOrderDoctor(row.orderDoctorId)"
          v-if="row.orderDoctorCode"
        >
          <a readonly>
            {{ row.orderDoctorCode }}
          </a>
        </div>
        <div v-else>无</div>
      </template>
      <template
        slot-scope="{ type, size, row, index }"
        slot="doctorSettlementCode"
      >
        <div
          class="to-view"
          @click="toOrderDoctorSettlement(row.doctorSettlementId)"
          v-if="row.doctorSettlementCode"
        >
          <a readonly>
            {{ row.doctorSettlementCode }}
          </a>
        </div>
        <div v-else>无</div>
      </template>
      <template slot="auditStatus" slot-scope="{ row }">
        <el-tag v-if="row.auditStatus == 1" type="warning" size="small"
        >待审核</el-tag
        >
        <el-tag v-if="row.auditStatus == 2" type="success" size="small"
        >通过</el-tag
        >
        <el-tag v-if="row.auditStatus == 3" type="danger" size="small"
        >驳回</el-tag
        >
      </template>
      <!-- 支付结果 -->
      <template slot="paymentResult" slot-scope="{ row }">
        <el-tag v-if="row.paymentResult == 1" type="warning" size="small"
          >未支付</el-tag
        >
        <el-tag v-if="row.paymentResult == 2" type="success" size="small"
          >支付成功</el-tag
        >
        <el-tag v-if="row.paymentResult == 3" type="danger" size="small"
          >支付失败</el-tag
        >
      </template>
      <template slot="menuLeft">
        <!--        <el-button type="danger"-->
        <!--                   size="small"-->
        <!--                   icon="el-icon-delete"-->
        <!--                   plain-->
        <!--                   v-if="permission.orderDoctorPayment_delete"-->
        <!--                   @click="handleDelete">删 除-->
        <!--        </el-button>-->
        <el-button
          type="primary"
          size="small"
          @click="toApproval"
        >积分发放审核</el-button>
        <el-button
          type="warning"
          size="small"
          v-if="permission.orderDoctorPayment_export"
          plain
          icon="el-icon-download"
          @click="handleExport"
          >支付单导出
        </el-button>
        <el-button
          type="warning"
          size="small"
          v-if="permission.orderDoctorPaymentFinance_export"
          plain
          icon="el-icon-download"
          @click="handleExportFinance"
          >动资导出
        </el-button>
        <el-button
          type="primary"
          v-if="permission.handleApply_handleImport"
          @click="handleImport"
        >缓动导入
        </el-button>
        <el-button
          type="primary"
          v-if="permission.handleApply_thaw"
          @click="thaws"
        >解冻
        </el-button>
      </template>
    </avue-crud>
    <el-dialog
      title="积分发放审核"
      :visible.sync="approveVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      width="340px"
    >
      <el-form ref="approveForm" :model="approveForm">
        <el-form-item prop="agree">
          <el-switch
            v-model="approveForm.agree"
            active-text="同意"
            inactive-text="驳回"
          />
        </el-form-item>
        <el-form-item label="备注"  prop="auditRemark" :rules="[{ required: !approveForm.agree, message: '请输入备注', trigger: 'change' }]">
          <el-input
            v-model="approveForm.auditRemark"
            resize="none"
            type="textarea"
            placeholder="请输入备注"
            :autosize="{ minRows: 2, maxRows: 3}"
          />
        </el-form-item>
      </el-form>
      <span slot="footer">
          <el-button @click="approveVisible = false">取消</el-button>
          <el-button type="primary" @click="handleApproval">确认</el-button>
        </span>
    </el-dialog>
    <el-dialog
      title="缓动导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-before="uploadBefore"
        :upload-after="uploadAfter"
      >
        <template #excelTemplate>
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove, auditReject, auditPass,thaw
} from "@/api/orderdoctorpaymentpoint/orderDoctorPaymentPoint";
import option from "@/const/orderdoctorpaymentpoint/orderDoctorPaymentPoint";
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import func from "@/util/func";
import {saveOrUpdate} from "@/api/demoActivityPlan/demoActivityPlan";
export default {
  data() {
    return {
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      approveVisible: false,
      approveForm: { agree: true, auditRemark: null },
      // 导入
      thawStatus: false,
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "导入",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "数据上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
            },
            data: {
              isCovered: 1,
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            // action: `${this.id}`,
            action: "/api/blade-svc/orderDoctorPaymentPoint/freeze",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      excelBox: false,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.orderDoctorPayment_add, false),
        viewBtn: this.vaildData(this.permission.orderDoctorPayment_view, false),
        delBtn: this.vaildData(
          this.permission.orderDoctorPayment_delete,
          false
        ),
        editBtn: this.vaildData(this.permission.orderDoctorPayment_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    handleImport() {
      this.excelBox = true;
    },
    uploadBefore(file, done) {
      this.excelBox = false;
      this.$message({
        type: "success",
        message: "操作成功，请稍候查看数据。",
      });
      done();
    },
    uploadAfter(res, done, loading, column) {
      this.$alert(res.data.msg, '提示', {
        confirmButtonText: '确定',
      });
      window.console.log(column);
      this.excelBox = false;
      this.refreshChange();
      done();
    },
    handleTemplate() {
      exportBlob(
        `/api/blade-handleApply/handleApply/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, `缓动导入模板.xlsx`);
      });
    },
    thaws() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      let status = true;
      console.log(this.selectionList);
      this.selectionList.map((item) => {
        if (item.freezeStatus != 2) {
          status = false;
        }
      });
      if (!status) {
        this.$message.warning("请选择操作状态为冻结的数据");
        return;
      }
      if (this.thawStatus) {
        this.$message.warning("解冻中请稍后");
        return;
      }
      this.thawStatus = true;
      thaw(this.ids).then(
        (res) => {
          this.thawStatus = false;
          if (res.data.success) {
            this.$message({
              type: "success",
              message: res.data.msg,
            });
            this.onLoad(this.page);
          }
        },
        () => {
          this.thawStatus = false;
        }
      );
    },
    toOrderDoctor(id) {
      this.$router.push({
        path: `/orderdoctor/detail/${id}`,
      });
    },
    toOrderDoctorSettlement(id) {
      this.$router.push({
        path: `/orderdoctosettlementpoint/detail/${id}`,
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleExport() {
      const orderDoctorCode = func.toStr(this.search.orderDoctorCode);
      const doctorName = func.toStr(this.search.doctorName);
      const doctorPhone = func.toStr(this.search.doctorPhone);
      //支付时间
      let paymentTimeStart = "";
      let paymentTimeEnd = "";
      if (!this.validatenull(this.search.paymentTime)) {
        paymentTimeStart = this.search.paymentTime[0];
        paymentTimeEnd = this.search.paymentTime[1];
      }
      let values = {
        orderDoctorCode,
        doctorName,
        doctorPhone,
        paymentTimeStart,
        paymentTimeEnd,
      };
      let downloadUrl = `/api/blade-svc/orderDoctorPaymentPoint/export-orderDoctorPayment?${
        this.website.tokenHeader
      }=${getToken()}`;
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `会员积分订单支付明细表${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    handleExportFinance() {
      const orderDoctorCode = func.toStr(this.search.orderDoctorCode);
      const doctorName = func.toStr(this.search.doctorName);
      const doctorPhone = func.toStr(this.search.doctorPhone);
      //支付时间
      let paymentTimeStart = "";
      let paymentTimeEnd = "";
      if (!this.validatenull(this.search.paymentTime)) {
        paymentTimeStart = this.search.paymentTime[0];
        paymentTimeEnd = this.search.paymentTime[1];
      }
      let values = {
        orderDoctorCode,
        doctorName,
        doctorPhone,
        paymentTimeStart,
        paymentTimeEnd,
      };
      let downloadUrl = `/api/blade-svc/orderDoctorPaymentPoint/export-orderDoctorPaymentFinance?${
        this.website.tokenHeader
      }=${getToken()}`;
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `会员积分订单动资明细表${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/orderdoctorpaymentpoint/detail/${this.form.id}`,
        });
        return;
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      //支付时间
      if (!this.validatenull(params.paymentTime)) {
        this.query.paymentTimeStart = params.paymentTime[0];
        this.query.paymentTimeEnd = params.paymentTime[1];
        delete this.query.paymentTime;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    toApproval() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.approveVisible = true
    },
    handleApproval() {
      this.$refs.approveForm.validate((valid) => {
        if (valid) {
          const api = this.approveForm.agree ? auditPass : auditReject;
          api({ ids: this.selectionList.map(item => item.id), auditRemark: this.approveForm.auditRemark}).then(() => {
            this.approveVisible = false
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
        }
      });
    }
  },
};
</script>

<style></style>
