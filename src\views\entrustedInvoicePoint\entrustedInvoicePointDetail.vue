<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      <div
        class="button"
        v-if="
          permission.point_entrustedInvoice_submitReview && form.confirmStatus == 1
        "
      >
        <el-button type="success" @click="onAudit(2)">通过</el-button>
        <el-button type="danger" @click="onAudit(3)">驳回</el-button>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
      >
        <template slot-scope="{}" slot="entrustedCompanyName">
          <div class="to-view" @click="toView()">
            <a readonly>
              {{ form.entrustedCompanyName }}
            </a>
          </div>
        </template>
        <template slot-scope="{}" slot="settlementOrderCode">
          <div class="to-view" @click="toViewSettlement()">
            <a readonly>
              {{ form.settlementOrderCode }}
            </a>
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
      <div v-for="(item, index) of form.invoiceDetails" :key="index">
        <avue-form
          ref="form2"
          :option="option2"
          v-model="form.invoiceDetails[index]"
          :upload-preview="uploadPreview"
        >
          <template slot-scope="{ size }" slot="menuForm">
            <!-- <el-button
              :size="option.size"
              type="primary"
              plain
              icon="el-icon-view"
              @click="(showItems = true), (invoiceItems = item.invoiceItems)"
              >项目明细</el-button
            > -->
            <el-button
              type="primary"
              plain
              icon="el-icon-delete"
              :size="size"
              @click="removeInvoice(item)"
              v-if="
                permission.point_entrustedInvoice_removeInvoice &&
                form.orderStatus != 3 && (form.confirmStatus == 0 || form.confirmStatus == 3)
              "
              >删除</el-button
            >
          </template>
        </avue-form>
      </div>
      <el-dialog
        title="发票项目明细"
        append-to-body
        :visible.sync="showItems"
        width="75%"
      >
        <avue-crud :data="invoiceItems" :option="optionCurd2"></avue-crud>
      </el-dialog>
      <el-dialog
        title="附件预览"
        :visible.sync="dialogVisible"
        append-to-body
        width="50%"
      >
        <el-image
          v-if="filetType == 1"
          :src="fileUrl"
          :preview-src-list="fileUrlList"
        ></el-image>
        <iframe v-else :src="fileUrl" width="100%" height="600px"></iframe>
        <span slot="footer" class="dialog-footer"> </span>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import {
  getDetail,
  getInvoiceItem,
  removeInvoice,
  invoiceSubmit,
  submitReview,
} from "@/api/entrustedInvoicePoint/entrustedInvoicePoint";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      //弹窗
      dialogOption: {
        submitText: "完成",
        span: 24,
        column: [
          {
            label: "验收说明",
            prop: "confirmResult",
            type: "textarea",
            maxlength: 500,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入验收说明",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },
      dialogVisible: false,
      filetType: 1,
      fileUrl: "",
      fileUrlList: [],
      id: "", //数据id
      invoiceItems: {}, //发票项目明细
      showItems: false,
      form: {},
      query: {},
      loading: false,
      //基础信息
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "基本信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "结算单编码",
                prop: "settlementOrderCode",
              },

              {
                label: "客户名称",
                prop: "entrustedCompanyName",
              },
              {
                label: "发票单编号",
                prop: "code",
              },
              {
                label: "发票单名称",
                prop: "name",
              },
              {
                label: "结算单总金额",
                prop: "settlementTotalAmount",
              },
              {
                label: "发票开具金额",
                prop: "uploadedInvoiceTotal",
              },
              {
                label: "发票开具张数",
                prop: "invoiceNumber",
              },

              {
                label: "发票状态",
                prop: "orderStatus",
                type: "select",
                search: true,
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=entrusted_invoice_status",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                disabled: true,
                dataType: "number",
                slot: true,
                width: 140,
              },
            ],
          },
          {
            label: "审核结果",
            arrow: true,
            prop: "group4",
            column: [
              {
                label: "验收时间",
                prop: "confirmDate",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "验收状态",
                prop: "confirmStatus",
                type: "select",
                dicData: [
                  {
                    label: "待提交",
                    value: 0,
                  },
                  {
                    label: "待审核",
                    value: 1,
                  },
                  {
                    label: "验收通过",
                    value: 2,
                  },
                  {
                    label: "验收驳回",
                    value: 3,
                  },
                ],
              },
              {
                label: "验收说明",
                prop: "confirmResult",
                type: "input",
              },
            ],
          },
        ],
      },
      option2: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "发票信息",
            arrow: true,
            prop: "group2",
            column: [
              {
                label: "发票上传类型",
                prop: "type",
                type: "radio",
                control: (val) => {
                  if (val === 1) {
                    return {
                      invoiceDataCode: { display: true },
                      invoiceNumber: { display: true, label: "发票号码" },
                      checkCode: { display: true },
                      billingTime: { display: true },
                      amount: { display: true },
                      taxDiskCode: { display: true },
                      invoiceTypeName: { display: true },
                      purchaserName: { display: true },
                      taxpayerNumber: { display: true },
                      taxpayerBankAccount: { display: true },
                      taxpayerAddressOrPhone: { display: true },
                      salesName: { display: true },
                      salesTaxpayerNum: { display: true },
                      salesTaxpayerBankAccount: { display: true },
                      salesTaxpayerAddress: { display: true },
                      taxAmount: { display: true },
                      totalTaxAmount: { display: true },
                    };
                  } else {
                    return {
                      invoiceDataCode: { display: false },
                      invoiceNumber: { display: true, label: "数电票号码" },
                      checkCode: { display: false },
                      billingTime: { display: true },
                      amount: { display: false },
                      taxDiskCode: { display: false },
                      invoiceTypeName: { display: false },
                      purchaserName: { display: true },
                      taxpayerNumber: { display: true },
                      taxpayerBankAccount: { display: false },
                      taxpayerAddressOrPhone: { display: false },
                      salesName: { display: true },
                      salesTaxpayerNum: { display: true },
                      salesTaxpayerBankAccount: { display: false },
                      salesTaxpayerAddress: { display: false },
                      taxAmount: { display: false },
                      totalTaxAmount: { display: true },
                    };
                  }
                },
                value: 1,
                dicData: [
                  {
                    label: "普通发票",
                    value: 1,
                  },
                  {
                    label: "数电发票",
                    value: 2,
                  },
                ],
              },
              {
                label: "开票日期",
                prop: "billingTime",
                type: "date",
                format: "yyyy-MM-dd",
              },
              {
                label: "发票号码",
                prop: "invoiceNumber",
              },
              {
                label: "购方名称",
                prop: "purchaserName",
              },
              {
                label: "购方纳税人识别号",
                prop: "taxpayerNumber",
              },
              {
                label: "购方开户行及账号",
                prop: "taxpayerBankAccount",
              },
              {
                label: "购方地址、电话",
                prop: "taxpayerAddressOrPhone",
              },
              {
                label: "销方名称",
                prop: "salesName",
              },
              {
                label: "销方纳税人识别号",
                prop: "salesTaxpayerNum",
              },
              {
                label: "销方开户行及账号",
                prop: "salesTaxpayerBankAccount",
              },
              {
                label: "销方地址、电话",
                prop: "salesTaxpayerAddress",
              },
              {
                label: "金额",
                prop: "amount",
                append: "元",
              },
              {
                label: "税额",
                prop: "taxAmount",
                append: "元",
              },
              {
                label: "价税合计",
                prop: "totalTaxAmount",
                append: "元",
              },
              {
                label: "附件",
                prop: "invoiceFileLink",
                type: "upload",
                // listType: 'picture-img',
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
                span: 8,
              },
            ],
          },
        ],
      },
      data: [],
      optionCurd2: {
        index: true,
        title: "发票项目明细",
        titleSize: "h4",
        stripe: true,
        addBtn: false,
        refreshBtn: false,
        columnBtn: false,
        editBtn: false,
        menu: false,
        border: true,
        column: [
          {
            label: "项目名称",
            prop: "itemName",
          },
          {
            label: "单位",
            prop: "unit",
          },
          {
            label: "数量",
            prop: "amount",
          },
          {
            label: "单价",
            prop: "priceUnit",
          },
          {
            label: "税率",
            prop: "taxRate",
          },
          {
            label: "税额",
            prop: "taxAmount",
          },
          {
            label: "金额",
            prop: "totalTaxAmount",
          },
        ],
      },
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      data2: [],
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail(); //获取详情
    this.getInvoiceItem(this.page);
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    onAudit(confirmStatus) {
      let _this = this;
      if (confirmStatus == 2) {
        this.$confirm("确定将该条数据审核通过?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let data = {
              confirmStatus: confirmStatus,
              id: _this.id,
              confirmResult: "同意",
            };
            return submitReview(data);
          })
          .then(() => {
            this.getDetail();
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      } else {
        _this.$DialogForm.show({
          title: "审核",
          width: "30%",
          menuPosition: "right",
          option: this.dialogOption,
          beforeClose: (done) => {
            setTimeout(() => {
              done();
            }, 100);
          },
          callback: (res) => {
            res.data.confirmStatus = confirmStatus;
            res.data.id = _this.id;
            submitReview(res.data).then(
              () => {
                this.getDetail();
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
                res.close();
              },
              (error) => {
                res.done();
                console.log(error);
              }
            );
          },
        });
      }
    },
    //去企业详情
    toView() {
      this.$router.push({
        path: `/entrustedcompany/detail/${this.form.entrustedCompanyId}/1`,
      });
    },
    //去结算单详情
    toViewSettlement() {
      this.$router.push({
        path: `/settlementOrderDoctorPoint/detail/${this.form.settlementOrderId}`,
      });
    },
    uploadPreview(file) {
      this.fileUrl = file.url;
      //获取最后一个.的位置
      var index = this.fileUrl.lastIndexOf(".");
      //获取后缀
      var ext = this.fileUrl.substring(index + 1);
      this.filetType = 1;
      if (ext == "pdf") {
        this.filetType = 2;
      } else {
        this.fileUrlList[0] = this.fileUrl;
      }
      this.dialogVisible = true;
    },
    // 删除
    removeInvoice(data) {
      this.$confirm("确定将该发票数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return removeInvoice(data.id);
        })
        .then(() => {
          this.getDetail();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    // 获取数据详情
    getDetail() {
      getDetail(this.id).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            this.form = data.data;
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
    // 获取发票项目明细
    getInvoiceItem(page) {
      this.loading = true;
      getInvoiceItem(page.currentPage, page.pageSize, this.id).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data2 = data.records;
        this.loading = false;
      });
    },
    submit(status) {
      invoiceSubmit(this.id, status).then((res) => {
        if (res.data.success) {
          this.getDetail();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        }
      });
    },
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}
.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
