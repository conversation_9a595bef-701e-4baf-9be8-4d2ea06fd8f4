import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/settlementOrderDoctor/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/settlementOrderDoctor/detail",
    method: "get",
    params: {
      id,
    },
  });
};

//生成明细结算单文件
export const createSettlementOrderFile = (SettlementOrderDoctorId) => {
  return request({
    url: "/api/blade-svc/settlementOrderDoctor/createSettlementOrderFile",
    method: "get",
    params: {
      SettlementOrderDoctorId,
    },
  });
};
