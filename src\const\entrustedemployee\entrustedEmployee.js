// 手机号验证
const validateLinkTel = (rule, value, callback) => {
  let reg = /^(1\d{10})$/;
  if (value === "") {
    callback(new Error("请输入手机号"));
  } else {
    if (!reg.test(value)) {
      callback(new Error("请输入合法的手机号"));
    } else {
      callback();
    }
  }
};

export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  delBtnIcon: " ",
  editBtnIcon: " ",
  addBtnIcon: " ",
  viewBtnIcon: " ",
  submitIcon: " ",
  emptyIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  cancelBtnIcon: " ",
  dialogClickModal: false,
  labelWidth: "100",
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "企业名称",
      prop: "entrustedCompanyId",
      type: "select",
      props: {
        label: "name",
        value: "id",
      },
      dicUrl: "/api/blade-csc/entrustedCompany/getList",
      rules: [
        {
          required: true,
          message: "请选择企业名称",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "姓名",
      prop: "name",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入代表姓名",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "工号",
      prop: "jobNumber",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入工号",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "职位",
      prop: "jobPosition",
      type: "input",
    },
    {
      label: "部门简称",
      prop: "entrustedDeptShort",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "部门全称",
      prop: "entrustedDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "手机号码",
      prop: "phone",
      type: "input",
      search: true,
      searchLabelWidth: 110,
      rules: [
        {
          required: true,
          message: "请输入手机号码",
          trigger: ["blur", "change"],
        },
        {
          validator: validateLinkTel,
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "所在部门",
      prop: "entrustedDeptId",
      search: true,
      type: "tree",
      dicData: [],
      hide: true,
      addDisabled: false,
      searchLabelWidth: 110,
      props: {
        label: "title",
        value: "id",
      },
      rules: [
        {
          required: true,
          message: "请输入所在部门",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "在职状态",
      prop: "jobStatus",
      type: "select",
      rules: [
        {
          required: true,
          message: "请选择在职状态",
          trigger: ["blur", "change"],
        },
      ],
      dicData: [
        {
          label: "离职",
          value: 0,
        },
        {
          label: "在职",
          value: 1,
        },
      ],
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
