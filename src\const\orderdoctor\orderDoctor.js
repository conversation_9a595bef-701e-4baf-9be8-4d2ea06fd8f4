export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  delBtn: false,
  editBtn: false,
  addBtn: false,
  viewBtnIcon: " ",
  dialogClickModal: false,
  searchLabelWidth: 130,
  column: [
    {
      label: "订单编号",
      prop: "code",
      type: "input",
      search: true,
      searchOrder: 3,
      fixed: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "计划编号",
      prop: "planCode",
      type: "input",
      search: true,
      searchOrder: 2,
      width: "120",
      overHidden: true,
    },
    {
      label: "客户名称",
      prop: "entrustedCompanyName",
      type: "input",
      search: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "统一社会信用代码",
      prop: "socialCreditCode",
      type: "input",
      width: "140",
      overHidden: true,
    },
    {
      label: "订单发布时间",
      prop: "realityStartDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      width: "120",
      overHidden: true,
    },
    {
      label: "订单计划开始时间",
      prop: "invitationStartDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: "140",
      overHidden: true,
    },
    {
      label: "订单计划结束时间",
      prop: "invitationEndDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: "140",
      overHidden: true,
    },

    {
      label: "产品名",
      prop: "baseProductName",
      type: "input",
      width: "120",
      overHidden: true,
    },

    {
      label: "服务项目类型",
      prop: "projectTypeName",
      type: "input",
      width: "120",
      overHidden: true,
    },
    {
      label: "活动类型",
      prop: "actType",
      type: "select",
      search: true,
      searchOrder: 2,
      dicData: [
        {
          label: "学术会议",
          value: 1,
        },
        {
          label: "知识创作",
          value: 2,
        },
        {
          label: "临床调研",
          value: 4,
        },
        {
          label: "用药反馈",
          value: 5,
        },
        {
          label: "专业评审",
          value: 6,
        },
        {
          label: "病例征集",
            value: 8,
        },
      ],
      width: "120",
      overHidden: true,
      hide: true,
    },
    {
      label: "单价",
      prop: "taskUnitPrice",
      type: "input",
      width: "120",
      overHidden: true,
    },
    {
      label: "订单计划数量",
      prop: "planNum",
      type: "input",
      width: "120",
      overHidden: true,
    },
    {
      label: "订单计划金额/积分",
      prop: "planAmount",
      type: "input",
      width: "140",
      overHidden: true,
    },
    {
      label: "支付方式",
      prop: "payType",
      type: "select",
      search: true,
      searchOrder: 1,
      dicData: [
        {
          label: "积分",
          value: 1,
        },
        {
          label: "非积分",
          value: 2,
        },
      ],
      width: "120",
      overHidden: true,
    },
    {
      label: "接单人员姓名",
      prop: "doctorName",
      type: "input",
      search: true,
      searchOrder: 1,
      width: "120",
      overHidden: true,
    },
    {
      label: "接单人员手机",
      prop: "doctorPhone",
      type: "input",
      search: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "接单人证件号",
      prop: "doctorIdCard",
      type: "input",
      search: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "订单接单时间",
      prop: "receivingOrderData",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: "120",
      overHidden: true,
    },
    {
      label: "订单任务状态",
      prop: "orderStatus",
      type: "select",
      search: true,
      searchOrder: 2,
      dicData: [
        {
          label: "已接单",
          value: 1,
        },
        {
          label: "待验收",
          value: 2,
        },
        {
          label: "待结算",
          value: 3,
        },
        {
          label: "已完成",
          value: 4,
        },
        {
          label: "已作废",
          value: 5,
        },
      ],
      fixed: "right",
      width: "120",
      overHidden: true,
    },
  ],
};
