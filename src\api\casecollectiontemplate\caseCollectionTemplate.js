import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/caseCollectionTemplate/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/caseCollectionTemplate/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/caseCollectionTemplate/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/caseCollectionTemplate/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/caseCollectionTemplate/submit",
    method: "post",
    data: row,
  });
};

export const getIssueList = () => {
  return request({
    url: "/api/blade-act/caseCollectionTemplateAttr/getList",
    method: "get",
  });
};

export const stopTemplate = (id, status) => {
  return request({
    url: "/api/blade-act/caseCollectionTemplate/stopTemplate",
    method: "get",
    params: {
      id,
      status,
    },
  });
};
