export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  title: "平台净收入记账单",
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  delBtn: false,
  addBtn: false,
  selection: true,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  menu: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "结算单ID",
      prop: "settlementId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "企业结算单编号",
      prop: "settlementCode",
      type: "input",
      disabled: true,
      span: 24,

      rules: [
        {
          required: true,
          message: "请输入结算单CODE",
          trigger: "blur",
        },
      ],
    },
    {
      label: "企业ID",
      prop: "entrustedId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "客户名称",
      prop: "entrustedName",
      type: "input",
      disabled: true,
    },
    {
      label: "代发经办申请单ID",
      prop: "handleId",
      type: "input",
      disabled: true,
      hide: true,
    },
    {
      label: "业务模式",
      prop: "busmod",
      type: "input",
      disabled: true,
      hide: true,
    },
    {
      label: "业务参考号",
      prop: "yurref",
      type: "input",
      disabled: true,
    },
    {
      label: "出款账号",
      prop: "accnbr",
      type: "input",
      hide: true,
    },
    {
      label: "付款方记账子单元编号",
      prop: "dmadbt",
      type: "input",
      disabled: true,
    },
    {
      label: "收款方记账子单元编号",
      prop: "dmacrt",
      type: "input",
      disabled: true,
    },
    {
      label: "记账金额",
      prop: "trxamt",
      type: "input",
      disabled: true,
    },
    {
      label: "交易摘要",
      prop: "trxtxt",
      type: "input",
      disabled: true,
    },
    {
      label: "记账日期",
      prop: "trxdat",
      type: "input",
      disabled: true,
    },
    {
      label: "记账流水号",
      prop: "trxnbr",
      type: "input",
      disabled: true,
    },
    {
      label: "失败原因",
      prop: "errtxt",
      type: "input",
      disabled: true,
    },

    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
