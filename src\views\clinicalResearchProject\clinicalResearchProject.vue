<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :search.sync="search"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- 完成状态 -->
      <template slot="isFinish" slot-scope="{ row }">
        <el-tag v-if="row.isFinish == 0" size="small">未完成</el-tag>
        <el-tag v-if="row.isFinish == 1" type="success" size="small"
          >已完成</el-tag
        >
      </template>
      <!-- 开启状态 -->
      <template slot-scope="{ type, size, row, index }" slot="enable">
        <div style="cursor: pointer">
          <el-switch
            v-if="permission.caseCollectionTemplate_stopTemplate"
            v-model="row.enable"
            @change="enableChange(row)"
            active-color="#67C23A"
            inactive-color="#cacdd4"
            active-text="启用"
            inactive-text="停用"
            :active-value="1"
            :inactive-value="0"
          >
          </el-switch>
          <div v-else>
            {{ row.enable == 1 ? "启用" : "停用" }}
          </div>
        </div>
      </template>
      <template slot="menuLeft"> </template>
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button v-if="permission.clinicalResearchProject_review&&row.confirmStatus==0" :size="option.size" type="text" @click="confirmChange(row.id, 1)" >确认</el-button>
        <el-button v-if="permission.clinicalResearchProject_review&&row.confirmStatus==0" :size="option.size" type="text" @click="confirmChange(row.id, 2)" >拒绝</el-button>
      </template>
      <!-- <template slot-scope="{ type, size, row, index }" slot="code">
        <div
          class="to-view"
          @click="toOpenPlan(row.caseCollectionId)"
          v-if="row.code"
        >
          <a readonly>
            {{ row.code }}
          </a>
        </div>
        <div v-else>无</div>
      </template> -->
    </avue-crud>
    <el-dialog
      :title="btnStatusText"
      @close="dialogClose"
      append-to-body
      :visible.sync="dialogVisible"
      width="60%"
    >
      <avue-form
        v-if="dialogVisible"
        ref="dialogForm"
        :option="dialogOption"
        v-model="dialogForm"
        @submit="submit"
        @reset-change="handleReset"
      >
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  stopProject,
  confirmProject,
} from "@/api/clinicalResearchProject/clinicalResearchProject";
import option from "@/const/clinicalResearchProject/clinicalResearchProject";
import { mapGetters } from "vuex";
import { validatenum } from "@/util/validate.js";
import dayjs from "dayjs";
export default {
  data() {
    var checkNum = (rule, value, callback) => {
      if (validatenum(value, 2)) {
        callback(new Error("请输入整数值"));
      } else {
        callback();
      }
    };
    return {
      // 弹框标题
      title: "",

      // 是否显示查询
      search: true,
      // 加载中
      loading: true,
      // 是否为查看模式
      view: false,
      // 查询信息
      query: {},
      // 分页信息
      page: {
        currentPage: 1,
        pageSize: 10,
        total: 40,
      },
      // 表单数据
      form: {},
      // 选择行
      selectionList: [],
      // 表单配置
      option: option,
      // 表单列表
      data: [],
      //弹窗
      dialogVisible: false,
      dialogForm: {},
      dialogOption: {
        submitIcon: " ",
        emptyIcon: " ",
        labelWidth: "140",
        column: [
          {
            label: "项目编号",
            prop: "code",
            display: false,
            overHidden: true,
            disabled: true,
            value: 0,
          },
          {
            label: "项目名称",
            prop: "name",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入项目名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "项目开始日期",
            prop: "startDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择项目开始日期",
                trigger: ["blur", "change"],
              },
            ],
            change: ({ value }) => {
              if (
                dayjs(value).valueOf() >
                dayjs(this.dialogForm.endDate).valueOf()
              ) {
                this.dialogForm.endDate = "";
              }
            },
          },
          {
            label: "项目截止日期",
            prop: "endDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择项目截止日期",
                trigger: ["blur", "change"],
              },
            ],
            pickerOptions: {
              disabledDate: (time) => {
                let start = dayjs(this.dialogForm.startDate).valueOf();
                return time.getTime() < start;
              },
            },
          },
          {
            label: "项目关联调研模版",
            prop: "researchTemplateId",
            type: "select",
            props: {
              label: "templateName",
              value: "id",
            },
            dicUrl:
              "/api/blade-act/clinicalResearchTemplate/getClinicalResearchTemplateList",
            rules: [
              {
                required: true,
                message: "请选择客户名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "计划收集份数",
            prop: "planNum",
            overHidden: true,
            rules: [
              {
                required: true,
                message: "请选择输入计划收集份数",
                trigger: ["blur", "change"],
              },
              {
                validator: checkNum,
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "项目负责人",
            prop: "organizer",
            type: "input",
            rules: [
              {
                required: false,
                message: "请输入项目负责人",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "项目开展目的",
            prop: "purpose",
            type: "input",
            rules: [
              {
                required: false,
                message: "请输入项目开展目的",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },
      btnStatusText: "新增",
      //end
    };
  },
  mounted() {},
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.clinicalResearchProject_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.clinicalResearchProject_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.clinicalResearchProject_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.clinicalResearchProject_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    confirmChange(id, confirmStatus) {
      this.$confirm("是否确认此操作?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          confirmProject(id, confirmStatus).then(
            () => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            },
            () => {
              row.enable = 1;
            }
          );
        })
    },
    dialogClose() {
      this.$refs.dialogForm.resetFields();
    },

    beforeOpen(done, type) {
      if (type == "add") {
        this.dialogOption.emptyBtn = true;
        this.dialogOption.submitBtn = true;
        this.dialogOption.disabled = false;
        this.btnStatusText = "新增";
        this.dialogVisible = true;
        // 新增编码不展示
        this.dialogOption.column[0].display = false;
        this.dialogForm = {};
        this.dialogVisible = true;
        this.dialogOption.column[4].dicUrl =
          "/api/blade-act/clinicalResearchTemplate/getClinicalResearchTemplateList";
      } else if (type == "edit") {
        this.dialogOption.emptyBtn = true;
        this.dialogOption.submitBtn = true;
        this.dialogOption.disabled = false;
        this.dialogOption.column[4].dicUrl =
          "/api/blade-act/clinicalResearchTemplate/getClinicalResearchTemplateList?projectId=" +
          this.form.id;
        // 编码展示
        this.dialogOption.column[0].display = true;
        getDetail(this.form.id).then((res) => {
          this.dialogForm = res.data.data;
          this.btnStatusText = "编辑";
          this.dialogVisible = true;
        });
      } else if (type == "view") {
        this.dialogOption.emptyBtn = false;
        this.dialogOption.submitBtn = false;
        this.dialogOption.disabled = true;
        this.dialogOption.column[4].dicUrl =
          "/api/blade-act/clinicalResearchTemplate/getClinicalResearchTemplateList?projectId=" +
          this.form.id;
        // 编码展示
        getDetail(this.form.id).then((res) => {
          this.dialogForm = res.data.data;
          this.btnStatusText = "查看";
          this.dialogVisible = true;
        });
      } else {
        done();
      }
    },
    submit(form, done) {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          if (!this.dialogForm.id) {
            add(form).then(
              () => {
                this.dialogVisible = false;
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
                done();
              },
              (error) => {
                console.log(error);
                done();
              }
            );
          } else {
            update(form).then(
              () => {
                this.dialogVisible = false;
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
                done();
              },
              (error) => {
                console.log(error);
                done();
              }
            );
          }
        }
      });
    },
    //重置
    handleReset() {},
    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.selectionClear();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    enableChange(row) {
      console.log(row);
      // 只有已确认的数据可以进行启用/禁用操作
      if(row.confirmStatus!=1){
        this.$message({
          type: "error",
          message: "未确认数据无法进行启用/禁用操作!",
        });
        this.onLoad(this.page);
        return
      }
      stopProject(row.id, row.enable).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        () => {
          row.enable = 1;
        }
      );
    },
    searchChange(params, done) {
      console.log(params);
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-pagination {
  margin-top: 20px;
}
</style>
