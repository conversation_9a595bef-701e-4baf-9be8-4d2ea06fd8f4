<template>
  <div>
    <basic-container class="el-card__body"
    ><el-page-header @back="goBack" :content="$route.name"></el-page-header>
    </basic-container>
    <basic-container>
      <avue-crud :option="option"
                 :search.sync="search"
                 :table-loading="loading"
                 :data="data"
                 :page.sync="page"
                 :before-open="beforeOpen"
                 v-model="form"
                 ref="crud"
                 @row-update="rowUpdate"
                 @row-save="rowSave"
                 @row-del="rowDel"
                 @search-change="searchChange"
                 @search-reset="searchReset"
                 @selection-change="selectionChange"
                 @current-change="currentChange"
                 @size-change="sizeChange"
                 @refresh-change="refreshChange"
                 @on-load="onLoad">
        <template slot="menuLeft">
          <el-button type="primary"
                     size="small"
                     icon="el-icon-plus"
                     @click="handleAdd">新 增
          </el-button>
          <el-button type="danger"
                     size="small"
                     icon="el-icon-delete"
                     plain
                     @click="handleDelete">删 除
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
    <el-dialog
      title="请选择会员"
      append-to-body
      @close="closeDialog"
      :visible.sync="showDialog"
      width="75%"
    >
      <avue-crud
        :row-style="rowStyle"
        :table-loading="dialogLoading"
        :data="dialogData"
        :option="dialogOption"
        :page.sync="dialogPage"
        ref="dialogCrud"
        @search-change="dialogSearchChange"
        @search-reset="dialogSearchReset"
        @current-change="dialogCurrentChange"
        @size-change="dialogSizeChange"
        @refresh-change="dialogRefreshChange"
        @selection-change="dialogSelectionChange"
      >
      </avue-crud>
      <span slot="footer" class="dialog-footer">
          <el-button @click="showDialog = false">取 消</el-button>
          <el-button type="primary" @click="saveList()">确 定</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import {getList as getAllDoctorList} from "@/api/authenticationDoctor/authenticationDoctor";
import {getList, getDetail, add, update, remove, getExistDoctorIds, submitList} from "@/api/system/userBlacklist";
  import option from "@/option/system/userBlacklist";
  import {mapGetters} from "vuex";
  import {exportBlob} from "@/api/common";
  import {getToken} from '@/util/auth';
  import {downloadXls} from "@/util/util";
  import {dateNow} from "@/util/date";
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';
import {saveList} from "@/api/demoMeetingdoctor/demoMeetingDoctor";

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        //弹窗
        dialogLoading: false,
        dialogSelectionList: [],
        doctorIds: [],
        showDialog: false,
        dialogData: [],
        dialogPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0,
        },
        dialogQuery: {},
        dialogOption: {
          selectable: (row) => {
            return !this.doctorIds.includes(row.id);
          },
          height: "37vh",
          rowKey: "doctorId",
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          reserveSelection: true,
          border: true,
          index: true,
          menu: false,
          viewBtn: false,
          addBtn: false,
          delBtn: false,
          editBtn: false,
          selection: true,
          dialogClickModal: false,
          column: [
            {
              label: "会员名称",
              prop: "name",
              type: "input",
              search: true,
            },
            {
              label: "医院名称/工作单位",
              prop: "hospitalName",
              type: "input",
              search: true,
              searchLabelWidth: 150,
            },
            {
              label: "所在科室",
              prop: "departmentName",
              type: "input",
            },
            {
              label: "职称",
              prop: "professional",
              type: "input",
            },
            {
              label: "职务",
              prop: "duty",
              type: "input",
            },
            {
              label: "手机号码",
              prop: "phone",
              type: "input",
            },
          ],
        },
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      goBack() {
        this.$router.$avueRouter.closeTag();
        this.$router.go(-1);
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleAdd() {
        this.openDialog()
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleExport() {
        let downloadUrl = `/api/blade-userBlacklist/userBlacklist/export-userBlacklist?${this.website.tokenHeader}=${getToken()}`;
        const {
        } = this.query;
        let values = {
        };
        this.$confirm("是否导出数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          NProgress.start();
          exportBlob(downloadUrl, values).then(res => {
            downloadXls(res.data, `用户黑名单表${dateNow()}.xlsx`);
            NProgress.done();
          })
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage,
          page.pageSize,
          Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      rowStyle({ row }) {
        if (this.doctorIds.includes(row.id)) {
          return {
            backgroundColor: "#eee",
          };
        }
      },
      //打开弹窗
      openDialog() {
        this.dialogPage.currentPage = 1;
        this.getExistDoctorIds();
      },
      //清空搜索
      dialogSearchReset() {
        this.dialogQueryquery = {};
        this.$refs.dialogCrud.toggleSelection();
        this.getExistDoctorIds();
        // this.getDialogList();
      },
      //搜索条件更改
      dialogSearchChange(params, done) {
        this.dialogQueryquery = params;
        this.dialogPage.currentPage = 1;
        this.getDialogList(params);
        done();
      },
      dialogCurrentChange(currentPage) {
        this.dialogPage.currentPage = currentPage;
        this.getDialogList();
      },
      dialogSizeChange(pageSize) {
        this.dialogPage.pageSize = pageSize;
        this.getDialogList();
      },
      //刷新
      dialogRefreshChange() {
        this.getDialogList();
      },
      //选中
      dialogSelectionChange(list) {
        this.dialogSelectionList = list;
      },
      closeDialog() {
        this.$refs.dialogCrud.toggleSelection();
      },
      //保存会员
      saveList() {
        const userBlacklistEntityList = this.dialogSelectionList.map(item => {
          return {
            doctorId: item.id,
            doctorName: item.name,
            phone: item.phone
          }
        })
        submitList({ userBlacklistEntityList }).then(
          () => {
            this.showDialog = false;
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          },
          (error) => {
            console.log(error);
          }
        );
      },
      //获取已经选中会员id
      getExistDoctorIds() {
        getExistDoctorIds(this.id).then((res) => {
          const data = res.data.data;
          this.doctorIds = data;
          this.getDialogList();
        });
      },
      //获取会员列表
      getDialogList(params = {}) {
        this.dialogLoading = true;
        params.cityCode = this.form.cityCode;
        params.approvalResult = 1;
        getAllDoctorList(
          this.dialogPage.currentPage,
          this.dialogPage.pageSize,
          Object.assign(params)
        ).then((res) => {
          const data = res.data.data;
          this.dialogPage.total = data.total;
          this.dialogData = data.records;
          this.showDialog = true;
          this.dialogLoading = false;
          this.$nextTick(() => {
            if (!this.$refs.dialogCrud.gridShow) {
              // myTable是表格的ref属性值
              this.$refs.dialogCrud.doLayout();
            }
          });
        });
      },
    }
  };
</script>

<style>
</style>
