export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  selection: true,
  menu: false,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "姓名",
      prop: "name",
      type: "input",
    },
    {
      label: "同步状态",
      prop: "type",
      type: "input",
    },
    {
      label: "积分获取时间",
      prop: "pointNum",
      type: "input",
    },
    {
      label: "积分获取动作",
      prop: "syncStatus",
      type: "input",
    },
    {
      label: "积分获取数量",
      prop: "syncStatus",
      type: "input",
    },

  ]
}
