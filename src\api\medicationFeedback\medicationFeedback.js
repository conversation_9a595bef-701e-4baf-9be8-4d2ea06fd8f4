import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/medicationFeedback/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getPointList = (current, size, params) => {
  return request({
    url: "/api/blade-act/medicationFeedback/pointList",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (medicationFeedbackId) => {
  return request({
    url: "/api/blade-act/medicationFeedback/detail",
    method: "get",
    params: {
      medicationFeedbackId,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/medicationFeedback/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/medicationFeedback/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/medicationFeedback/submit",
    method: "post",
    data: row,
  });
};

//开始-结束计划
export const stopMedicationFeedback = (id, status) => {
  return request({
    url: "/api/blade-act/medicationFeedback/stopMedicationFeedback",
    method: "get",
    params: {
      id,
      status,
    },
  });
};

//保存会员
export const saveList = (data) => {
  return request({
    url: "/api/blade-act/medicationFeedbackDoctor/saveList",
    method: "post",
    data,
  });
};
//获取已经邀请会员
export const getDoctorList = (current, size, params) => {
  return request({
    url: "/api/blade-act/medicationFeedbackDoctor/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
//删除已经邀请会员
export const deleteDoctor = (id) => {
  return request({
    url: "/api/blade-act/medicationFeedbackDoctor/deleteDoctor",
    method: "post",
    params: {
      id,
    },
  });
};
//获取已经选择会员id
export const getExistDoctorIds = (medicationFeedbackId) => {
  return request({
    url: "/api/blade-act/medicationFeedbackDoctor/getExistDoctorIds",
    method: "get",
    params: {
      medicationFeedbackId,
    },
  });
};

//修改计划收集份数
export const savePlanSearchNum = (id, planSearchNum) => {
  return request({
    url: "/api/blade-act/medicationFeedbackDoctor/savePlanSearchNum",
    method: "post",
    params: {
      id,
      planSearchNum,
    },
  });
};
//重发合同
export const startPlanAgain = (id) => {
  return request({
    url: "/api/blade-act/medicationFeedback/startPlanAgain",
    method: "get",
    params: {
      id,
    },
  });
};
