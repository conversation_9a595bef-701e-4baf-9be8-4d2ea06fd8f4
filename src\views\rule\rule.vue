<template>
  <basic-container>
    <avue-crud :option="option"
               :search.sync="search"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot="status" slot-scope="{ row }">
        <el-tag
            v-if="row.status == 0"
            type="success"
            size="small"
        >生效</el-tag
        >
        <el-tag v-if="row.status == 1" type="danger" size="small"
        >未生效</el-tag
        >
      </template>
      <template slot="menuLeft">

        <el-button
            type="primary"
            v-if="permission.rule_add"
            @click="toAdd()"
        >+ 新增学识规则
        </el-button>

        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.rule_delete"
                   @click="handleDelete">删除学识规则
        </el-button>

      </template>
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button
            :size="option.size"
            type="text"
            v-if="permission.entrustedInvoice_impor"
            @click="ruleConfig(row)"
        >学识规则配置
        </el-button
        >
        <el-button
            :size="option.size"
            type="text"
            :style="{color: row.status==0?'red':''}"
            v-if="permission.entrustedInvoice_impor"
            @click="unUsefulConfig(row)"
        >{{row.status==0?'配置失效':'配置生效'}}
        </el-button
        >
      </template>
    </avue-crud>
    <el-dialog
        title="新增"
        @close="viewDialogForm = {}"
        append-to-body
        destroy-on-close
        :visible.sync="viewDialogVisibleADD"
        width="60%"
    >
        <avue-form ref="formAdd" @submit="handleSubmitAdd"  :option="viewDialogFormADD" v-model="viewDialogFormAdd">
        </avue-form>

    </el-dialog>
    <el-dialog
        title="学识配置规则"
        @close="viewDialogForm = {}"
        append-to-body
        destroy-on-close
        :visible.sync="viewDialogVisible"
        width="60%"
    >
      <avue-form ref="form" @submit="handleSubmit" :option="viewDialogOption[type]" v-model="viewDialogForm">
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getList, getDetail, remove, addOrUpdate} from "@/api/rule/rule";
import option from "@/option/rule/rule";
import {mapGetters} from "vuex";
import 'nprogress/nprogress.css';

export default {
  data() {
    return {
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: option,
      data: [],
      type: 0,
      viewDialogVisible: false,
      viewDialogVisibleADD: false,
      viewDialogForm: {},
      viewDialogFormAdd: {},
      viewDialogFormADD: {
          labelWidth: "150",
          column: [
              {
                  label: "会员动作",
                  prop: "type",
                  type: "select",
                  dicData: option.typeDicData,
                  rules: [
                      {
                          required: true,
                          message: "请选择会员动作",
                          trigger: "blur",
                      },
                  ],
              },
              {
                  label: "获得学识数量",
                  prop: 'pointNum',
                  controlsPosition: '',
                  type: 'number',
                  min: 0,
                  max: 100,
                  step: 5,
                  stepStrictly: true,
              },
              {
                  label: '状态规则',
                  prop: 'status',
                  type: 'radio',
                  dicData: [
                      {label: '生效', value: 0},
                      {label: '未生效', value: 1},
                  ],
                  value: 0,
                  rules: [
                      {
                          required: true,
                          message: "请选择状态规则",
                          trigger: "blur",
                      },
                  ],
              },
          ],
      },

      viewDialogOption: [
        {
          labelWidth: "150",
          column: [
            {
              label: "获得学识数量",
              prop: 'pointNum',
              controlsPosition: '',
              type: 'number',
              min: 0,
              max: 100,
              step: 5,
              span:24,
              stepStrictly: true,
              rules: [
                {
                  required: true,
                  message: "请输入获得学识数量",
                  trigger: "blur",
                },
              ],
            },
          ],
        },
        {
          labelWidth: "150",
          column: [
            {
              label: "获得学识数量",
              prop: 'pointNum',
              controlsPosition: '',
              type: 'number',
              min: 0,
              max: 100,
              step: 5,
              stepStrictly: true,
            },
            {
              label: '每日获取次数上限',
              prop: 'dayLimit',
              type: 'number',
              controls: false,
            },
            {
              label: '字数限制≥',
              prop: 'ruleWordsNum',
              type: 'number',
              controls: false,
            },
            {
              label: '图片数量≥',
              prop: 'ruleImageNum',
              type: 'number',
              controls: false,
            },
            {
              label: '是否需要封面',
              prop: 'ruleCoverFlag',
              type: 'radio',
              dicData: [
                {label: '是', value: 0},
                {label: '否', value: 1},
              ]
            },
          ],
        },
        {
          labelWidth: "150",
          column: [
            {
              label: "获得学识数量",
              prop: 'pointNum',
              controlsPosition: '',
              type: 'number',
              min: 0,
              max: 100,
              step: 5,
              stepStrictly: true,
            },
            {
              label: '每日获取次数上限',
              prop: 'dayLimit',
              type: 'number',
              controls: false,
            },
            {
              label: '字数限制≥',
              prop: 'ruleWordsNum',
              type: 'number',
              controls: false,
            },
          ],
        },
        {
          labelWidth: "150",
          column: [
            {
              label: "获得学识数量",
              prop: 'pointNum',
              controlsPosition: '',
              type: 'number',
              min: 0,
              max: 100,
              step: 5,
              stepStrictly: true,
            },
            {
              label: '每日获取次数上限≥',
              prop: 'dayLimit',
              type: 'number',
              controls: false,
            },
            {
              label: '浏览时间(秒)≥',
              prop: 'ruleTimeNum',
              type: 'number',
              controls: false,
            },
          ],
        },
        {
          labelWidth: "150",
          column: [
            {
              label: "获得学识数量",
              prop: 'pointNum',
              controlsPosition: '',
              type: 'number',
              min: 0,
              max: 100,
              step: 5,
              stepStrictly: true,
            },
            {
              label: '每日获取次数上限',
              prop: 'dayLimit',
              type: 'number',
              controls: false,
            },

          ],
        },
      ]
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        delBtn: this.vaildData(this.permission.rule_delete, false),
      };
    },

    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    handleSubmit(form, done) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          addOrUpdate(form).then(
                () => {
                  this.$message({
                    type: "success",
                    message: "操作成功!",
                  });
                  this.viewDialogVisible = false;
                  this.viewDialogForm ={}
                  this.searchReset();
                },
                (error) => {
                  done();
                  console.log(error);
                }
            );
        }
      });
    },
    handleSubmitAdd(form, done) {
      this.$refs.formAdd.validate((valid) => {
        if (valid) {
          if(this.data.some(e=>e.type==form.type)){
            this.$message.warning('已有此类型的配置规则,请勿重复添加！')
            done()
            return false
          }
          addOrUpdate(form).then(
              () => {
                this.$message({
                  type: "success",
                  message: "操作成功!",
                });
                this.viewDialogVisibleADD = false;
                this.viewDialogFormAdd ={}
                this.searchReset();
              },
              (error) => {
                done();
                console.log(error);
              }
          );
        }
      });
    },
    unUsefulConfig(row, index, done, loading) {
      console.log(row,9999)
      addOrUpdate({id:row.id,status:row.status===0?1:0}).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },

    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad({...this.page, currentPage: 1});
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
    },

    ruleConfig(row) {
      console.log(row.type,888)
      getDetail(row.id).then((res) => {
        this.viewDialogForm = res.data.data[0];
        this.$set(this.viewDialogForm,'dayLimit',this.viewDialogForm.dayLimit===null?undefined:this.viewDialogForm.dayLimit)
        this.$set(this.viewDialogForm,'ruleWordsNum',this.viewDialogForm.ruleWordsNum===null?undefined:this.viewDialogForm.ruleWordsNum)
        this.$set(this.viewDialogForm,'ruleImageNum',this.viewDialogForm.ruleImageNum===null?undefined:this.viewDialogForm.ruleImageNum)
        this.$set(this.viewDialogForm,'ruleTimeNum',this.viewDialogForm.ruleTimeNum===null?undefined:this.viewDialogForm.ruleTimeNum)
        this.$set(this.viewDialogForm,'ruleCoverFlag',this.viewDialogForm.ruleCoverFlag===null?0:this.viewDialogForm.ruleCoverFlag)
        if(row.type==0||row.type==1){
          this.type = 0
        }else if(row.type==2){
          this.type = 1
        }else if(row.type==3){
          this.type = 2
        }else if(row.type==4){
          this.type = 3
        }else if(row.type==5||row.type==6){
          this.type = 4
        }
          this.viewDialogVisible = true;

      });


    },

    toAdd() {
      this.viewDialogVisibleADD = true;
      this.$set(this.viewDialogFormAdd,'type',undefined)
      this.$set(this.viewDialogFormAdd,'pointNum',undefined)
      this.$set(this.viewDialogFormAdd,'status',0)

    },


    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
        console.log(111,params)
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params ={}) {
      this.loading = true;

      // const {} = this.query;
      this.query={...this.query,status:this.query.status1,status1:undefined}
      let values =  Object.assign(params, this.query);

      getList(page.currentPage, page.pageSize, values).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style>
</style>
