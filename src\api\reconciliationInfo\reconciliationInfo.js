import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-reconciliationInfo/reconciliationInfo/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-reconciliationInfo/reconciliationInfo/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-reconciliationInfo/reconciliationInfo/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-reconciliationInfo/reconciliationInfo/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-reconciliationInfo/reconciliationInfo/submit",
    method: "post",
    data: row,
  });
};
//代发明细对账单链接获取
export const accountCheckingResult = (taskid) => {
  return request({
    url: "/api/blade-reconciliationInfo/reconciliationInfo/accountCheckingResult",
    method: "post",
    params: { taskid },
  });
};

//新建招商银行对账单处理结果
export const submitReconciliationInfo = (params) => {
  return request({
    url: "/api/blade-reconciliationInfo/reconciliationInfo/submitReconciliationInfo",
    method: "post",
    params,
  });
};
