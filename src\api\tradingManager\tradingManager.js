import request from "@/router/axios";

//关闭
export const close = (id) => {
  return request({
    url: "/api/blade-tradingManager/tradingManagerInfo/close",
    method: "post",
    params: {
      id,
    },
  });
};

//查询记账子单元
export const tradingManagerList = (dmanbr) => {
  return request({
    url: "/api/blade-tradingManager/tradingManagerInfo/listByDmanbr",
    method: "get",
    params: {
      dmanbr,
    },
  });
};
//新增
export const add = (row) => {
  return request({
    url: "/api/blade-tradingManager/tradingManagerInfo/add",
    method: "post",
    data: row,
  });
};
//修改
export const update = (id, dmanam) => {
  return request({
    url: "/api/blade-tradingManager/tradingManagerInfo/updateInfo",
    method: "post",
    params: {
      id,
      dmanam,
    },
  });
};

//交易管家记账子单元获取列表
export const getSubelementList = (current, size, params) => {
  return request({
    url: "/api/blade-tradingManager/tradingManagerInfo/select",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

//交易管家记账子单元获取详情
export const getSubelementDetail = (id) => {
  return request({
    url: "/api/blade-tradingManager/tradingManagerInfo/detail",
    method: "get",
    params: {
      id,
    },
  });
};

//查询是否存在记账子单元
export const getByCustomerId = (customerId) => {
  return request({
    url: "/api/blade-tradingManager/tradingManagerInfo/getByCustomerId",
    method: "get",
    params: {
      customerId,
    },
  });
};
