export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  labelWidth: 180,
  searchShow: true,
  // searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  align: "center",
  addBtnText: "开通交易管家记账子单元账户",
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  addBtn: true,
  delBtn: false,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
      placeholder: " ",
    },
    // {
    //   label: "账号",
    //   prop: "accnbr",
    //   type: "input",
    //   addDisplay: false,
    //   placeholder: " ",
    //   editDisplay: false,
    // },
    {
      label: "分行号",
      prop: "bbknbr",
      type: "input",
      addDisplay: false,
      placeholder: " ",
      hide: true,
      editDisplay: false,
    },
    {
      label: "记账子单元编号",
      prop: "dmanbr",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    //（账号+记账子单元编号）
    {
      label: "记账子单元账号",
      prop: "trcnbr",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "记账子单元名称",
      prop: "dmanam",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入记账子单元名称",
          trigger: "blur",
        },
      ],
    },
    {
      label: "账户余额",
      prop: "actbal",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    // （Y： 允许透支（默认） N：不允许透支）
    {
      label: "额度控制标志",
      prop: "ovrctl",
      type: "select",
      dicData: [
        {
          label: "允许透支",
          value: "Y",
        },
        {
          label: "不允许透支",
          value: "N",
        },
      ],
      value: "N",
      rules: [
        {
          required: true,
          message: "请选择额度控制标志",
          trigger: "blur",
        },
      ],
      hide: true,
      editDisplay: false,
    },
    //（Y:退回原记账子单元 N：退回结算户（默认））
    {
      label: "退票处理方式",
      prop: "bcktyp",
      type: "select",
      dicData: [
        {
          label: "退回原记账子单元",
          value: "Y",
        },
        {
          label: "退回结算户",
          value: "N",
        },
      ],
      value: "Y",
      rules: [
        {
          required: true,
          message: "请选择退票处理方式",
          trigger: "blur",
        },
      ],
      editDisplay: false,
    },
    //（Y：可关闭（默认）， N：不可关闭）
    {
      label: "余额非零时是否可关闭",
      prop: "clstyp",
      type: "select",
      dicData: [
        {
          label: "可关闭",
          value: "Y",
        },
        {
          label: "不可关闭",
          value: "N",
        },
      ],
      value: "Y",
      rules: [
        {
          required: true,
          message: "请选择余额非零时是否可关闭",
          trigger: "blur",
        },
      ],
      hide: true,
      editDisplay: false,
    },
    {
      label: "业务参考号",
      prop: "yurref",
      type: "input",
      placeholder: " ",
      addDisplay: false,
      editDisplay: false,
    },
    //（N：不设置收款额度（默认）， Y：设置收款额度）
    {
      label: "额度标志",
      prop: "lmtflg",
      placeholder: " ",
      type: "select",
      dicData: [
        {
          label: "设置收款额度",
          value: "Y",
        },
        {
          label: "不设置收款额度",
          value: "N",
        },
      ],
      addDisplay: false,
      hide: true,
      editDisplay: false,
    },
    //（LMTFLG=Y时，BALLMT要大于0）
    {
      label: "余额上限额度",
      prop: "ballmt",
      type: "input",
      placeholder: " ",
      addDisplay: false,
      hide: true,
      editDisplay: false,
    },
    {
      label: "记账子单元币种",
      prop: "dmaccy",
      type: "input",
      placeholder: " ",
      addDisplay: false,
      hide: true,
      editDisplay: false,
    },
    {
      label: "生效日期",
      prop: "eftdat",
      type: "input",
      placeholder: " ",
      addDisplay: false,
      editDisplay: false,
    },
    //（A：生效 W：银行处理中）
    {
      label: "状态",
      prop: "stscod",
      placeholder: " ",
      type: "select",
      dicData: [
        {
          label: "生效",
          value: "A",
        },
        {
          label: "银行处理中",
          value: "W",
        },
      ],
      addDisplay: false,
      editDisplay: false,
    },
    //（Y：有校验 N：不校验）
    {
      label: "付方校验标志",
      prop: "rltchk",
      placeholder: " ",
      type: "select",
      dicData: [
        {
          label: "有校验",
          value: "Y",
        },
        {
          label: "不校验",
          value: "N",
        },
      ],
      addDisplay: false,
      hide: true,
      editDisplay: false,
    },
    //（Y：非关联收款入账付方指定子单元 N：非关联收款入账默认子单元 R：非关联收款拒绝入账 		（空表示未设置关联付方账号））
    {
      label: "入账方式",
      prop: "tlyopr",
      placeholder: " ",
      type: "select",
      dicData: [
        {
          label: "非关联收款入账付方指定子单元",
          value: "Y",
        },
        {
          label: "非关联收款入账默认子单元",
          value: "N",
        },
        {
          label: "非关联收款拒绝入账",
          value: "R",
        },
      ],
      addDisplay: false,
      hide: true,
      editDisplay: false,
    },
    //（余额上限-联机余额）
    {
      label: "收款可用额度",
      prop: "avilmt",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      hide: true,
      editDisplay: false,
    },
    {
      label: "需求方id",
      prop: "customerId",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      hide: true,
      editDisplay: false,
    },
    //（ 0 未开通，1 已开通，2 已关闭）
    {
      label: "开通状态",
      prop: "actstatus",
      placeholder: " ",
      type: "select",
      dicData: [
        {
          label: "未开通",
          value: 0,
        },
        {
          label: "已开通",
          value: 1,
        },
        {
          label: "已关闭",
          value: 2,
        },
      ],
      addDisplay: false,
      editDisplay: false,
    },

    //（S：成功，F：失败，I/W：银行处理中）
    {
      label: "业务处理结果",
      prop: "rtnsts",
      placeholder: " ",
      type: "select",
      dicData: [
        {
          label: "成功",
          value: "S",
        },
        {
          label: "失败",
          value: "F",
        },
        {
          label: "银行处理中",
          value: "I",
        },
        {
          label: "银行处理中",
          value: "W",
        },
      ],
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "错误码",
      prop: "errcod",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      hide: true,
      editDisplay: false,
    },
    {
      label: "错误信息 ",
      prop: "errtxt",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      hide: true,
      editDisplay: false,
    },
    {
      label: "创建人",
      prop: "createUser",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      placeholder: " ",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
