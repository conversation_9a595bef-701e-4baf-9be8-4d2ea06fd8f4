export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  editBtn: false,
  viewBtnIcon: " ",
  addBtnIcon: " ",
  delBtn: false,
  dialogClickModal: false,
  menuWidth: 120,
  searchLabelWidth: "130",
  addBtn: false,
  column: [
    {
      label: "成果单编号",
      prop: "code",
      type: "input",
      searchLabelWidth: "120",
      search: true,
    },
    {
      label: "客户名称id",
      prop: "entrustedCompanyId",
      type: "input",
      hide: true,
    },
    {
      label: "客户方",
      prop: "entrustedCompanyName",
      type: "input",
      search: true,
    },

    {
      label: "平台方",
      prop: "generalContractor",
      type: "input",
    },

    {
      label: "上游企业ID",
      prop: "enterpriseId",
      type: "input",
      hide: true,
    },
    {
      label: "业务期间",
      prop: "accountPeriod",
      type: "month",
      valueFormat: "yyyy年MM月",
      searchLabelWidth: "120",
      search: true,
    },
    {
      label: "服务开始日期",
      prop: "actStartTime",
      type: "datetime",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      searchLabelWidth: "120",
      search: true,
    },
    {
      label: "服务结束日期",
      prop: "actEndTime",
      type: "datetime",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      searchLabelWidth: "120",
      search: true,
    },
    {
      label: "服务活动金额",
      prop: "actFee",
      type: "input",
    },
    {
      label: "成果单总金额",
      prop: "actTotalFee",
      type: "input",
    },
    // {
    //   label: "清单状态",
    //   prop: "submitStatus",
    //   type: "select",
    //   searchLabelWidth: "120",
    //   search: true,
    //   dicData: [
    //     {
    //       label: "制单",
    //       value: 0,
    //     },
    //     {
    //       label: "待验收",
    //       value: 1,
    //     },
    //     {
    //       label: "验收通过",
    //       value: 2,
    //     },
    //     {
    //       label: "验收驳回",
    //       value: 3,
    //     },
    //   ],
    // },

    // {
    //   label: "清单汇总人员",
    //   prop: "summaryPersonnel",
    //   type: "input",
    // },

    {
      label: "结算单编号",
      prop: "settlementActivityCode",
      type: "input",
      width: "145",
    },

    {
      label: "结算单金额",
      prop: "settlementActivityAmount",
      type: "input",
    },
    {
      label: "提交时间",
      prop: "submitDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      hide: true,
    },

    {
      label: "验收时间",
      prop: "confirmDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      hide: true,
    },
    // {
    //   label: "验收说明",
    //   prop: "confirmResult",
    //   type: "input",
    //   hide: true,
    // },
    {
      label: "确认状态",
      prop: "submitStatus",
      type: "select",
      dicData: [
        {
          label: "待确认",
          value: 0,
        },
        {
          label: "已确认",
          value: 1,
        },
        {
          label: "已驳回",
          value: 2,
        },
      ],
    },
    {
      label: "验收说明",
      prop: "confirmResult",
      type: "input",
      width: "165",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
