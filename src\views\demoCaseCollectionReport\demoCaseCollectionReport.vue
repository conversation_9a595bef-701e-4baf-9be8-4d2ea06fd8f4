<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :search.sync="search"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- 初审状态 -->
      <template slot="approvalStatus" slot-scope="{ row }">
        <el-tag v-if="row.approvalStatus == 0" size="small">待审核</el-tag>
        <el-tag v-if="row.approvalStatus == 1" type="success" size="small"
          >通过</el-tag
        >
        <el-tag v-if="row.approvalStatus == 2" type="danger" size="small"
          >驳回</el-tag
        >
      </template>
      <!-- 验收状态 -->
      <template slot="confirmStatus" slot-scope="{ row }">
        <el-tag v-if="row.confirmStatus == 0" size="small">待验收</el-tag>
        <el-tag v-if="row.confirmStatus == 1" type="success" size="small"
          >通过</el-tag
        >
        <el-tag v-if="row.confirmStatus == 2" type="danger" size="small"
          >驳回</el-tag
        >
      </template>
      <!-- <template slot-scope="{ type, size, row, index }" slot="code">
        <div
          class="to-view"
          @click="toOpenPlan(row.caseCollectionId)"
          v-if="row.code"
        >
          <a readonly>
            {{ row.code }}
          </a>
        </div>
        <div v-else>无</div>
      </template> -->
      <template slot="menuLeft"> </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove,
} from "@/api/demoCaseCollectionReport/demoCaseCollectionReport";
import { tree } from "@/api/entrusteddept/entrustedDept";
import option from "@/const/caseCollectionReport/caseCollectionReport";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      search: {},
      selectionList: [],
      option: option,
      data: [],
      orgData: [], //业务部门id
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.caseCollectionReport_add, false),
        viewBtn: this.vaildData(
          this.permission.caseCollectionReport_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.caseCollectionReport_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.caseCollectionReport_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.initData();
    if (
      !this.validatenull(
        window.sessionStorage.getItem("caseCollectionReportSearch")
      )
    ) {
      this.search = JSON.parse(
        window.sessionStorage.getItem("caseCollectionReportSearch")
      );
      this.query = JSON.parse(
        window.sessionStorage.getItem("caseCollectionReportSearch")
      );
      window.sessionStorage.removeItem("caseCollectionReportSearch");
    }
  },

  //组件销毁
  beforeDestroy() {
    if (!this.validatenull(this.query)) {
      window.sessionStorage.setItem(
        "caseCollectionReportSearch",
        JSON.stringify(this.query)
      );
    }
  },
  methods: {
    initData() {
      // tree().then((res) => {
      //   this.option.column[27].dicData = res.data.data;
      //   this.orgData = res.data.data;
      // });
      tree().then((res) => {
        var prop = this.findObject(option.column, "baseDepartment");
        prop.dicData = res.data.data;
        this.orgData = res.data.data;
      });
    },
    getAllChildIdsById(id, data) {
      let _this = this;
      let childIds = [];
      // 递归辅助函数，用于遍历数据并获取子节点ID
      function traverseChildren(nodes) {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].id === id) {
            // 如果当前节点匹配到给定ID，将其子节点ID添加到结果数组中
            if (nodes[i].children && nodes[i].children.length > 0) {
              childIds = childIds.concat(
                _this.getAllChildIdsByIdHelper(nodes[i].children)
              );
            }
          } else if (nodes[i].children && nodes[i].children.length > 0) {
            // 如果当前节点不匹配给定ID，继续向下遍历子节点
            traverseChildren(nodes[i].children);
          }
        }
      }

      traverseChildren([data]); // 调用辅助函数从根节点开始遍历

      return childIds;
    },

    getAllChildIdsByIdHelper(nodes) {
      let childIds = [];
      let _this = this;
      for (let i = 0; i < nodes.length; i++) {
        childIds.push(nodes[i].id);

        if (nodes[i].children && nodes[i].children.length > 0) {
          childIds = childIds.concat(
            _this.getAllChildIdsByIdHelper(nodes[i].children)
          );
        }
      }

      return childIds;
    },

    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/demoCaseCollectionReport/detail/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      //提交时间
      if (!this.validatenull(params.submitTime)) {
        this.query.submitTimeStartStart = params.submitTime[0];
        this.query.submitTimeStartEnd = params.submitTime[1];
        delete this.query.submitTime;
      }
      //审核时间
      if (!this.validatenull(params.approvalDate)) {
        this.query.approvalDateEndStart = params.approvalDate[0];
        this.query.approvalDateEndEnd = params.approvalDate[1];
        delete this.query.approvalDate;
      }

      if (params.baseDepartment) {
        let baseDepartmentId = params.baseDepartment.join(",") + ",";
        params.baseDepartment.map((item) => {
          this.orgData.map((item2) => {
            let itemList = this.getAllChildIdsById(item, item2);
            baseDepartmentId += itemList.join(",");
          });
        });
        // 调用方法，传入给定ID和组织架构数据
        params.baseDepartmentIds = baseDepartmentId;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    toOpenPlan(id) {
      this.$router.push({
        path: `/demoActivityPlan/demoCaseCollectionView/${id}`,
      });
    },
  },
};
</script>

<style></style>
