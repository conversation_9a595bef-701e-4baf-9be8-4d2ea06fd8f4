export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "用户ID",
      prop: "userId",
      type: "input",
    },
    {
      label: "会员ID",
      prop: "doctorId",
      type: "input",
    },
    {
      label: "短链key",
      prop: "shortKey",
      type: "input",
    },
    {
      label: "短链url",
      prop: "shortUrl",
      type: "input",
    },
    {
      label: "请求原链",
      prop: "originalUrl",
      type: "input",
    },
    {
      label: "限制打开次数",
      prop: "periodUpper",
      type: "input",
    },
    {
      label: "已使用次数",
      prop: "periodUse",
      type: "input",
    },
    {
      label: "过期时间",
      prop: "expirationTime",
      type: "input",
    },
    {
      label: "生效状态",
      prop: "validStatus",
      type: "select",
      dicData: [
        {
          label: "已作废",
          value: "0",
        },
        {
          label: "已生效",
          value: "1",
        },
      ],
    },

    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
