<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="orderStatus" slot-scope="{ row }"> </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, add, update, remove } from "@/api/caseInfo/caseInfo";
import option from "@/const/caseInfo/caseInfo";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      importShow: false,
      form: {},
      id: "",
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.entrustedInvoice_add, false),
        viewBtn: this.vaildData(this.permission.entrustedInvoice_view, false),
        delBtn: this.vaildData(this.permission.entrustedInvoice_delete, false),
        editBtn: this.vaildData(this.permission.entrustedInvoice_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    toDetail() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList.length > 1) {
        this.$message.error("只能选择一条数据");
        return;
      }
      if (this.selectionList[0].businessType == 1) {
        this.$router.push({
          path: `/settlementOrder/detail/${this.selectionList[0].settlementOrderId}`,
        });
      } else {
        this.$router.push({
          path: `/settlementOrderDoctor/detail/${this.selectionList[0].settlementOrderId}`,
        });
      }

      // this.$router.push({
      //   path: `/entrustedInvoiceItem/entrustedInvoiceItem`,
      //   query: {
      //     id: this.ids
      //   }
      // });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },

    toView(row) {
      this.$router.push({
        path: `/entrustedInvoice/detail/${row.id}`,
      });
    },
    toEdit(row) {
      this.$router.push({
        path: `/entrustedInvoice/edit/${row.id}`,
      });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/entrustedInvoice/detail/${this.form.id}`,
        });
      } else if (type == "edit") {
        this.$router.push({
          path: `/entrustedInvoice/edit/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
