<template>
  <div>
    <!-- <div>{{ audioUrl }}</div>
    <div>{{ audioDuration }}</div> -->
    <audio
      :id="id"
      @timeupdate="updateProgress"
      @ended="onended"
      @pause="onPause"
      @playing="onPlay"
      controls
      ref="audioRef"
      style="display: none"
    >
      <source :src="audioUrl" type="audio/mpeg" />
      您的浏览器不支持音频播放
    </audio>
    <div class="audio_right">
      <div v-if="!audioIsPlay" @click="playAudio">
        <i style="font-size: 25px" class="el-icon-video-play"></i>
      </div>
      <div v-if="audioIsPlay" @click="playAudio">
        <i style="font-size: 25px" class="el-icon-video-pause"></i>
      </div>
      <div @click="stopAudio"></div>
      <el-slider
        class="slider_box"
        v-model="currentProgress"
        :show-tooltip="false"
        @change="handleProgressChange"
        @input="handleInput"
      />
      <div class="audio_time">
        <span class="audio_current">{{ audioStart }}</span>
        &nbsp;/&nbsp;
        <span class="audio_total">{{ durationTime }}</span>
      </div>
      <!-- <div class="volume">
        <div class="volume_progress" v-show="audioHuds">
          <el-slider
            vertical
            height="100px"
            class="volume_bar"
            v-model="audioVolume"
            :show-tooltip="false"
            @change="handleAudioVolume"
          />
        </div>
        <img
          class="volume_icon"
          v-if="audioVolume <= 0"
          @click.stop="audioHuds = !audioHuds"
          src="../../../public/favicon.png"
          alt=""
        />
        <img
          class="volume_icon"
          v-if="audioVolume > 0"
          @click.stop="audioHuds = !audioHuds"
          src="../../../public/favicon.png"
          alt=""
        />
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "audioPlayer",
  props: {
    audioUrl: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
    audioDuration: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      audioIsPlay: false, //音频是否在播放
      audioStart: "0:00",
      durationTime: "0:00", //音频的总时长，显示的时间格式
      duration: 0, //音频的总时长
      audioVolume: 80, //音量的默认值是0.8
      audioHuds: false, //是否显示音量slider
      audioRef: null,
      currentProgress: 0,
    };
  },
  mounted() {
    this.calculateDuration();
  },
  methods: {
    calculateDuration() {
      var myVid = this.$refs.audioRef;
      myVid.src = this.audioUrl;
      // 监听音频播放完毕
      myVid.addEventListener(
        "ended",
        function () {
          this.audioIsPlay = false;
          this.currentProgress = 0;
        },
        false
      );
      if (myVid != null) {
        let _this = this;
        myVid.oncanplay = function () {
          if (
            _this.validatenull(myVid.duration) ||
            myVid.duration == "Infinity"
          ) {
            // console.log(
            //   "没有获取到时长,使用接口传入的时长为",
            //   _this.audioDuration
            // );
            _this.duration = _this.audioDuration; // 计算音频时长
            _this.durationTime = _this.transTime(_this.audioDuration); //换算成时间格式
          } else {
            // console.log("使用audio获取的音频时长", myVid.duration);
            _this.duration = myVid.duration; // 计算音频时长
            _this.durationTime = _this.transTime(myVid.duration); //换算成时间格式
          }
        };
        // myVid.volume = 0.8; // 设置默认音量50%
        this.audioIsPlay = false;
      }
    },
    // 音频播放时间换算
    transTime(duration) {
      if (typeof duration === "string") {
        duration = parseFloat(duration);
      }
      duration = duration >= 0 ? Math.floor(duration) : Math.ceil(duration);
      let second = duration % 60;
      if (second < 10) second = "0" + second;
      let min = Math.floor(duration / 60);
      if (min < 10) min = "0" + min;
      return min + ":" + second;
      //
      // const minutes = Math.floor(duration / 60);
      // const seconds = Math.floor(duration % 60);
      // const formattedMinutes = String(minutes).padStart(2, "0"); //padStart(2,"0") 使用0填充使字符串长度达到2
      // const formattedSeconds = String(seconds).padStart(2, "0");
      // // console.log(`最终时长${formattedMinutes}:${formattedSeconds}`);
      // return `${formattedMinutes}:${formattedSeconds}`;
    },
    // 播放暂停控制
    playAudio() {
      if (this.$refs.audioRef.paused) {
        // this.$emit("playAudio", this.id); // 通知父组件播放当前音频
        this.$refs.audioRef.play();
        this.audioIsPlay = true;
      } else {
        this.$refs.audioRef.pause();
        this.audioIsPlay = false;
      }
    },
    // 根据当前播放时间，实时更新进度条
    updateProgress(e) {
      var value = e.target.currentTime / this.duration;
      // console.log(this.$refs.audioRef)
      if (this.$refs.audioRef && this.$refs.audioRef.play) {
        this.currentProgress = value * 100;
        this.audioStart = this.transTime(this.$refs.audioRef.currentTime);
      }
    },
    //调整播放进度
    handleProgressChange(val) {
      // console.log("调整播放进度了", val);
      if (!val) {
        return;
      }
      let currentTime = this.duration * (val / 100);

      // 更新音频的当前播放时间
      this.$refs.audioRef.currentTime = currentTime;
      // this.$refs.audioRef.play();
    },
    //
    //调整音量
    handleAudioVolume(val) {
      this.$refs.audioRef.volume = val / 100;
    },
    // 监听：结束事件
    onended() {
      this.audioIsPlay = false;
      this.currentProgress = 0;
    },
    // 暂停事件
    onPause() {
      // console.log("音频被暂停了！！！");
      this.audioIsPlay = false;
    },
    // 播放事件
    onPlay() {
      // console.log("音频播放了了！！！");
      this.audioIsPlay = true;
    },
    stopAudio() {
      console.log("暂停音频");
      console.log(this.$refs.audioRef);
      this.$refs.audioRef.pause();
    },
  },
};
</script>

<style lang="scss" scoped>
.audio_right {
  width: 300px;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: #f1f3f4;
  border-radius: 4px;
  padding: 0 10px;
  box-sizing: border-box;
  position: relative;
  .slider_box {
    flex: 1;
    margin: 0 15px;
  }
  .audio_icon {
    width: 20px;
    height: 20px;
    margin-bottom: 4px;
    cursor: pointer;
  }
  .audio_time {
    margin-left: 20px;
    color: black;
    overflow: hidden;
    font-size: 12px;
    .audio_total {
      float: right;
    }
    .audio_current {
      float: left;
    }
  }
}
.volume {
  position: relative;
  .volume_progress {
    width: 32px;
    height: 140px;
    position: absolute;
    top: -142px;
    right: -4px;
  }
  .volume_bar {
    background: #fff;
    border-radius: 4px;
  }
  .volume_icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
}
.el-slider__button-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.volume_bar {
  .el-slider__runway {
    margin: 0 14px !important;
  }
}
</style>
