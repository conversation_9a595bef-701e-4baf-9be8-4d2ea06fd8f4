<template>
  <basic-container>
    <el-tabs v-model="active" @tab-click="handleClick">
      <el-tab-pane
        label="会员内容"
        name="1"
      >
      </el-tab-pane>
      <el-tab-pane
        label="平台内容"
        name="2"
      >
      </el-tab-pane>
      <el-tab-pane
        label="圈子"
        name="3"
      ></el-tab-pane>
    </el-tabs>
    <avue-crud :option="option"
               :search.sync="search"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :permission="permissionList"
               v-model="form"
               ref="crud"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template slot-scope="{ row }" slot="menu">
        <el-button
          type="text"
          v-if="permission.contentManagement_view"
          @click="viewClick(row)"
        >查看</el-button>
        <el-button
          type="text"
          v-if="permission.contentManagement_edit && row.status == 0"
          @click="editClick(row)"
        >编辑</el-button>
        <el-button
          type="text"
          v-if="permission.contentManagement_edit && (row.status == 0 || row.status == 4)"
          @click="rowUpdate([row], 2)"
        >发布</el-button>
        <el-button
          type="text"
          v-if="permission.contentManagement_edit && (row.status == 2)"
          @click="rowUpdate([row], 4)"
        >下架</el-button>
        <el-button
          type="text"
          v-if="permission.contentManagement_view"
          @click="viewCommen(row)"
        >查看评论</el-button>
        <el-button
          type="text"
          v-if="permission.contentManagement_delete&& (row.status != 2)"
          @click="rowDel([row.id])"
        >删除</el-button>
      </template>
      <template slot="status" slot-scope="{ row }">
        <el-tag v-if="row.status == 0" type="info" size="small">草稿</el-tag>
        <el-tag v-if="row.status == 1" type="warning" size="small">待审核</el-tag>
        <el-tag v-if="row.status == 2" size="small" type="success">已发布</el-tag>
        <el-tag v-if="row.status == 3" type="danger" size="small">已驳回</el-tag>
        <el-tag v-if="row.status == 4" type="info" size="small">已下架</el-tag>
        <el-tag v-if="row.status == 5" size="small">待发布</el-tag>
      </template>
      <template slot="menuLeft">
        <el-button v-if="permission.contentManagement_add && active == '2'" type="primary" @click="addContent"
        >新增内容</el-button>
        <el-dropdown>
          <el-button>
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="rowUpdate(selectionList, 2)">批量发布</el-dropdown-item>
            <el-dropdown-item @click.native="rowUpdate(selectionList, 4)">批量下架</el-dropdown-item>
            <el-dropdown-item @click.native="rowDel(selectionList.map(item => item.id))">批量删除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template #interaction="{ row }">
            <span class="interaction">
              <span><i class="interaction-icon el-icon-view"></i>{{ row.browseNumCount || 0 }}</span>
              <span><i class="interaction-icon el-icon-thumb"></i>{{ row.likeNumCount || 0 }}</span>
              <span><i class="interaction-icon el-icon-chat-square"></i>{{ row.commentNum || 0 }}</span>
              <span><i class="interaction-icon el-icon-position"></i>{{ row.transmitCount || 0 }}</span>
            </span>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {getListPage, add, update, remove, updateStatus} from "@/api/contentManagement/contentManagement";
  import option from "@/option/contentManagement/contentManagement";
  import {mapGetters} from "vuex";
  import {exportBlob} from "@/api/common";
  import {getToken} from '@/util/auth';
  import {downloadXls} from "@/util/util";
  import {dateNow} from "@/util/date";
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';

  export default {
    data() {
      return {
        active: "1",
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.contentManagement_add, false),
          viewBtn: this.vaildData(this.permission.contentManagement_view, false),
          delBtn: this.vaildData(this.permission.contentManagement_delete, false),
          editBtn: this.vaildData(this.permission.contentManagement_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      if (!this.validatenull(window.sessionStorage.getItem("activeContentType"))) {
        this.active = window.sessionStorage.getItem("activeContentType");
        window.sessionStorage.removeItem("activeContentType");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowDel(ids) {
        if(ids.length === 0 ){
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定是否删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      viewCommen(row) {
        this.$router.push({
          path: `/commenmanagement/commenManagement`,
          query: { contentId: row.id }
        });
        window.sessionStorage.setItem("activeContentType", row.type);
      },
      rowUpdate(selection, status) {

        const rejectStatusRow = selection.find(item => item.status == '3');
        if(rejectStatusRow) {
          this.$message.warning("不可操作已驳回的数据")
          return;
        }
        const ids = selection.map(item => item.id)

        if(ids.length === 0 ){
          this.$message.warning("请选择至少一条数据");
          return;
        }
        const statusName = {
          2: '发布',
          4: '下架'
        }
        this.$confirm(`确定是否${statusName[status]}?`, {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return updateStatus({ ids, status });
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleExport() {
        let downloadUrl = `/api/blade-contentManagement/contentManagement/export-contentManagement?${this.website.tokenHeader}=${getToken()}`;
        const {
        } = this.query;
        let values = {
        };
        this.$confirm("是否导出数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          NProgress.start();
          exportBlob(downloadUrl, values).then(res => {
            downloadXls(res.data, `内容管理表${dateNow()}.xlsx`);
            NProgress.done();
          })
        });
      },
      viewClick(row) {
        this.$router.push({
          path: `/contentManagement/contentDetail/${row.id}`,
          query: { type: this.active }
        });
      },
      editClick(row) {
        this.$router.push({
          path: `/contentManagement/contentEdit/${row.id}`,
          query: { type: this.active }
        });
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getListPage(page.currentPage, page.pageSize, Object.assign(params, this.query, { type: this.active })).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      handleClick(tab) {
        this.page.currentPage = 1;
        this.query = {};
        this.data = [];
        this.onLoad(this.page);
      },
      addContent() {
        this.$router.push({
          path: `/contentManagement/contentCreate`,
          query: { type: 2 }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
.interaction {
  display: flex;
  gap: 12px;
  color: #ababab;
}
.interaction-icon {
  margin-right: 5px;
}
</style>
