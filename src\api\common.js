import request from "@/router/axios";
/**
 * 文件流返回
 * @param url 接口地址
 * @param params 接口参数
 */
export const exportBlob = (url, params) => {
  return request({
    url: url,
    params: params,
    method: "get",
    responseType: "blob",
  });
};

/**
 * sfe获取token
 * @param url 接口地址
 * @param params 接口参数
 */
export const getWebToken = (params) => {
  return request({
    url: "/api/openapi/auth/webToken",
    method: "get",
    params,
  });
};
