import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/summaryInvoice/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/summaryInvoice/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const add = (row) => {
  console.log(row);
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-svc/summaryInvoice/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/summaryInvoice/update",
    method: "post",
    data: row,
  });
};

// 完税发票上传
export const summaryInvoiceUpload = (row) => {
  return request({
    url: "/api/blade-svc/summaryInvoice/summaryInvoiceUpload",
    method: "post",
    data: row,
  });
};

