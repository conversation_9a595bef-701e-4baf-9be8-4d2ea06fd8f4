<template>
  <div>
    <basic-container>
      <div>
        <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      </div>
    </basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <!--病例征集活动计划-->
          <avue-form
            ref="form"
            :option="option"
            v-model="form"
            @submit="submit"
            @reset-change="handleReset"
          >
            <!-- 涉及产品名称 -->
            <template slot-scope="{ disabled, size }" slot="baseProductId">
              <el-select
                v-model="form.baseProductId"
                @change="productNameChange"
                @focus="productNameFocus"
                filterable
                remote
                reserve-keyword
                placeholder="请选择产品名"
                :remote-method="remoteMethodProduct"
                :loading="productSelectLoading"
              >
                <el-option
                  v-for="item in productOptions"
                  :key="item.value"
                  :label="item.productName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </template>

            <!-- 业务人员  -->
            <template slot-scope="{ disabled, size }" slot="baseEmployeeId">
              <el-select
                v-model="form.baseEmployeeId"
                @change="baseEmployeeChange"
                @focus="baseEmployeeFocus"
                filterable
                remote
                reserve-keyword
                placeholder="请选择业务人员"
                :remote-method="remoteMethodBaseEmployee"
                :loading="selectLoading"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </template>
          </avue-form>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import { add } from "@/api/medicationFeedback/medicationFeedback";
import {
  listByProductId,
  getByEntrustedCompanyId,
} from "@/api/activityPlan/activityPlan";
import { getProductList } from "@/api/activityserviceproject/activityServiceProject";
import { mapGetters } from "vuex";
import dayjs from "dayjs";
export default {
  data() {
    return {
      //涉及产品名称
      productSelectOption: {}, //选中的对象
      productOptions: [],
      productSelectLoading: false,
      //业务人员
      selectOption: {}, //选中的对象
      options: [],
      selectLoading: false,
      activeName: "1",
      id: "",
      //基础信息
      option: {
        submitIcon: " ",
        emptyIcon: " ",
        disabled: false,
        labelWidth: "150",
        column: [
          {
            label: "计划开始时间",
            prop: "invitationStartDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择计划开始时间",
                trigger: ["blur", "change"],
              },
            ],
            change: ({ value }) => {
              if (
                dayjs(value).valueOf() >=
                dayjs(this.form.invitationEndDate).valueOf()
              ) {
                this.form.invitationEndDate = "";
              }
            },
          },
          {
            label: "计划结束时间",
            prop: "invitationEndDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择计划结束时间",
                trigger: ["blur", "change"],
              },
            ],
            pickerOptions: {
              disabledDate: (time) => {
                let start = dayjs(this.form.invitationStartDate).valueOf();
                return time.getTime() < start;
              },
            },
          },
          {
            label: "临床项目类型",
            prop: "projectTypeName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请输入临床项目类型",
                trigger: ["blur", "change"],
              },
            ],
            disabled: true,
          },
          {
            label: "产品名",
            prop: "baseProductId",
            type: "select",
            props: {
              label: "key",
              value: "value",
            },
            dicData: [],
            rules: [
              {
                required: true,
                message: "请选择产品名",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "项目服务方案",
            prop: "serviceProjectId",
            type: "select",
            props: {
              label: "projectName",
              value: "id",
            },
            labelTip: "请先选择产品名",
            dicData: [],
            rules: [
              {
                required: true,
                message: "请选择项目服务方案",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "企业名称",
            prop: "entrustedCompanyId",
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            change: ({ value }) => {
              if (value) {
                this.entrustedCompanyId = value;
                this.initOptions();
              }
            },
            dicUrl: "/api/blade-csc/entrustedCompany/getList",
            rules: [
              {
                required: true,
                message: "请选择企业名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "业务人员",
            prop: "baseEmployeeId",
            type: "select",
            labelTip: "请先选择企业名称",
            rules: [
              {
                required: true,
                message: "请选择业务人员",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "业务部门",
            prop: "baseDepartmentName",
            type: "input",
            rules: [
              {
                required: true,
                message: "请选择代表部门",
                trigger: ["blur", "change"],
              },
            ],
            disabled: true,
          },
          {
            label: "所属省",
            prop: "provinceCode",
            type: "select",
            props: {
              label: "name",
              value: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择所属省",
                trigger: ["blur", "change"],
              },
            ],
            cascader: ["cityCode"],
            dicUrl: "/api/blade-system/region/select",
          },
          {
            label: "所属市",
            prop: "cityCode",
            type: "select",
            props: {
              label: "name",
              value: "code",
            },
            rules: [
              {
                required: true,
                message: "请选择所属市",
                trigger: ["blur", "change"],
              },
            ],
            dicFlag: false,
            dicUrl: "/api/blade-system/region/select?code={{key}}",
          },
        ],
      },
      form: {
        baseProductId: "",
        projectTypeName: "用药反馈",
        planSearchNum: "0",
      },
      //医患服务方案列表
      serviceProjectList: [],
      //模板列表
      templateNameList: [],
      //企业名称id
      entrustedCompanyId: "",
    };
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  created() {},
  methods: {
    //获取业务人员
    initOptions() {
      if (!this.entrustedCompanyId) {
        this.$message({
          type: "error",
          message: "请先选择企业名称!",
        });
        return;
      }
      this.selectLoading = true;
      let query = {
        entrustedCompanyId: this.entrustedCompanyId,
      };
      getByEntrustedCompanyId(query).then((res) => {
        const data = res.data;
        this.options = data.data;
        this.selectLoading = false;
        this.entrustedCompanyChange();
      });
    },
    //业务人员搜索
    remoteMethodBaseEmployee(name) {
      if (name !== "") {
        if (!this.entrustedCompanyId) {
          this.$message({
            type: "error",
            message: "请先选择企业名称!",
          });
          return;
        }
        this.productSelectLoading = true;
        let query = {
          name: name,
          entrustedCompanyId: this.entrustedCompanyId,
        };
        getByEntrustedCompanyId(query).then((res) => {
          const data = res.data;
          this.options = data.data;
          this.selectLoading = false;
        });
      } else {
        this.initOptions();
      }
    },
    baseEmployeeFocus() {
      if (this.options.length == 0) {
        this.initOptions();
      }
    },
    //需求方改变后判断是否还有该业务人员
    entrustedCompanyChange() {
      let serviceProjectList = this.options.filter((item) => {
        return item.id == this.form.baseEmployeeId;
      });
      //代表为空
      if (serviceProjectList.length == 0) {
        this.form.baseEmployeeId = ""; //代表id
        this.form.baseEmployeeName = ""; //代表名字
        this.form.baseEmployeeNumber = ""; //代表工号
        this.form.baseDepartmentName = ""; //部门名
        this.form.baseDepartmentId = ""; //部门id
      }
    },
    //代表姓名更改
    baseEmployeeChange(value) {
      let obj = this.options.filter((item) => {
        return item.id == value;
      });
      this.selectOption = obj[0];
      this.form.baseDepartmentName = this.selectOption.entrustedDeptShort;
    },
    //业务人员end
    //初始化数据
    initProductOptions() {
      this.productSelectLoading = true;
      getProductList().then((res) => {
        const data = res.data;
        this.productOptions = data.data;
        this.productSelectLoading = false;
      });
    },
    //涉及产品名称搜索
    remoteMethodProduct(query) {
      if (query !== "") {
        this.productSelectLoading = true;
        getProductList(query).then((res) => {
          const data = res.data;
          this.productOptions = data.data;
          this.productSelectLoading = false;
        });
      } else {
        this.initProductOptions();
      }
    },
    //获取焦点
    productNameFocus() {
      if (this.productOptions.length == 0) {
        this.initProductOptions();
      }
    },
    //涉及产品名称更改
    productNameChange(value) {
      let obj = this.productOptions.filter((item) => {
        return item.id == value;
      });
      if (value) {
        this.listByProductId();
      }

      this.productSelectOption = obj[0];
    },
    //涉及产品名称end

    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      window.sessionStorage.setItem("active", 2);
      this.$router.go(-1);
    },
    //获取医患服务方案
    listByProductId() {
      listByProductId(this.form.baseProductId, 5).then(({ data: res }) => {
        this.option.column[4].dicData = res.data;
        this.serviceProjectList = res.data;
        this.baseProductChange();
      });
    },
    //产品改变后判断是否还有该条模板或者方案
    baseProductChange() {
      let serviceProjectList = this.serviceProjectList.filter((item) => {
        return item.id == this.form.serviceProjectId;
      });
      //方案为空
      if (serviceProjectList.length == 0) {
        this.form.serviceProjectId = "";
      }
    },
    //清空
    handleReset() {
      this.option.column[4].dicData = [];
      this.serviceProjectList = [];
      this.option.column[5].dicData = [];
      this.templateNameList = [];
      this.form.projectTypeName = "用药反馈征集";
    },
    submit(form, done) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          form.invitationStartDate = form.invitationStartDate + " 00:00:00";
          form.invitationEndDate = form.invitationEndDate + " 23:59:59";
          form.province = this.form.$provinceCode; //所属省
          form.city = this.form.$cityCode; //所属市
          form.entrustedCompanyName = this.form.$entrustedCompanyId; //企业名称
          form.baseProductLine = this.productSelectOption.productLine; //产品线
          form.baseProductName = this.productSelectOption.productName; //产品名
          form.baseProductId = this.productSelectOption.id; //产品d
          form.baseEmployeeId = this.selectOption.id; //代表id
          form.baseEmployeeName = this.selectOption.name; //代表名字
          form.baseEmployeeNumber = this.selectOption.jobNumber; //代表工号
          form.baseDepartmentId = this.selectOption.entrustedDeptId; //部门id
          add(form).then(
            () => {
              this.$message({
                type: "success",
                message: "保存成功!",
              });
              done();

              this.goBack();
            },
            (error) => {
              done();
              console.log(error);
            }
          );
        }
      });
    },

    handleClick(tab) {
      if (tab.name == "1") {
        // this.getTenderDetail();
      }
    },
  },
};
</script>

<style></style>
