export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "编号",
      prop: "code",
      type: "input",
    },
    {
      label: "会员姓名",
      prop: "name",
      type: "input",
    },
    {
      label: "手机号码",
      prop: "phone",
      type: "input",
    },
    {
      label: "性别 (1 男 2 女)",
      prop: "sex",
      type: "input",
    },
    {
      label: "民族",
      prop: "nationality",
      type: "input",
    },
    {
      label: "籍贯",
      prop: "nativePlace",
      type: "input",
    },
    {
      label: "所属省",
      prop: "province",
      type: "input",
    },
    {
      label: "所属市",
      prop: "city",
      type: "input",
    },
    {
      label: "所属区",
      prop: "district",
      type: "input",
    },
    {
      label: "详细地址",
      prop: "address",
      type: "input",
    },
    {
      label: "毕业院校",
      prop: "graduateSchool",
      type: "input",
    },
    {
      label: "最高学历",
      prop: "education",
      type: "input",
    },
    {
      label: "医院名称/工作单位",
      prop: "hospitalName",
      type: "input",
    },
    {
      label: "所在科室（一级）",
      prop: "departmentName",
      type: "input",
    },
    {
      label: "所在科室(二级)",
      prop: "departmentTwoName",
      type: "input",
    },
    {
      label: "职称",
      prop: "professional",
      type: "input",
    },
    {
      label: "职务",
      prop: "duty",
      type: "input",
    },
    {
      label: "讲者级别（特殊 资深 高级 中级 普通）",
      prop: "speakerLevel",
      type: "input",
    },
    {
      label: "主治专长",
      prop: "speciality",
      type: "input",
    },
    {
      label:
        "证件类型（1身份证  2港澳通行证或居住证  3台湾通行证或居住证  4护照）",
      prop: "credentialType",
      type: "input",
    },
    {
      label: "身份证号",
      prop: "idCardNumber",
      type: "input",
    },
    {
      label: "身份证正面",
      prop: "idCardPositive",
      type: "input",
    },
    {
      label: "身份证反面",
      prop: "idCardNegative",
      type: "input",
    },
    {
      label: "港澳台通行证或居住证",
      prop: "residencePermit",
      type: "input",
    },
    {
      label: "海外护照个人资料页",
      prop: "passportPersonal",
      type: "input",
    },
    {
      label: "海外护照盖章页",
      prop: "passportSign",
      type: "input",
    },
    {
      label: "工牌/医师资格证/执业医师证 多张逗号分隔",
      prop: "doctorCertificateData",
      type: "input",
    },
    {
      label: "开户银行",
      prop: "bankName",
      type: "input",
    },
    {
      label: "银行预留手机号",
      prop: "bankPhone",
      type: "input",
    },
    {
      label: "银行账号",
      prop: "bankAccount",
      type: "input",
    },
    {
      label: "银行卡图片",
      prop: "bankImg",
      type: "input",
    },
    {
      label: "会员认证核准时间",
      prop: "approvalDate",
      type: "input",
    },
    {
      label: "会员认证核准人员",
      prop: "approvalOfficer",
      type: "input",
    },
    {
      label: "会员认证核准组织",
      prop: "approvalOrganization",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
