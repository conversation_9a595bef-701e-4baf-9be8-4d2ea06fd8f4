<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="primary"
          v-if="permission.baseProduct_import"
          @click="dialogVisible = true"
          >导入
        </el-button>
      </template>
    </avue-crud>
    <el-dialog
      title="产品目录导入"
      append-to-body
      :visible.sync="dialogVisible"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
      title="查看"
      @close="form = {}"
      append-to-body
      :visible.sync="viewDialogVisible"
      width="60%"
    >
      <avue-form :option="viewDialogOption" v-model="viewDialogForm">
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
} from "@/api/baseproduct/baseProduct";
import option from "@/const/baseproduct/baseProduct";
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";

export default {
  data() {
    return {
      //查看
      viewDialogVisible: false,
      viewDialogForm: {},
      viewDialogOption: {
        disabled: true,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: "100",
        column: [
          {
            label: "企业名称",
            prop: "entrustedCompanyId",
            type: "select",
            props: {
              label: "name",
              value: "id",
            },
            dicUrl: "/api/blade-csc/entrustedCompany/getList",
            rules: [
              {
                required: true,
                message: "请选择企业名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "产品名称",
            prop: "productName",
            type: "input",
            maxlength: 100,
            showWordLimit: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入产品名称",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "品种编码",
            prop: "varietyCode",
            type: "input",
            maxlength: 100,
            showWordLimit: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入品种编码",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "产品类别",
            prop: "productType",
            type: "input",
            maxlength: 100,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入产品类别",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "标准品规",
            prop: "productFormat",
            type: "input",
            maxlength: 100,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入标准品规",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "产品分类",
            prop: "productCategory",
            type: "input",
            maxlength: 100,
            showWordLimit: true,
            search: true,
            rules: [
              {
                required: true,
                message: "请输入产品分类",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "产品线",
            prop: "productLine",
            type: "input",
            maxlength: 100,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入产品线",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },
      //end
      dialogVisible: false,
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      excelForm: {},
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "模板上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            data: {
              isCovered: 1,
            },
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/blade-sys/baseProduct/import-product",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.baseProduct_add, false),
        viewBtn: this.vaildData(this.permission.baseProduct_view, false),
        delBtn: this.vaildData(this.permission.baseProduct_delete, false),
        editBtn: this.vaildData(this.permission.baseProduct_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    uploadAfter(res, done) {
      this.dialogVisible = false;
      this.$message({
        type: "success",
        message: "操作成功!",
      });
      this.refreshChange();
      done();
    },
    handleTemplate() {
      exportBlob(
        `/api/blade-sys/baseProduct/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, `产品目录模板.xlsx`);
      });
    },
    rowSave(row, done, loading) {
      row.entrustedCompanyName = this.form.$entrustedCompanyId; //企业名称
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      row.entrustedCompanyName = this.form.$entrustedCompanyId; //企业名称
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        getDetail(this.form.id).then((res) => {
          this.viewDialogVisible = true;
          this.viewDialogForm = res.data.data;
        });
      } else if ("edit" == type) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          done();
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
