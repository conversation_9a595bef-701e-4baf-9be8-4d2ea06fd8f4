export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  addBtn: false,
  viewBtn: false,
  delBtn: false,
  editBtn: false,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "内容id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "标题",
      prop: "title",
      type: "input",
      search: true,
    },
    {
      label: "内容",
      prop: "content",
      type: "input",
      hide: true,
    },
    {
      label: "初始浏览量",
      prop: "initBrowseNum",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "浏览量",
      prop: "browseNum",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "初始点赞量",
      prop: "initLikeNum",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "点赞量",
      prop: "likeNum",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "评论量",
      prop: "commentNum",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "转发量",
      prop: "transmitNum",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "封面",
      prop: "cover",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    // 0是 1否
    {
      label: "阅读权限",
      prop: "readAuthority",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    // 0是 1否
    {
      label: "是否评论",
      prop: "isComment",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    // 0是 1否
    {
      label: "是否推荐",
      prop: "isRecommend",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    // 0是 1否
    {
      label: "是否定时发布",
      prop: "isTimingPublish",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "发布人",
      prop: "createUserName",
      type: "input",
      search: true,
    },
    {
      label: "发布时间",
      prop: "publishTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    // 0 草稿 1待审核 2已发布 3已驳回 4已下架 5已通过
    {
      label: "状态",
      prop: "status",
      type: "input",
    },
    {
      label: "互动数据",
      prop: "interaction",
      type: "input",
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
