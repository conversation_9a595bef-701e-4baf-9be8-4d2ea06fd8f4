<template>
  <div>
    <basic-container>
      <el-row :gutter="20" style="display: flex; align-items: center">
        <el-col :span="10">
          <el-page-header @back="goBack" :content="$route.name">
          </el-page-header>
        </el-col>
        <el-col
          :span="14"
          v-if="$route.query.type != 'view'"
          style="text-align: right"
        >
        </el-col>
      </el-row>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
        @submit="submit"
      >
        <template slot-scope="{}" slot="orderDoctorCode">
          <div class="to-view" @click="toOrderDoctor()">
            <a readonly>
              {{ form.orderDoctorCode }}
            </a>
          </div>
        </template>
        <template slot-scope="{}" slot="doctorSettlementCode">
          <div class="to-view" @click="toDoctorSettlementCode()">
            <a readonly>
              {{ form.doctorSettlementCode }}
            </a>
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/orderdoctorpayment/orderDoctorPayment";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      //弹窗

      id: "",
      form: {},
      option: {
        disabled: true,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: "170",
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",
            column: [
              {
                label: "会员订单编号",
                prop: "orderDoctorCode",
                type: "input",
              },

              {
                label: "结算单编号",
                prop: "doctorSettlementCode",
                type: "input",
              },

              {
                label: "会员姓名",
                prop: "doctorName",
                type: "input",
              },
              {
                label: "会员手机号",
                prop: "doctorPhone",
                type: "input",
              },
              {
                label: "身份证号",
                prop: "doctorIdCard",
                type: "input",
              },
              {
                label: "银行卡号",
                prop: "doctorCardNo",
                type: "input",
              },
              {
                label: "支付金额",
                prop: "paymentAmount",
                type: "input",
              },
              {
                label: "支付时间",
                prop: "paymentTime",
                type: "date",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
                search: "true",
              },
              {
                label: "支付凭证",
                prop: "paymentEvidence",
                type: "input",
              },
              {
                label: "支付结果",
                prop: "paymentResult",
                type: "select",
                dicUrl:
                  "/api/blade-system/dict-biz/dictionary?code=payment_result",
                props: {
                  label: "dictValue",
                  value: "dictKey",
                },
                dataType: "number",
              },
            ],
          },
        ],
      },

      query: {},
      loading: false,

      data: [],
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission", "createEvidenceFileStatus"]),
  },
  methods: {
    toOrderDoctor() {
      this.$router.push({
        path: `/orderdoctor/detail/${this.form.orderDoctorId}`,
      });
    },
    // 结算单
    toDoctorSettlementCode() {
      this.$router.push({
        path: `/orderdoctosettlement/detail/${this.form.doctorSettlementId}`,
      });
    },

    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },

    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.avue-crud > h2 {
  font-size: 16px !important;
  font-weight: 500 !important;
}
</style>
