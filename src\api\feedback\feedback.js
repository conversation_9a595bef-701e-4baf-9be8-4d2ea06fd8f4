import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-feedback/feedback/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (feedbackId) => {
  return request({
    url: '/api/blade-feedback/feedback/detail',
    method: 'get',
    params: {
      feedbackId
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-feedback/feedback/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-feedback/feedback/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-feedback/feedback/submit',
    method: 'post',
    data: row
  })
}

