<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ type, size, row, index }" slot="enable">
        <div style="cursor: pointer">
          <el-switch
          v-if="permission.caseCollectionTemplate_stopTemplate"
            v-model="row.enable"
            @change="enableChange(row)"
            active-color="#67C23A"
            inactive-color="#cacdd4"
            active-text="启用"
            inactive-text="停用"
            :active-value="1"
            :inactive-value="0"
          >
          </el-switch>
          <div v-else>
            {{ row.enable == 1 ? "启用" : "停用" }}
          </div>
        </div>
      </template>
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button v-if="permission.caseCollectionTemplate_review&&row.confirmStatus==0" :size="option.size" type="text" @click="confirmChange(row.id, 1)" >确认</el-button>
        <el-button v-if="permission.caseCollectionTemplate_review&&row.confirmStatus==0" :size="option.size" type="text" @click="confirmChange(row.id, 2)" >拒绝</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove,
  stopTemplate,
  confirmTemplate,
} from "@/api/casecollectiontemplate/caseCollectionTemplate";
import option from "@/const/casecollectiontemplate/caseCollectionTemplate";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.caseCollectionTemplate_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.caseCollectionTemplate_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.caseCollectionTemplate_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.caseCollectionTemplate_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    enableChange(row) {
      console.log(row);
      // 只有已确认的数据可以进行启用/禁用操作
      if(row.confirmStatus!=1){
        this.$message({
          type: "error",
          message: "未确认数据无法进行启用/禁用操作!",
        });
        this.onLoad(this.page);
        return
      }
      stopTemplate(row.id, row.enable).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        },
        () => {
          row.enable = 1;
        }
      );
    },
    confirmChange(id, confirmStatus) {
      this.$confirm("是否确认此操作?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          confirmTemplate(id, confirmStatus).then(
            () => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!",
              });
            },
            () => {
              row.enable = 1;
            }
          );
        })
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    beforeOpen(done, type) {
      if (type == "add") {
        this.$router.push({
          path: `/casecollectiontemplate/add/`,
        });
      } else if (type == "edit") {
        this.$router.push({
          path: `/casecollectiontemplate/edit/${this.form.id}`,
        });
      } else if (type == "view") {
        this.$router.push({
          path: `/casecollectiontemplate/detail/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
