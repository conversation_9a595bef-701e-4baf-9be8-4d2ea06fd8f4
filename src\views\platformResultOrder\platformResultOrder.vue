<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      :searchslot="true"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="serviceManagerReport" slot-scope="{ row }">
        <el-button
          v-if="row.serviceManagerReport"
          type="text"
          @click="previewPDF(row.serviceManagerReport)"
          >预览报告
        </el-button>
        <div v-else>无</div>
      </template>
      <template slot="code" slot-scope="{ row }">
        <div class="to-view" @click="toResultorderDetail(row)">
          <a readonly>
            {{ row.code }}
          </a>
        </div>
      </template>
      <template slot-scope="{ row, size }" slot="menu">
        <template>
          <el-button
            v-if="
              permission.platformResultOrder_serviceManagementReport &&
              row.submitStatus == 2
            "
            :size="size"
            type="text"
            @click="Reporting(row)"
            >重新生成
          </el-button>
        </template>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList } from "@/api/resultorder/resultOrder";
import { createServiceManagerReport } from "@/api/platformResultOrder/platformResultOrder";
import option from "@/const/platformResultOrder/platformResultOrder";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      sponsorHandleStatus: false,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.resultOrder_add, false),
        viewBtn: this.vaildData(this.permission.resultOrder_view, false),
        delBtn: this.vaildData(this.permission.resultOrder_delete, false),
        editBtn: this.vaildData(this.permission.resultOrder_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    toResultorderDetail(row) {
      this.$router.push({
        path: `/resultorder/detail/${row.id}`,
      });
    },
    previewPDF(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    searchReset() {
      this.startTime = [];
      this.endTime = [];
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      delete params.actStartTime;
      delete params.actEndTime;
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);

      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.submitStatus = 2;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },

    // 生成服务管理报告
    Reporting(row) {
      this.$message({
        message: "生成服务管理报告中请稍后",
        duration: 0,
      });
      createServiceManagerReport(row.id).then(
        (res) => {
          if (res.data.success) {
            this.$message.closeAll();
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.onLoad(this.page);
          }
        },
        (err) => {
          console.log(err);
          setTimeout(() => {
            this.$message.closeAll();
          }, 2000);
        }
      );
    },
  },
};
</script>

<style></style>
