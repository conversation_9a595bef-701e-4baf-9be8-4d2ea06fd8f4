import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-csc/customerCertificationRecord/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-csc/customerCertificationRecord/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-csc/customerCertificationRecord/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

//审核状态 2 审核通过 3审核不通过
export const audit = (data) => {
  return request({
    url: "/api/blade-csc/customerCertificationRecord/audit",
    method: "post",
    data,
  });
};

//重发人脸认证短信
export const retryFaceSms = (doctorId) => {
  return request({
    url: "/api/blade-csc/customerCertificationRecord/retryFaceSms",
    method: "get",
    params: {
      doctorId,
    },
  });
};
