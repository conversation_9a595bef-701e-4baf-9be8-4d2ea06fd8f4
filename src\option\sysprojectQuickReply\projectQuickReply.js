export const moduleOptions = [
  { value: 'public', label: '不限定项目' },
  { value: 'professionalReviewReport', label: '专业评审' },
  { value: 'meeting', label: '学术会议' },
  { value: 'lecture_1', label: '大讲堂' },
  { value: 'lecture_2', label: '专业录播' },
  { value: 'lecture_3', label: '微学堂' },
  { value: 'lecture_4', label: '杏林集萃' },
  { value: 'caseCollectionReport', label: '病例征集' },
  { value: 'medicationFeedbackReport', label: '用药反馈' },
  { value: 'clinicalResearchReport', label: '临床调研' },
  { value: 'customerCertificationRecord', label: '会员认证' },
]


export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "项目",
      prop: "moduleCode",
      type: "select",
      dicData: moduleOptions,
    },
    {
      label: "快捷回复内容",
      prop: "content",
      type: "input",
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态 ",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
