export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "统计数据类型 1企业 2会员",
      prop: "type",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "企业成果单id",
      prop: "resultOrderId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "医生会员id",
      prop: "doctorId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "服务项目类型名称",
      prop: "projectTypeName",
      type: "input",
    },
    {
      label: "产品名",
      prop: "baseProductName",
      type: "input",
    },
    {
      label: "产品线",
      prop: "baseProductLine",
      type: "input",
    },
    {
      label: "服务项目方案名称",
      prop: "serviceProjectName",
      type: "input",
    },
    {
      label: "个人服务单价（服务承包单价-税后）",
      prop: "taskUnitPrice",
      type: "input",
    },
    {
      label: "企业结算单价（企业委托单价-税后）",
      prop: "entrustPrice",
      type: "input",
    },
    {
      label: "任务计划数量",
      prop: "planNumber",
      type: "input",
    },
    {
      label: "任务完成数量",
      prop: "finishNumber",
      type: "input",
    },
    {
      label: "任务验收数量",
      prop: "resultNumber",
      type: "input",
    },
    {
      label: "个人结算金额",
      prop: "doctorAmount",
      type: "input",
    },
    {
      label: "企业结算金额",
      prop: "entrustAmount",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态 ",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
