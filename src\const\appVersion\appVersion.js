export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  labelWidth: 150,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "应用编号",
      prop: "appCode",
      type: "input",
      search: true,
    },
    {
      label: "应用名称",
      prop: "appName",
      type: "input",
    },

    {
      label: "应用类型",
      search: true,
      prop: "appType",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=app_type",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
    },
    {
      label: "应用版本号",
      prop: "versions",
      type: "input",
      search: true,
    },
    {
      label: "内部版本号",
      prop: "buildVersion",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "发布版本号",
      prop: "releaseVersion",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "下载地址",
      prop: "apkLink",
      type: "input",
    },
    {
      label: "内测时间",
      prop: "buildDate",
      type: "date",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "发版时间",
      prop: "releaseDate",
      type: "date",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      hide: true,
    },
    {
      //：0非强制更新 1强制更新
      label: "更新机制",
      prop: "updateScheme",
      type: "radio",
      dicData: [
        {
          label: "非强制更新",
          value: 0,
        },
        {
          label: "强制更新",
          value: 1,
        },
      ],
      hide: true,
    },
    {
      label: "更新说明",
      prop: "releaseNote",
      type: "textarea",
      hide: true,
    },
    {
      label: "详细描述",
      prop: "description",
      type: "textarea",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
