<template>
  <div>
    <basic-container><el-page-header @back="goBack" :content="$route.name"></el-page-header></basic-container>
    <basic-container>
      <iframe
        :src="pdfUrl"
        width="100%"
        :height="iframeHeight"
        title="预览"
        frameBorder="no"
        border="0"
        marginWidth="0"
        marginHeight="0"
        scrolling="no"
        allowTransparency="yes"
      ></iframe>
    </basic-container>
  </div>
</template>

<script>
export default {
  data() {
    return {
      pdfUrl: '',
      iframeHeight: window.innerHeight - 280,
  };
  },
  created() {
    this.iframeHeight = window.innerHeight - 280;
    this.pdfUrl = this.$route.query.pdfUrl;
  },

  methods: {
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },

}
}
</script>

<style lang="scss"></style>
