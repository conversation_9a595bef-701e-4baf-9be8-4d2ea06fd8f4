import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/orderEntrusted/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (orderEntrustedId) => {
  return request({
    url: "/api/blade-svc/orderEntrusted/detail",
    method: "get",
    params: {
      orderEntrustedId,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-svc/orderEntrusted/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-svc/orderEntrusted/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/orderEntrusted/submit",
    method: "post",
    data: row,
  });
};

export const getAssociationList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/orderEntrustedRel/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
