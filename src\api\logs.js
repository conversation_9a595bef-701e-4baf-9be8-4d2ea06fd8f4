import request from "@/router/axios";

export const getUsualList = (current, size,params) => {
  return request({
    url: "/api/blade-log/usual/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getApiList = (current, size, params) => {
  return request({
    url: "/api/blade-log/api/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getErrorList = (current, size) => {
  return request({
    url: "/api/blade-log/error/list",
    method: "get",
    params: {
      current,
      size,
    },
  });
};

export const getUsualLogs = (id) => {
  return request({
    url: "/api/blade-log/usual/detail",
    method: "get",
    params: {
      id,
    },
  });
};
export const getApiLogs = (id) => {
  return request({
    url: "/api/blade-log/api/detail",
    method: "get",
    params: {
      id,
    },
  });
};
export const getErrorLogs = (id) => {
  return request({
    url: "/api/blade-log/error/detail",
    method: "get",
    params: {
      id,
    },
  });
};
