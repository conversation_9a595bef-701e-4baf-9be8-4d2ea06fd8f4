import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/summaryTaxReturns/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/summaryTaxReturns/detail",
    method: "get",
    params: {
      id,
    },
  });
};

//生成月度纳税申报单
export const createSummaryTaxReturns = () => {
  return request({
    url: "/api/blade-svc/summaryTaxReturns/createSummaryTaxReturns",
    method: "get",
    params: {},
  });
};

export const remove = (id) => {
  return request({
    url: "/api/blade-svc/summaryTaxReturns/remove",
    method: "post",
    params: {
      id,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-svc/summaryTaxReturns/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/summaryTaxReturns/submit",
    method: "post",
    data: row,
  });
};

//上传凭证
export const updateTaxCertStatus = (params) => {
  return request({
    url: "/api/blade-svc/summaryTaxReturns/updateTaxCertStatus",
    method: "get",
    params,
  });
};

//客户结算
export const create = (id) => {
  return request({
    url: "/api/blade-svc/summaryTaxReturns/create",
    method: "get",
    params: { id },
  });
};
