<template>
  <div class="header">
    <div>
      <el-row :gutter="10" justify="space-between">
        <el-col :span="6" v-for="(item, index) in list" :key="index">
          <div class="header-item">
            <div class="item-left">
              <div>{{ item.title }}</div>
              <!-- <div class="num" v-if="index < 2">
                {{ form[item.num] }}<span class="unit">万</span>
              </div> -->
              <div class="num">
                {{ form[item.num] }}<span class="unit">元</span>
              </div>
            </div>
            <div class="item-right"><img :src="item.iconUrl" alt="" /></div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
export default {
  name: "homeHeader",
  props: {
    form: {
      type: Object,
      required: {},
    },
  },
  data: () => {
    return {
      list: [
        {
          iconUrl: require("@/assets/images/home/<USER>"),
          title: "企业订单额",
          num: "entrustedAmount",
        },
        {
          iconUrl: require("@/assets/images/home/<USER>"),
          title: "个人订单额",
          num: "personAmount",
        },
        {
          iconUrl: require("@/assets/images/home/<USER>"),
          title: "纳税金额",
          num: "taxAmount",
        },
        {
          iconUrl: require("@/assets/images/home/<USER>"),
          title: "平台净收入",
          num: "platformAmount",
        },
      ],
    };
  },
  mounted() {},
  methods: {},

  beforeUnmount() {},
};
</script>

<style scoped lang="scss">
.el-col {
  margin-bottom: 0 !important;
}
.header {
  height: 102px;
  width: 100%;
  margin-bottom: 12px;
}

.header-item {
  display: flex;
  height: 102px;
  border-radius: 8px;
  background: #ffffff;
  justify-content: space-between;
  align-items: center;
}
.item-left {
  min-width: 96px;
  height: 78px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  margin-left: 30px;
}
.unit {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
}
.item-left > :nth-child(1) {
  text-align: left;
  font-size: 16px;
  font-weight: 400;
  color: #000000;
  line-height: 22px;
}
.item-left > :nth-child(2) {
  text-align: left;
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  line-height: 28px;
}
.item-right {
  width: 78px;
  height: 78px;
  margin-right: 62px;
  img {
    width: 100%;
  }
}
</style>
