import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/clinicalResearchTemplate/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/clinicalResearchTemplate/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/clinicalResearchTemplate/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/clinicalResearchTemplate/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/clinicalResearchTemplate/submit",
    method: "post",
    data: row,
  });
};
//1启用 0停用
export const updateStatus = (id, status) => {
  return request({
    url: "/api/blade-act/clinicalResearchTemplate/updateStatus",
    method: "get",
    params: {
      id,
      status,
    },
  });
};
