export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  labelWidth: 150,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  viewBtnIcon: " ",
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "编码",
      prop: "code",
      type: "input",
      search: true,
    },

    {
      label: "客户名称",
      prop: "enterpriseName",
      type: "input",
      search: true,
      searchLabelWidth: "100",
    },
    {
      label: "统一社会信用代码",
      prop: "socialCreditCode",
      type: "input",
      search: true,
      searchLabelWidth: 150,
    },
    {
      label: "企业结算单编号",
      prop: "settlementCode",
      type: "input",
      search: true,
      searchLabelWidth: "120",
    },
    // {
    //   label: "企业结算单名称",
    //   prop: "settlementName",
    //   type: "input",
    // },
    {
      label: "服务开始日期",
      prop: "actStartTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: false,
      searchRange: true,
    },
    {
      label: "服务结束日期",
      prop: "actEndTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: false,
      searchRange: true,
    },
    {
      label: "净收入金额",
      prop: "svcFeeAmount",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入服务费金额",
          trigger: "blur",
        },
      ],
    },
    {
      label: "支付状态",
      prop: "payStatus",
      type: "input",
    },

    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
