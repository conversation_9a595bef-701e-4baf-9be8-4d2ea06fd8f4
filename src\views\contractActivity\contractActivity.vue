<template>
  <basic-container>
    <avue-crud
      :option="option"
      :search.sync="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button type="text" @click="viewBtn(row)">查看 </el-button>
      </template>
    </avue-crud>
    <el-dialog
      title="详情"
      @close="viewDialogForm = {}"
      append-to-body
      destroy-on-close
      :visible.sync="viewDialogVisible"
      width="60%"
    >
      <avue-form ref="form" :option="viewDialogOption" v-model="viewDialogForm">
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  getRetroactiveDetail,
} from "@/api/contractActivity/contractActivity";
import option from "@/option/contractActivity/contractActivity";
import { mapGetters } from "vuex";
import "nprogress/nprogress.css";
import dayjs from "dayjs";

export default {
  data() {
    return {
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      viewDialogVisible: false,
      viewDialogForm: {},
      viewDialogOption: {
        labelWidth: "150",
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "活动名称",
            prop: "name",
            placeholder: " ",
            type: "input",
            disabled: true,
          },
          {
            label: "协议签署状态",
            prop: "agreementStatus",
            type: "select",
            disabled: true,
            searchLabelWidth: 100,
            dicData: [
              {
                label: "待签署",
                value: 0,
              },
              {
                label: "同意",
                value: 1,
              },
              {
                label: "拒绝",
                value: 2,
              },
              {
                label: "作废",
                value: 3,
              },
            ],
          },

          {
            label: "活动计划开始时间",
            prop: "invitationStartDate",
            placeholder: " ",
            type: "input",
            disabled: true,
          },
          {
            label: "活动计划结束时间",
            prop: "invitationEndDate",
            placeholder: " ",
            type: "input",
            disabled: true,
          },
          {
            label: "产品名",
            prop: "baseProductName",
            placeholder: " ",
            type: "input",
            disabled: true,
          },
          {
            label: "业务代表部门名称",
            prop: "baseDepartmentName",
            placeholder: " ",
            type: "input",
            disabled: true,
          },
          {
            label: "企业客户名称",
            prop: "entrustedCompanyName",
            placeholder: " ",
            type: "input",
            disabled: true,
          },
          {
            label: "协议签署时间",
            prop: "agreementTime",
            placeholder: " ",
            type: "input",
            disabled: true,
          },
          {
            label: "签署附件",
            prop: "imageUrl",
            listType: "picture-img",
            type: "upload",
            dataType: "string",
            disabled: true,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        viewBtn: this.vaildData(this.permission.contractActivity_view, false),
      };
    },
  },
  methods: {
    viewBtn(row) {
      if (row.contractType == 1) {
        getDetail({ activeId: row.id }).then((res) => {
          this.viewDialogForm = res.data.data;
          this.viewDialogVisible = true;
        });
      } else if (row.contractType == 2) {
        getRetroactiveDetail({ activeId: row.id }).then((res) => {
          this.viewDialogForm = res.data.data;
          this.viewDialogVisible = true;
        });
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },

    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;

      const { name, baseProductName, agreementStatus } = this.query;
      let invitationStartDateStart = undefined;
      let invitationStartDateEnd = undefined;
      let invitationEndDateStart = undefined;
      let invitationEndDateEnd = undefined;
      if (
        this.query.invitationStartDate &&
        this.query.invitationStartDate.length > 0
      ) {
        invitationStartDateStart = dayjs(
          this.query.invitationStartDate[0]
        ).format("YYYY-MM-DD 00:00:00");
        invitationStartDateEnd =
          dayjs(this.query.invitationStartDate[1]).format("YYYY-MM-DD") +
          " 23:59:59";
      }
      if (
        this.query.invitationEndDate &&
        this.query.invitationEndDate.length > 0
      ) {
        invitationEndDateStart =
          dayjs(this.query.invitationEndDate[0]).format("YYYY-MM-DD") +
          " 00:00:00";
        invitationEndDateEnd =
          dayjs(this.query.invitationEndDate[1]).format("YYYY-MM-DD") +
          " 23:59:59";
      }
      console.log(invitationStartDateStart, invitationStartDateEnd);
      let values = {
        name: name,
        invitationStartDateStart,
        invitationStartDateEnd,
        invitationEndDateStart,
        invitationEndDateEnd,
        baseProductName: baseProductName,
        agreementStatus: agreementStatus,
      };

      getList(page.currentPage, page.pageSize, values).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
        console.log(321);
      });
    },
  },
};
</script>

<style></style>
