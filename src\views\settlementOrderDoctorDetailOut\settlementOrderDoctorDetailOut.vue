<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template
        slot-scope="{ type, size, row, index }"
        slot="settlementOrderCode"
      >
        <div
          class="to-view"
          @click="toSettlementOrder(row.settlementOrderId)"
          v-if="row.settlementOrderCode"
        >
          <a readonly>
            {{ row.settlementOrderCode }}
          </a>
        </div>
        <div v-else>无</div>
      </template>
      <!-- 个人成果单编号 -->
      <template
        slot-scope="{ type, size, row, index }"
        slot="resultOrderDoctorCode"
      >
        <div
          class="to-view"
          @click="toResultOrderDoctor(row.resultOrderDoctorId)"
          v-if="row.resultOrderDoctorCode"
        >
          <a readonly>
            {{ row.resultOrderDoctorCode }}
          </a>
        </div>
        <div v-else>无</div>
      </template>

      <template slot-scope="{ type, size, row, index }" slot="doctorName">
        <div
          class="to-view"
          @click="toAuthenticationDoctorView(row.doctorId)"
          v-if="row.doctorName"
        >
          <a readonly>
            {{ row.doctorName }}
          </a>
        </div>
        <div v-else>无</div>
      </template>
      <template
        slot-scope="{ type, size, row, index }"
        slot="serviceResultFile"
      >
        <div style="cursor: pointer" v-if="row.serviceResultFile">
          <el-tag @click="preview(row.serviceResultFile)"
            >预览成果单文件</el-tag
          >
        </div>
        <div v-else>无</div>
      </template>
      <template slot-scope="{ row, size }" slot="menu"> </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList } from "@/api/settlementOrderDoctorDetailOut/settlementOrderDoctorDetailOut";
import option from "@/const/settlementOrderDoctorDetailOut/settlementOrderDoctorDetailOut";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      sponsorHandleStatus1: false,
      sponsorHandleStatus2: false,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/settlementOrderDoctorDetailOut/detail/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    //去会员详情
    toAuthenticationDoctorView(id) {
      this.$router.push({
        path: `/authenticationDoctor/detail/${id}/1`,
      });
    },
    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    toSettlementOrder(id) {
      this.$router.push({
        path: `/settlementOrderDoctor/detail/${id}`,
      });
    },
    toResultOrderDoctor(id) {
      this.$router.push({
        path: `/resultorderdetail/detail/${id}`,
      });
    },
  },
};
</script>

<style></style>
