import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/lecture/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
//个人订单管理
export const orderDoctorList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/lecture/orderDoctorList",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
export const getListPage = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/lecture/page",
    method: "post",
    data: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/demo/lecture/detail",
    method: "get",
    params: {
      id,
    },
  });
};
export const stopLecture = (id, status) => {
  return request({
    url: "/api/blade-act/demo/lecture/stopLecture",
    method: "get",
    params: {
      id,
      status,
    },
  });
};

export const getFileList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/lectureContent/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const remove = (id) => {
  return request({
    url: "/api/blade-act/demo/lecture/remove",
    method: "post",
    params: {
      id,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/demo/lecture/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/demo/lecture/submit",
    method: "post",
    data: row,
  });
};

export const audit = (row) => {
  return request({
    url: "/api/blade-act/demo/lecture/audit",
    method: "post",
    data: row,
  });
};
//重新开始
export const startPlanAgain = (id) => {
  return request({
    url: "/api/blade-act/demo/lecture/startPlanAgain",
    method: "get",
    params: {
      id,
    },
  });
};


//获取已经邀请会员
export const getDoctorList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/lectureDoctor/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

//删除已经邀请会员
export const deleteDoctor = (id) => {
  return request({
    url: "/api/blade-act/demo/lectureDoctor/deleteDoctor",
    method: "post",
    params: {
      id,
    },
  });
};
//获取已经选择会员id
export const getExistDoctorIds = (lectureId) => {
  return request({
    url: "/api/blade-act/demo/lectureDoctor/getExistDoctorIds",
    method: "get",
    params: {
      lectureId,
    },
  });
};

//修改计划收集份数
export const savePlanSearchNum = (id, planSearchNum) => {
  return request({
    url: "/api/blade-act/demo/lectureDoctor/savePlanSearchNum",
    method: "post",
    params: {
      id,
      planSearchNum,
    },
  });
};

export const saveList = (data) => {
  return request({
    url: "/api/blade-act/demo/lectureDoctor/saveList",
    method: "post",
    data,
  });
};
