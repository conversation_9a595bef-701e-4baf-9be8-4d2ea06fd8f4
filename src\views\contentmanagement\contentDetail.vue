<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
      <div
        v-if="form.status == 1"
        class="button"
      >
        <el-button
          type="primary"
          @click="onAudit(0)"
        >通过并发布</el-button>
        <el-button
          type="danger"
          @click="onAudit(1)"
        >驳回</el-button>
      </div>
      <div
        v-if="type == 3 && form.activityRelated == 1"
        class="button"
      >
        <el-button
          type="primary"
          @click="viewTask"
        >查看任务</el-button>
      </div>
    </basic-container>
    <div class="content-detail-container">
      <basic-container class="el-card__body detail-content">
        <el-alert
          v-if="illegalContent"
          :title="illegalContent"
          type="error">
        </el-alert>
        <h1 class="content-detail-title">
        {{ form.title }}
      </h1>
        <p class="publish-info">
          <span class="publish-user">{{ form.createUserName }}</span>
          <span class="publish-time">
        <template v-if="!loaded">
          <el-skeleton />
        </template>
        <template v-else>
          <template v-if="form.status != 2">
          <el-tag v-if="form.status == 0" type="info" size="mini">草稿</el-tag>
          <el-tag v-if="form.status == 1" type="warning" size="mini">待审核</el-tag>
          <el-tag v-if="form.status == 3" type="danger" size="mini">已驳回</el-tag>
          <el-tag v-if="form.status == 4" type="info" size="mini">已下架</el-tag>
          <el-tag v-if="form.status == 5" type="success" size="mini">已通过</el-tag>
          </template>
          <span v-else>发布于{{  form.publishTime }}</span>
        </template>
      </span>
        </p>
        <content-render :value="form.content" />
      </basic-container>
      <basic-container v-if="approvalList.length > 0" class="el-card__body detail-approve">
        <h3 class="approval-record-title">
          审核记录
        </h3>
        <el-timeline>
          <el-timeline-item v-for="item in approvalList" :timestamp="item.status === 0 ? '通过' : '驳回'" placement="top">
            <h4 v-if="item.groundsForRejection">
              {{ item.groundsForRejection }}
            </h4>
            <p>{{ item.createUserName  }} 审核于 {{ item.createTime }}</p>
          </el-timeline-item>
        </el-timeline>
      </basic-container>
    </div>
    <el-dialog
      title="驳回"
      @close="auditForm = {}"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="auditDialogVisible"
      width="30%"
    >
      <avue-form
        v-if="auditDialogVisible"
        ref="auditForm"
        :option="auditOption"
        v-model="auditForm"
      >
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取 消</el-button>
        <el-button :loading="auditLoading" type="primary" @click="auditConfirm"
        >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {getDetail, audit, getApprovalDetail} from "@/api/contentManagement/contentManagement";
import ContentRender from "@/components/content-editor/render.vue";


export default {
  components: {ContentRender},
  data() {
    return {
      illegalContent:null,
      id: null,
      loaded: false,
      type: null,
      approvalList: [],
      form: {
        title: "",
        content: "",
        createUserName: "",
        publishTime: "",
        status: "",
        activityRelated: "",
        activityReportId: "",
        activityType: ""
      },
      auditLoading: false,
      auditDialogVisible: false,
      auditForm: { groundsForRejection: "" },
      auditOption: {
        submitText: "完成",
        span: 24,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "驳回理由",
            prop: "groundsForRejection",
            type: "textarea",
            maxlength: 500,
            showWordLimit: true,
            rules: [
              {
                required: true,
                message: "请输入驳回理由",
                trigger: ["blur", "change"],
              },
            ],
          },
          // {
          //   label: "快捷回复",
          //   prop: "quickReplyList",
          //   type: "input",
          // },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.type = this.$route.query.type;
    this.getDetail()
    this.getApprovalList()
  },
  watch: {
  },
  computed: {
  },
  methods: {
    goBack() {
      this.$router.$avueRouter.closeTag();
      window.sessionStorage.setItem("activeContentType", this.type);
      this.$router.go(-1);
    },
    getDetail() {
      let _this = this;
      getDetail(_this.id).then((res) => {
        const data = res.data;
        this.form.title = data.data.title
        this.form.content = JSON.parse(data.data.content)
        this.form.status = data.data.status
        this.form.createUserName = data.data.createUserName
        this.form.publishTime = data.data.publishTime
        this.form.activityRelated = data.data.activityRelated
        this.form.activityReportId = data.data.activityReportId
        this.form.activityType = data.data.activityType
        this.parseIllegalContent(data.data.illegalContent)
        this.loaded  = true
      });
    },
    getApprovalList() {
      getApprovalDetail(this.id).then(res => {
        this.approvalList = res.data.data || []
      })
    },
    onAudit(auditStatus) {
      let _this = this;
      if (auditStatus === 0) {
        this.$confirm("确定将该条数据审核通过?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.handleAudit(auditStatus)
          })
      } else {
        this.auditDialogVisible = true;
      }
    },
    auditConfirm() {
      this.$refs.auditForm.validate((valid) => {
        if(valid) {
          this.handleAudit(1)
        }
      })
    },
    async handleAudit(status) {
      const params = { status, id: this.id }
      if(status === 1) {
        params.groundsForRejection = this.auditForm.groundsForRejection
      }
      audit(params).then(
        () => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.goBack()
        }
      );
    },
    parseIllegalContent(illegalContent) {
      if(!illegalContent) {
        return
      }
      try{
        const illegalContentArr = JSON.parse(illegalContent)
        this.illegalContent = illegalContentArr.length > 0
          ? illegalContentArr.filter(item => item.moderationResult.segments.length > 0).map(item => `${ item.type }存在敏感词 ${item.moderationResult.segments.join('、')}`)
                              .join('; ')
          : null
      }catch (e) {
        console.warn('parse illegalContent error', e)
      }
    },
    viewTask(row) {
      this.$router.push({
        path: `/lecture_${this.form.activityType}/detail/${this.form.activityReportId}`,
      });
    }
  },
};
</script>

<style scoped lang="scss">
.content-detail-title {
  font-size: 24px;
}
.publish-info {

}
.publish-user {
  color: #1d90a2;
  margin-right: 20px;
}
.publish-time {
  color: #ababab;
}
.el-card__body {
  position: relative;
  padding-top: 0;
}
.button {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 99;
}
.content-detail-container {
  display: flex;
}
.detail-content {
  flex: 1;
}
.detail-approve {
  width: 400px;
}
.approval-record-title {
  color: #1D90A2;
}
</style>
