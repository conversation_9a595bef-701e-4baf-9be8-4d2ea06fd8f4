{"name": "beehive-vue-tax", "version": "3.2.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "publish": "yarn run build && node publish.js", "prod": "yarn run build && node publish-prod.js", "pretest": "yarn run build && node publish-pretest.js"}, "dependencies": {"avue-plugin-ueditor": "^0.1.4", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "classlist-polyfill": "^1.2.0", "compressing": "^1.10.0", "crypto-js": "^4.0.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-ui": "^2.15.14", "js-base64": "^2.5.1", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "mockjs": "^1.0.1-beta3", "node-gyp": "^5.0.6", "nprogress": "^0.2.0", "portfinder": "^1.0.23", "save": "^2.9.0", "script-loader": "^0.7.2", "ssh2": "^1.14.0", "vue": "^2.6.10", "vue-axios": "^2.1.2", "vue-clipboard2": "^0.3.3", "vue-i18n": "^8.7.0", "vue-lottie": "^0.2.1", "vue-pdf": "^4.3.0", "vue-router": "^3.0.1", "vuex": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "chai": "^4.1.2", "sass": "^1.64.2", "sass-loader": "^10.2.0", "vue-template-compiler": "^2.5.17", "webpack-bundle-analyzer": "^3.0.3"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}