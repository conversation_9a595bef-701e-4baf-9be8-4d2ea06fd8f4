<template>
  <div>
    <basic-container>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div>
          <el-page-header
            @back="goBack"
            :content="$route.name"
          ></el-page-header>
        </div>
      </div>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
      >
        <template slot-scope="{ disabled, size }" slot="serviceSettlementFile">
          <div style="cursor: pointer">
            <el-tag @click="preview(form.serviceSettlementFile)"
              >预览《结算单报告》</el-tag
            >
          </div>
        </template>
        <template slot-scope="{}" slot="settlementOrderCode">
          <div
            class="to-view"
            @click="toSettlementOrder(form.settlementOrderId)"
          >
            <a readonly>
              {{ form.settlementOrderCode }}
            </a>
          </div>
        </template>
        <!-- 自然人成果单编号 -->
        <template slot-scope="{}" slot="resultOrderDoctorCode">
          <div
            class="to-view"
            @click="toResultOrderDoctor(form.resultOrderDoctorId)"
          >
            <a readonly>
              {{ form.resultOrderDoctorCode }}
            </a>
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/settlementOrderDoctorDetailOut/settlementOrderDoctorDetailOut";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      dialogVisible: false,
      filetType: 1,
      fileUrl: "",
      fileUrlList: [],
      id: "", //数据id
      invoiceItems: {}, //发票项目明细
      showItems: false,
      form: {},
      query: {},
      loading: false,
      //基础信息
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "基本信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "自然人结算单编号",
                prop: "code",
                type: "input",
              },
              {
                label: "企业结算单编号",
                prop: "settlementOrderCode",
                type: "input",
              },
              {
                label: "自然人成果单编号",
                prop: "resultOrderDoctorCode",
                type: "input",
              },
              {
                label: "自然人结算单名称",
                prop: "settlementDetailName",
                type: "input",
              },
              {
                label: "会员姓名",
                prop: "doctorName",
                type: "input",
              },

              {
                label: "证件号码",
                prop: "idCardNumber",
                type: "input",
              },
              {
                label: "结算金额",
                prop: "settlementAmount",
                type: "input",
              },
              {
                label: "结算单报告",
                prop: "serviceSettlementFile",
                type: "upload",
                // listType: 'picture-img',
                dataType: "string",
                action: "/api/blade-resource/oss/endpoint/put-file",
                tip: "",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail(); //获取详情
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("报告为空无法预览");
      }
    },
    toSettlementOrder(id) {
      this.$router.push({
        path: `/settlementOrderDoctor/detail/${id}`,
      });
    },
    toResultOrderDoctor(id) {
      this.$router.push({
        path: `/resultorderdetail/detail/${id}`,
      });
    },
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    // 获取数据详情
    getDetail() {
      getDetail(this.id).then(
        (res) => {
          let data = res.data;
          if (data.success) {
            this.form = data.data;
          } else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        },
        (error) => {
          window.console.log(error);
        }
      );
    },
  },
};
</script>

<style></style>
