export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "活动计划ID",
      prop: "activityPlanId",
      type: "input",
    },
    {
      label: "计划数量",
      prop: "planNumber",
      type: "input",
    },
    {
      label: "km认证会员ID",
      prop: "doctorId",
      type: "input",
    },
    {
      label: "已完成数量",
      prop: "planCompleteNumber",
      type: "input",
    },
    {
      label: "活动计划类型",
      prop: "activityPlanType",
      type: "input",
    },
    {
      label: "发起时间",
      prop: "initiationDate",
      type: "input",
    },
    {
      label: "待办计划标题",
      prop: "activityPlanName",
      type: "input",
      span: 24,
      search: true,
      rules: [
        {
          required: true,
          message: "请输入待办计划标题",
          trigger: "blur",
        },
      ],
    },
    {
      label: "活动计划状态 ",
      prop: "planStatus",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
