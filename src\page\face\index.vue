<template>
  <div class="face" style="height: 100%; overflow-y: auto; overflow-x: hidden">
    <div class="face-tips">
      <Lottie
        :options="options"
        class="lottie-calss"
        loop
        :height="110"
        :width="110"
        @animCreated="handleAnimation"
      />
      <div>
        请进行人脸识别验证身份并确保 <b>{{ form.name }}</b> 本人操作
      </div>
    </div>
    <div v-if="loading" class="face-list">
      <el-skeleton :rows="5" animated />
    </div>
    <div class="face-list" v-else>
      <div class="item">
        <div class="label">姓名：</div>
        <div class="value">{{ form.name }}</div>
      </div>

      <div class="item">
        <div class="label">身份证号：</div>
        <div class="value">{{ form.idCardNumber }}</div>
      </div>
      <div class="item">
        <div class="label">手机号码：</div>
        <div class="value">{{ form.phone }}</div>
      </div>
      <div class="item">
        <div class="label">医院：</div>
        <div class="value">{{ form.hospitalName }}</div>
      </div>
      <div class="item">
        <div class="label">科室：</div>
        <div class="value">{{ form.departmentName }}</div>
      </div>
    </div>

    <el-alert
      title="请确认显示的信息是否一致，确认无误后点击开始验证继续"
      type="warning"
      :closable="false"
      class="tips"
    >
    </el-alert>
    <div class="btn">
      <div class="checkbox">
        <el-checkbox v-model="checked"></el-checkbox>阅读并同意<span
          class="ptx"
          @click.stop="showdrawer"
          >《实名认证用户隐私协议》</span
        >
      </div>
      <el-button
        type="primary"
        :disabled="loading"
        round
        class="button-class"
        @click="verifyBtn"
        >开始验证</el-button
      >
    </div>
    <div style="height: 40vh"></div>
    <el-drawer
      title="用户隐私协议"
      :visible.sync="drawer"
      direction="btt"
      show-close
      size="80%"
    >
      <div class="drawer-box">
        您（即授权人）正在使用【连云港亿辰信息科技有限责任公司】（以下称“业务办理机构”）为您提供的【元圈云DR小程序】（以下称“业务办理机构应用”），如您需使用【小程序完整功能】，您需按照业务办理机构的要求完成实名认证。
        <br />受业务办理机构之委托，北京百度网讯科技有限公司（以下简称“百度”）将为您提供实名认证核验服务（以下简称“本服务”），具体以您在业务办理机构应用相关页面选择的为准。为了保障您的合法权益，请您务必事先审慎阅读、充分理解授权书条款内容。如您不同意本授权书，请勿使用本服务。
        <br /><br />1.在您点击同意本授权书后，即表示您已详细阅读本授权书条款，您同意授权百度收集您的如下信息，并分享给为本服务提供必要技术支持的身份认证服务商（以下统称“被授权人”），以便被授权人处理您的相关信息，完成身份认证核验服务。<br />
        <br />（1）实名认证（包含人脸实名认证、人脸比对）服务：获取并使用您的姓名、身份证号码、人脸照片及视频信息，请求权威数据源进行信息核验，以便实现身份信息的比对。同时，为了确认您在进行认证时是否处于可信环境，识别和排除恶意请求、维护服务安全，还将获取并使用您的设备信息、浏览器信息、网络信息和操作记录信息。<br />
        <br />（2）身份证识别服务：获取并使用您的身份证照片信息、操作记录信息，通过OCR技术提取证件上的相关信息。<br /><br />
        2.第1条所述信息是完成实名认证核验服务所必须的信息，如您拒绝提供的，被授权人将无法为您提供本服务，但不影响业务办理机构为您提供其他服务。如您实名认证核验失败，请确保您已经按照要求正确使用本服务，或者向业务办理机构咨询其他办理渠道。在遵守相关法律法规及最小必要原则的前提下，基于业务办理机构为您提供服务之需要，百度可能会向业务办理机构提供实名认证核验服务记录以供查询。您在此同意授权百度将依据第1条收集的您的个人信息分享给业务办理机构，以便实现本条所述目的。<br /><br />
        3.百度仅在为实现本服务所必要的最短时间内保存您的个人信息。为了保障您的个人信息安全，百度会在现有技术水平下采取合理必要的措施来保护您的个人信息，采取物理、技术等安全措施来降低丢失、误用、非授权访问、披露和更改的风险。<br /><br />
        4.如您希望访问、撤回授权、修改、删除您的个人信息，您可以向业务办理机构提出需求，并提供必要的身份证明。<br /><br />
        5.授权人声明：本授权书是您向被授权人做出的单方承诺，效力具有独立性。若您与被授权人发生任何纠纷或争议，首先应友好协商解决；协商不成的，您同意将纠纷或争议提交本授权书签订地（即中国北京市海淀区）有管辖权的人民法院管辖。本授权书的成立、生效、履行、解释及纠纷解决等，适用中华人民共和国大陆地区法律（仅为本授权书目的，不含港澳台地区法律），不包括冲突法。<br /><br />
        6.您确认，已理解本授权书所有内容（特别是加粗字体内容）的意义以及由此产生的法律效力，自愿作出上述授权，本授权书是您真实的意思表示，您同意承担相关责任及后果。
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Lottie from "vue-lottie";
import * as faceFile from "@/assets/json/face.json";
import { getFaceInfo } from "@/api/bdface/bdface";
export default {
  name: "face",
  data() {
    return {
      code: "",
      drawer: false,
      options: {
        animationData: faceFile.default,
      },
      form: {},
      checked: false,
      loading: true,
    };
  },
  components: {
    Lottie,
  },
  created() {
    this.code = this.$route.params.code;
    if (this.code.length < 1) {
      this.$message.error("参数有误");
      return;
    }
    this.getDetail();
  },
  methods: {
    showdrawer() {
      this.drawer = true;
    },
    handleAnimation(anim) {
      this.anim = anim;
    },
    getDetail() {
      getFaceInfo({ shareKey: this.code }).then((res) => {
        this.loading = false;
        if (res.data.success) {
          this.form = res.data.data;
          this.$store.commit("SET_USER_FACE", res.data.data);
          //已完成认证 跳转完成页面
          if (res.data.data.faceVerify == 1) {
            this.$router.replace({ path: `/faceverify/success` });
            return;
          }
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    verifyBtn() {
      if (!this.checked) {
        this.$message({
          message: "请先阅读并同意隐私协议",
          type: "warning",
        });
        return;
      }
      window.location.href = this.form.verifyUrl;
    },
  },
};
</script>
<style lang="scss" scoped>
.face {
  display: block;
  margin: 20px auto;
  max-width: 800px;

  .face-list {
    margin: 15px auto;
    width: 85%;

    .item {
      display: flex;
      justify-content: flex-start;
      align-content: center;
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: 400;

      .label {
        flex: 1;
      }

      .value {
        flex: 2;
      }
    }
  }

  .face-tips {
    font-size: 22px;
    font-weight: 500;
    text-align: center;
    margin: 0px auto 40px auto;
    width: 86%;
  }

  .tips {
    width: 90%;
    margin: 20px auto;
  }

  .btn {
    text-align: center;
    width: 89%;
    margin: 10px auto 10px auto;
    position: fixed;
    bottom: 0;
    left: calc(50% - 45.5%);
    z-index: 99;
    background: rgb(255, 255, 255);
    .checkbox {
      text-align: left;
      font-size: 14px;
      color: #777;
    }

    .button-class {
      width: 90%;
      margin: 30px auto;
    }
  }
}

.drawer-box {
  padding: 15px;
  font-size: 16px;
  line-height: 25px;
}

.ptx {
  text-decoration: underline;
}
</style>
<style>
.el-drawer__header {
  margin-bottom: 10px;
  padding: 15px 15px 0;
}
</style>
