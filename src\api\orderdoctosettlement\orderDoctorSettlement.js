import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlement/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlement/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlement/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlement/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlement/submit",
    method: "post",
    data: row,
  });
};

export const applyForPayment = (ids) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlement/applyForPayment",
    method: "post",
    params: {
      ids,
    },
  });
};
export const simulatedPayment = (ids) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlement/simulatedPayment",
    method: "post",
    params: {
      ids,
    },
  });
};

export const applyForVoid = (ids) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlement/applyForVoid",
    method: "post",
    params: {
      ids,
    },
  });
};
