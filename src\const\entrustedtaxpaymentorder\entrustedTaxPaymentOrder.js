export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  labelWidth: 150,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  viewBtnIcon: " ",
  dialogClickModal: false,
  searchLabelWidth: 130,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "编码",
      prop: "code",
      type: "input",
      search: true,
    },
    {
      label: "客户名称",
      prop: "enterpriseName",
      type: "input",
      search: true,
      searchLabelWidth: "100",
      rules: [
        {
          required: true,
          message: "请输入客户名称",
          trigger: "blur",
        },
      ],
    },
    {
      label: "统一社会信用代码",
      prop: "socialCreditCode",
      type: "input",
      search: true,
      searchLabelWidth: "160",
    },
    {
      label: "企业结算单编号",
      prop: "settlementCode",
      type: "input",
      search: true,
      searchLabelWidth: "120",
    },
    // {
    //   label: "企业结算单名称",
    //   prop: "settlementName",
    //   type: "input",
    // },
    {
      label: "服务开始日期",
      prop: "actStartTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: false,
      searchRange: true,
    },
    {
      label: "服务结束日期",
      prop: "actEndTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: false,
      searchRange: true,
    },
    {
      label: "合计费率",
      prop: "taxFeeRate",
      type: "input",
      hide: true,
      rules: [
        {
          required: true,
          message: "合计费率",
          trigger: "blur",
        },
      ],
    },
    {
      label: "纳税金额",
      prop: "taxFeeAmount",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入纳税金额",
          trigger: "blur",
        },
      ],
    },
    {
      label: "支付状态",
      prop: "payStatus",
      type: "input",
    },
  ],
};
