export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  delBtn: false,
  editBtn: false,
  addBtn: false,
  viewBtnIcon: " ",
  dialogClickModal: false,
  searchLabelWidth: 130,
  column: [
    {
      label: "任务编号",
      prop: "code",
      type: "input",
      search: true,
    },

    {
      label: "客户名称",
      prop: "entrustedCompanyName",
      type: "input",
      search: true,
    },
    {
      label: "统一社会信用代码",
      prop: "socialCreditCode",
      type: "input",
      width: "165",
    },
    {
      label: "平台名称",
      prop: "taskPlateName",
      type: "input",
      width: "165",
    },
    {
      label: "任务开始时间",
      prop: "orderStartDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      width: "160",
    },
    {
      label: "任务结束时间",
      prop: "orderEndDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      width: "160",
    },
    {
      label: "任务数量",
      prop: "planNum",
      type: "input",
    },
    {
      label: "任务计划金额",
      prop: "planAmount",
      type: "input",
    },
    {
      label: "任务状态",
      prop: "orderStatus",
      type: "select",
      search: true,
      dicData: [
        {
          label: "已接单",
          value: 1,
        },
        {
          label: "待验收",
          value: 2,
        },
        {
          label: "待结算",
          value: 3,
        },
        {
          label: "已完成",
          value: 4,
        },
        {
          label: "已作废",
          value: 5,
        },
      ],
    },
  ],
};
