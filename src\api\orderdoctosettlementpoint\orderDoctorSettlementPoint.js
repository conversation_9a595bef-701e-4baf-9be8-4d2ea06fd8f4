import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlementPoint/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlementPoint/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlementPoint/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlementPoint/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlementPoint/submit",
    method: "post",
    data: row,
  });
};

export const applyForPayment = (ids) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlementPoint/applyForPayment",
    method: "post",
    params: {
      ids,
    },
  });
};
export const simulatedPayment = (ids) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlementPoint/simulatedPayment",
    method: "post",
    params: {
      ids,
    },
  });
};

export const applyForVoid = (ids) => {
  return request({
    url: "/api/blade-svc/orderDoctorSettlementPoint/applyForVoid",
    method: "post",
    params: {
      ids,
    },
  });
};
