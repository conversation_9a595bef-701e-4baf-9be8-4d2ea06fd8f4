import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-handleApply/handleApply/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-handleApply/handleApply/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-handleApply/handleApply/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-handleApply/handleApply/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-handleApply/handleApply/submit",
    method: "post",
    data: row,
  });
};

//发起代发经办
export const sponsorHandle = (handleIds) => {
  return request({
    // url: "/api/blade-handleApply/handleApply/sponsorHandle",
    url: "/api/blade-handleApply/handleApply/sponsorHandleForApproval",
    method: "post",
    params: { handleIds },
  });
};
//发起代发经办并打款
export const sponsorHandleNoApproval = (handleIds) => {
  return request({
    // url: "/api/blade-handleApply/handleApply/sponsorHandle",
    url: "/api/blade-handleApply/handleApply/sponsorHandleNoApproval",
    method: "post",
    params: { handleIds },
  });
};
export const sponsorHandleApproval = (data) => {
  return request({
    // url: "/api/blade-handleApply/handleApply/sponsorHandle",
    url: "/api/blade-handleApply/handleApply/sponsorHandleApproval",
    method: "post",
    data,
  });
};

//同步查询并更新经办单状态
export const handleApplyInfo = (handleApplyId) => {
  return request({
    url: "/api/blade-handleApply/handleApply/queryApplyInfoStatus",
    method: "get",
    params: { handleApplyId },
  });
};
//获取结算单失败经办明细列表
export const getFailApplyInfo = (current, size, params) => {
  return request({
    url: "/api/blade-handleApplyInfo/handleApplyInfo/getFailApplyInfo",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

//在经办：对失败经办重新发起经办业务
export const sponsorFailHandle = (handleApplyInfoVOList, handleId) => {
  return request({
    // url: "/api/blade-handleApply/handleApply/sponsorFailHandle",
    url: "/api/blade-handleApply/handleApply/sponsorHandleApprovalFailHandle",
    method: "post",
    data: {
      handleId,
      handleApplyInfoVOList,
    },
  });
};

// 支付退回
export const returnHandleApply = (id) => {
  return request({
    url: "/api/blade-handleApply/handleApply/returnHandleApply",
    method: "get",
    params: {
      id,
    },
  });
};
//解冻
export const thaw = (ids) => {
  return request({
    url: "/api/blade-handleApply/handleApply/thaw",
    method: "post",
    params: {
      ids,
    },
  });
};
