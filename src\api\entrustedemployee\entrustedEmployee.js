import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-csc/entrustedEmployee/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-csc/entrustedEmployee/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-csc/entrustedEmployee/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-csc/entrustedEmployee/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-csc/entrustedEmployee/submit",
    method: "post",
    data: row,
  });
};

export const createEmployeeUser = (id) => {
  return request({
    url: "/api/blade-csc/entrustedEmployee/createEmployeeUser",
    method: "get",
    params: {
      id,
      roleId: 39,
    },
  });
};
