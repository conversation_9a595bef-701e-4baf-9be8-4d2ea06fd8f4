export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  dialogClickModal: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  menu: false,
  searchLabelWidth: 130,
  column: [
    {
      label: "企业订单号",
      prop: "orderEntrustedCode",
      type: "input",
      // search: true,
      hide: true,
    },
    {
      label: "会员订单号",
      prop: "code",
      type: "input",
      search: true,
    },
    {
      label: "客户名称",
      prop: "entrustedCompanyName",
      type: "input",
      search: true,
    },
    {
      label: "统一社会信用代码",
      prop: "socialCreditCode",
      type: "input",
      width: "120",
      search: true,
    },
    {
      label: "会员名称",
      prop: "doctorName",
      type: "input",
      search: true,
    },
    {
      label: "会员手机号",
      prop: "doctorPhone",
      type: "input",
      search: true,
    },
    {
      label: "会员身份证号",
      prop: "doctorIdCard",
      type: "input",
      search: true,
    },
    {
      label: "项目类型",
      prop: "projectTypeName",
      type: "input",
    },
    {
      label: "应税项目",
      prop: "projectTaxItemName",
      type: "input",
      width: "120",
    },
    // {
    //   label: "项目执行方案",
    //   prop: "serviceProjectName",
    //   type: "input",
    //   search: true,
    // },
    {
      label: "项目计划数量",
      prop: "planNum",
      type: "input",
    },
    {
      label: "项目结算数量",
      prop: "resultNum",
      type: "input",
    },
    {
      label: "订单下达时间",
      prop: "realityStartDate",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: "100",
    },
    {
      label: "订单接单时间",
      prop: "receivingOrderData",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      search: true,
      searchRange: true,
      width: "100",
    },
    {
      label: "订单状态",
      prop: "orderStatus",
      type: "select",
      search: true,
      dicData: [
        {
          label: "已接单",
          value: 1,
        },
        {
          label: "待验收",
          value: 2,
        },
        {
          label: "待结算",
          value: 3,
        },
        {
          label: "已完成",
          value: 4,
        },
        {
          label: "已作废",
          value: 5,
        },
      ],
    },
    {
      label: "企业支付金额",
      prop: "doctorAmount",
      type: "input",
    },
    {
      label: "企业支付银行账户",
      prop: "entrustedBank",
      type: "input",
    },
    {
      label: "支付给会员金额",
      prop: "doctorAmount",
      type: "input",
    },
    {
      label: "会员收款银行账户",
      prop: "doctorBank",
      type: "input",
    },

    {
      label: "该项业务是否真实",
      prop: "realBusiness",
      type: "input",
    },
    {
      label: "核查人员",
      prop: "baseEmployeeName",
      type: "input",
    },
    // {
    //   label: "主管税务机关",
    //   prop: "123",
    //   type: "input",
    // },
    // {
    //   label: "向企业开具发票号码代码",
    //   prop: "233",
    //   type: "input",
    // },
    // {
    //   label: "向企业开具发票金额",
    //   prop: "333",
    //   type: "input",
    // },
    // {
    //   label: "会员代开进项发票号码代码",
    //   prop: "433",
    //   type: "input",
    // },
    // {
    //   label: "会员代开进项发票金额",
    //   prop: "533",
    //   type: "input",
    // },
  ],
};
