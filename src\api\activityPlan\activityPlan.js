import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/activityPlan/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/activityPlan/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/activityPlan/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/activityPlan/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/activityPlan/submit",
    method: "post",
    data: row,
  });
};

//获取可以邀请的会员列表
export const getPlanDoctor = (current, size, params) => {
  return request({
    url: "/api/blade-authenticationDoctor/authenticationDoctor/getPlanDoctor",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
//保存会员
export const saveList = (data) => {
  return request({
    url: "/api/blade-act/caseCollectionDoctor/saveList",
    method: "post",
    data,
  });
};
//获取已经邀请会员
export const getDoctorList = (current, size, params) => {
  return request({
    url: "/api/blade-act/caseCollectionDoctor/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};
//删除已经邀请会员
export const deleteDoctor = (id) => {
  return request({
    url: "/api/blade-act/caseCollectionDoctor/deleteDoctor",
    method: "post",
    params: {
      id,
    },
  });
};
//获取已经选择会员id
export const getExistDoctorIds = (caseCollectionId) => {
  return request({
    url: "/api/blade-act/caseCollectionDoctor/getExistDoctorIds",
    method: "get",
    params: {
      caseCollectionId,
    },
  });
};

//修改计划收集份数
export const savePlanSearchNum = (id, planSearchNum) => {
  return request({
    url: "/api/blade-act/caseCollectionDoctor/savePlanSearchNum",
    method: "post",
    params: {
      id,
      planSearchNum,
    },
  });
};

//医患服务方案
export const listByProductId = (productId, projectType) => {
  return request({
    url: "/api/blade-act/activityServiceProject/listByProductId",
    method: "get",
    params: {
      productId,
      projectType,
    },
  });
};
//获取业务人员
export const getByEntrustedCompanyId = (query) => {
  return request({
    url: "/api/blade-csc/entrustedEmployee/getByEntrustedCompanyId",
    method: "get",
    params: query,
  });
};
