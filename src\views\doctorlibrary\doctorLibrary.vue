<template>
  <basic-container>
    <avue-crud
      :option="option"
      :search.sync="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="primary"
          v-if="permission.doctorLibrary_handleExport"
          @click="handleExport"
          >导 出
        </el-button>
        <el-button
          type="primary"
          v-if="permission.doctorLibrary_handleImport"
          @click="handleImport"
          >导 入
        </el-button>
        <el-button
          type="primary"
          v-if="permission.doctorLibrary_delete"
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
    </avue-crud>
    <el-dialog
      title="认证医师资料导入"
      append-to-body
      :visible.sync="excelBox"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-before="uploadBefore"
        :upload-after="uploadAfter"
      >
        <template #excelTemplate>
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove,
} from "@/api/doctorlibrary/doctorLibrary";
import option from "@/const/doctorlibrary/doctorLibrary";
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import func from "@/util/func";

export default {
  data() {
    return {
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "认证医师资料",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "数据上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
            },
            data: {
              isCovered: 1,
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            // action: `${this.id}`,
            action: "/api/blade-csc/doctorLibrary/import-doctorLibrary",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
      excelForm: {},
      excelBox: false,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.doctorLibrary_add, false),
        viewBtn: this.vaildData(this.permission.doctorLibrary_view, false),
        delBtn: this.vaildData(this.permission.doctorLibrary_delete, false),
        editBtn: this.vaildData(this.permission.doctorLibrary_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleExport() {
      const name = func.toStr(this.search.name);
      const phone = func.toStr(this.search.phone);
      const idCardNumber = func.toStr(this.search.idCardNumber);
      const idCardOcrResult = func.toStr(this.search.idCardOcrResult);
      const carriersAuthResult = func.toStr(this.search.carriersAuthResult);
      const dataStatus = func.toStr(this.search.dataStatus);

      let downloadUrl = `/api/blade-csc/doctorLibrary/export-doctorLibrary?${
        this.website.tokenHeader
      }=${getToken()}`;
      let values = {
        name,
        phone,
        idCardNumber,
        idCardOcrResult,
        carriersAuthResult,
        dataStatus,
      };
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `认证医师资料库${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    handleImport() {
      this.excelBox = true;
    },
    uploadBefore(file, done) {
      this.excelBox = false;
      this.$message({
        type: "success",
        message: "操作成功，请稍候查看数据。",
      });
      done();
    },
    uploadAfter(res, done, loading, column) {
      window.console.log(column);
      this.excelBox = false;
      this.refreshChange();
      done();
    },
    handleTemplate() {
      window.open("/excel/认证医师信息导入模板.xlsx");
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/doctorlibrary/detail/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;

      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
