export const option2 = {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  addBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  emptyBtnIcon: " ",
  selection: true,
  dialogClickModal: false,
  searchLabelWidth: 130,
  column: [
    {
      label: "客户类型",
      prop: "customerType",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=customer_type",
      props: { label: "dictValue", value: "dictKey" },
      dataType: "number",
    },
    {
      label: "客户名称",
      prop: "customerName",
      search: true,
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入客户名称",
          trigger: "blur",
        },
      ],
    },
    {
      label: "提交时间",
      prop: "submitTime",
      type: "date",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
    },
    {
      label: "审核状态",
      prop: "auditStatus",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=audit_status",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      align: "center",
    },
    {
      label: "审核人",
      prop: "auditor",
      type: "input",
    },
    {
      label: "审核时间",
      prop: "auditTime",
      type: "date",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
    },
    {
      label: "审核意见",
      prop: "auditContext",
      type: "input",
    },
  ],
};

export const option3 = {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  addBtn: false,
  tip: false,
  searchShow: true,
  emptyBtnIcon: " ",
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  selection: true,
  dialogClickModal: false,
  searchLabelWidth: 130,
  column: [
    {
      label: "客户类型",
      prop: "customerType",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=customer_type",
      props: { label: "dictValue", value: "dictKey" },
      dataType: "number",
    },

    {
      label: "客户名称",
      prop: "customerName",
      search: true,
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入客户名称",
          trigger: "blur",
        },
      ],
    },
    {
      label: "提交时间",
      prop: "submitTime",
      type: "date",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
    },
    {
      label: "审核状态",
      prop: "auditStatus",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=audit_status",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      align: "center",
      search: true,
    },
    {
      label: "人脸提交状态",
      prop: "isFaceVerify",
      type: "select",
      search: true,
      dicData: [
        {
          label: "未提交",
          value: 0,
        },
        {
          label: "已提交",
          value: 1,
        },
      ],
    },
    {
      label: "结算类型",
      prop: "settleType",
      type: "select",
      dicUrl: "/api/blade-system/dict-biz/dictionary?code=settle_type",
      props: {
        label: "dictValue",
        value: "dictKey",
      },
      dataType: "number",
      align: "center",
    },
    {
      label: "审核人",
      prop: "auditor",
      type: "input",
    },
    {
      label: "审核时间",
      prop: "auditTime",
      type: "date",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
    },
    {
      label: "审核意见",
      prop: "auditContext",
      type: "input",
    },
  ],
};
