import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/caseCollection/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/demo/caseCollection/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/demo/caseCollection/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/demo/caseCollection/submit",
    method: "post",
    data: row,
  });
};



//获取模板
export const listByProductId = (productId) => {
  return request({
    url: "/api/blade-act/caseCollectionTemplate/listByProductId",
    method: "get",
    params: {
      productId,
    },
  });
};
//开始结束计划
export const stopCaseCollection = (id, status) => {
  return request({
    url: "/api/blade-act/demo/caseCollection/stopCaseCollection",
    method: "get",
    params: {
      id,
      status,
    },
  });
};
