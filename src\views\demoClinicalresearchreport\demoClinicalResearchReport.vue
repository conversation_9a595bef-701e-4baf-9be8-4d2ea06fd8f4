<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :search.sync="search"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
<!--      <template slot="menuLeft">-->
<!--        <el-button type="primary" @click="batchApproveClick(1)"-->
<!--        >批量初审</el-button>-->
<!--        <el-button  @click="batchApproveClick(2)"-->
<!--        >批量复审</el-button>-->
<!--      </template>-->
      <!-- 初审状态 -->
      <template slot="approvalStatus" slot-scope="{ row }">
        <el-tag v-if="row.approvalStatus == 0" size="small">待审核</el-tag>
        <el-tag v-if="row.approvalStatus == 1" type="success" size="small"
          >通过</el-tag
        >
        <el-tag v-if="row.approvalStatus == 2" type="danger" size="small"
          >驳回</el-tag
        >
      </template>
      <!-- 验收状态 -->
      <template slot="confirmStatus" slot-scope="{ row }">
        <el-tag v-if="row.confirmStatus == 0" size="small">待验收</el-tag>
        <el-tag v-if="row.confirmStatus == 1" type="success" size="small"
          >通过</el-tag
        >
        <el-tag v-if="row.confirmStatus == 2" type="danger" size="small"
          >驳回</el-tag
        >
      </template>
      <!--<template slot-scope="{ type, size, row, index }" slot="code">-->
      <!--<div-->
      <!--class="to-view"-->
      <!--@click="toOpenPlan(row.clinicalResearchId)"-->
      <!--v-if="row.code"-->
      <!--&gt;-->
      <!--<a readonly>-->
      <!--{{ row.code }}-->
      <!--</a>-->
      <!--</div>-->
      <!--<div v-else>无</div>-->
      <!--</template>-->
    </avue-crud>
    <el-dialog
      :title="`批量${approveType === 1 ? '初审' : '复审'}`"
      :visible.sync="approveVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      width="340px"
    >
      <el-form ref="approveForm" :model="approveForm">
        <el-form-item prop="agree">
          <el-switch
            v-model="approveForm.agree"
            active-text="同意"
            inactive-text="驳回"
          />
        </el-form-item>
        <el-form-item label="备注"  prop="remark" :rules="[{ required: !approveForm.agree, message: '请输入备注', trigger: 'change' }]">
          <el-input
            v-model="approveForm.remark"
            resize="none"
            type="textarea"
            placeholder="请输入备注"
            :autosize="{ minRows: 2, maxRows: 3}"
          />
        </el-form-item>
      </el-form>
      <span slot="footer">
          <el-button @click="approveVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBatchApprove">确认</el-button>
        </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  add,
  update,
  remove, batchAudit,
} from "@/api/demoClinicalresearchreport/demoClinicalResearchReport";
import { tree } from "@/api/entrusteddept/entrustedDept";
import option from "@/const/clinicalresearchreport/clinicalResearchReport";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      approveVisible: false,
      approveType: 1,
      approveForm: { agree: true, remark: null },
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      search: {},
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.clinicalResearchReport_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.clinicalResearchReport_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.clinicalResearchReport_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.clinicalResearchReport_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.initData();
    if (
      !this.validatenull(
        window.sessionStorage.getItem("clinicalResearchReportSearch")
      )
    ) {
      this.search = JSON.parse(
        window.sessionStorage.getItem("clinicalResearchReportSearch")
      );
      this.query = JSON.parse(
        window.sessionStorage.getItem("clinicalResearchReportSearch")
      );
      window.sessionStorage.removeItem("clinicalResearchReportSearch");
    }
  },
  //组件销毁
  beforeDestroy() {
    if (!this.validatenull(this.query)) {
      window.sessionStorage.setItem(
        "clinicalResearchReportSearch",
        JSON.stringify(this.query)
      );
    }
  },
  methods: {
    initData() {
      // tree().then((res) => {
      //   this.option.column[27].dicData = res.data.data;
      //   this.orgData = res.data.data;
      // });
      tree().then((res) => {
        var prop = this.findObject(option.column, "baseDepartment");
        prop.dicData = res.data.data;
        this.orgData = res.data.data;
      });
    },
    getAllChildIdsById(id, data) {
      let _this = this;
      let childIds = [];
      // 递归辅助函数，用于遍历数据并获取子节点ID
      function traverseChildren(nodes) {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].id === id) {
            // 如果当前节点匹配到给定ID，将其子节点ID添加到结果数组中
            if (nodes[i].children && nodes[i].children.length > 0) {
              childIds = childIds.concat(
                _this.getAllChildIdsByIdHelper(nodes[i].children)
              );
            }
          } else if (nodes[i].children && nodes[i].children.length > 0) {
            // 如果当前节点不匹配给定ID，继续向下遍历子节点
            traverseChildren(nodes[i].children);
          }
        }
      }

      traverseChildren([data]); // 调用辅助函数从根节点开始遍历

      return childIds;
    },
    getAllChildIdsByIdHelper(nodes) {
      let childIds = [];
      let _this = this;
      for (let i = 0; i < nodes.length; i++) {
        childIds.push(nodes[i].id);

        if (nodes[i].children && nodes[i].children.length > 0) {
          childIds = childIds.concat(
            _this.getAllChildIdsByIdHelper(nodes[i].children)
          );
        }
      }

      return childIds;
    },
    toOpenPlan(id) {
      this.$router.push({
        path: `/demoActivityPlan/demoClinicalResearchView/${id}`,
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/demoClinicalresearchreport/detail/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      //提交时间
      if (!this.validatenull(params.submitTime)) {
        this.query.submitTimeStartStart = params.submitTime[0];
        this.query.submitTimeStartEnd = params.submitTime[1];
        delete this.query.submitTime;
      }
      //审核时间
      if (!this.validatenull(params.approvalDate)) {
        this.query.approvalDateEndStart = params.approvalDate[0];
        this.query.approvalDateEndEnd = params.approvalDate[1];
        delete this.query.approvalDate;
      }

      if (params.baseDepartment) {
        let baseDepartmentId = params.baseDepartment.join(",") + ",";
        params.baseDepartment.map((item) => {
          this.orgData.map((item2) => {
            let itemList = this.getAllChildIdsById(item, item2);
            baseDepartmentId += itemList.join(",");
          });
        });
        // 调用方法，传入给定ID和组织架构数据
        params.baseDepartmentIds = baseDepartmentId;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    // approveType 1:初审 2:复审
    batchApproveClick(approveType) {
      if(this.selectionList.length > 0) {
        this.approveType = approveType
        this.approveVisible = true
      } else {
        this.$message.warning("请选择至少一条数据");
      }
    },
    handleBatchApprove() {
      this.$refs.approveForm.validate().then(valid => {
        if(valid) {
          const data = this.selectionList.map(row => {
            return {...row, approvalBusinessType: this.approveType, auditStatus: this.approveForm.agree ? 1 : 2, auditContent: this.approveForm.remark}
          })
          batchAudit(data).then(res => {
            this.onLoad(this.page);
            this.approveVisible = false
            if(res.data.data && res.data.data.length > 0) {
              this.$alert(res.data.data.map(item => `<div>${item}</div>`).join(''), '审批失败列表', {
                confirmButtonText: '确定',
                dangerouslyUseHTMLString: true
              });
            } else {
              this.$message.success('操作成功')
            }
          })
        }
      })
    }
  },
};
</script>

<style></style>
