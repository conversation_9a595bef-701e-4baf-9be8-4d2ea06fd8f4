<template>
  <div class="statistics">
    <div class="statistics-title">平台数量统计</div>
    <div class="statistics-list">
      <div class="statistics-item" @click="to(1)">
        <div class="item-title">企业会员数量</div>
        <div class="num">
          {{ form.entrustedMember }}<span class="unit">个</span>
        </div>
        <div class="item-img">
          <img src="@/assets/images/home/<USER>" alt="" />
        </div>
      </div>
      <div class="statistics-item item2" @click="to(2)">
        <div class="item-title">个人会员数量</div>
        <div class="num">
          {{ form.personMember }}<span class="unit">个</span>
        </div>
        <div class="item-img">
          <img src="@/assets/images/home/<USER>" alt="" />
        </div>
      </div>
    </div>
    <div class="statistics-list">
      <div class="statistics-item item3" @click="to(3)">
        <div class="item-title">客户任务量</div>
        <div class="num">
          {{ form.customerTasks }}<span class="unit">个</span>
        </div>
        <div class="item-img">
          <img src="@/assets/images/home/<USER>" alt="" />
        </div>
      </div>
      <div class="statistics-item item4" @click="to(4)">
        <div class="item-title">个人订单量</div>
        <div class="num">
          {{ form.personOrder }}<span class="unit">个</span>
        </div>
        <div class="item-img">
          <img src="@/assets/images/home/<USER>" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "homeStatistics",
  props: {
    form: {
      type: Object,
      required: {},
    },
  },
  data: () => {
    return {};
  },
  mounted() {},
  methods: {
    to(index) {
      if (index == 1) {
        this.$router.push({
          path: "/entrustedcompany/entrustedCompany",
        });
      } else if (index == 2) {
        this.$router.push({
          path: "/authenticationDoctor/authenticationDoctor",
        });
      } else if (index == 3) {
        this.$router.push({
          path: "/activityPlan/activityPlan",
          query: {
            index: index,
          },
        });
      } else if (index == 4) {
        this.$router.push({
          path: "/orderdoctor/orderDoctor",
        });
      }
    },
  },

  beforeUnmount() {},
};
</script>

<style scoped lang="scss">
.statistics {
  width: 100%;
  height: 451px;
  border-radius: 8px;
  background: #ffffff;
}
.statistics::before {
  display: table;
  content: "";
}
.statistics-title {
  width: 102px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  line-height: 22px;
  margin: 18px 0 20px 22px;
}
.statistics-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-around;
}
.statistics-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 38%;
  height: 152px;
  background: linear-gradient(
    179deg,
    rgba(255, 169, 28, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  cursor: pointer;
}
.item2 {
  background: linear-gradient(
    179deg,
    rgba(197, 108, 247, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}
.item3 {
  background: linear-gradient(
    179deg,
    rgba(45, 173, 250, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}
.item4 {
  background: linear-gradient(
    170deg,
    rgba(255, 215, 235, 0.5) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}
.item-title {
  margin-top: 15px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
}
.unit {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
}
.num {
  font-size: 20px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 28px;
}
.item-img {
  width: 48px;
  height: 48px;
  margin-bottom: 11px;
  img {
    width: 100%;
  }
}
</style>
