<template>
  <basic-container>
    <avue-crud
      :option="option"
      :search.sync="search"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- 提交状态 -->
      <template slot="submitType" slot-scope="{ row }">
        <el-tag v-if="row.submitType == 1" size="small">待提交</el-tag>
        <el-tag v-if="row.submitType == 2" type="success" size="small"
          >已提交</el-tag
        >
        <el-tag v-if="row.submitType == 3" type="danger" size="small"
          >驳回</el-tag
        >
      </template>
      <!-- 初审状态 -->
      <template slot="approvalStatus" slot-scope="{ row }">
        <div v-if="row.submitType != 1">
          <el-tag v-if="row.approvalStatus == 0" size="small">待审核</el-tag>
          <el-tag v-if="row.approvalStatus == 1" type="success" size="small"
            >通过</el-tag
          >
          <el-tag v-if="row.approvalStatus == 2" type="danger" size="small"
            >驳回</el-tag
          >
        </div>
      </template>
      <!-- 验收状态 -->
      <template slot="confirmStatus" slot-scope="{ row }">
        <el-tag v-if="row.confirmStatus == 0" size="small">待验收</el-tag>
        <el-tag v-if="row.confirmStatus == 1" type="success" size="small"
          >通过</el-tag
        >
        <el-tag v-if="row.confirmStatus == 2" type="danger" size="small"
          >驳回</el-tag
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getListPage, add, update, remove } from "@/api/demoLecture/demoLecture";
import option from "@/const/lecture/lecture";
import { mapGetters } from "vuex";
import { tree } from "@/api/entrusteddept/entrustedDept";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from "nprogress";
import "nprogress/nprogress.css";

export default {
  data() {
    return {
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
      type: "",
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.lecture_2_add, false),
        viewBtn: this.vaildData(this.permission.lecture_2_view, false),
        delBtn: this.vaildData(this.permission.lecture_2_delete, false),
        editBtn: this.vaildData(this.permission.lecture_2_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.initData();
    if (!this.validatenull(window.sessionStorage.getItem("lecture_2Search"))) {
      this.search = JSON.parse(
        window.sessionStorage.getItem("lecture_2Search")
      );
      this.query = JSON.parse(window.sessionStorage.getItem("lecture_2Search"));
      window.sessionStorage.removeItem("lecture_2Search");
    }
  },
  //组件销毁
  beforeDestroy() {
    if (!this.validatenull(this.query)) {
      window.sessionStorage.setItem(
        "lecture_2Search",
        JSON.stringify(this.query)
      );
    }
  },
  methods: {
    initData() {
      tree().then((res) => {
        var prop = this.findObject(this.option.column, "baseDepartment");
        prop.dicData = res.data.data;
        this.orgData = res.data.data;
      });
    },
    getAllChildIdsById(id, data) {
      let _this = this;
      let childIds = [];
      // 递归辅助函数，用于遍历数据并获取子节点ID
      function traverseChildren(nodes) {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].id === id) {
            // 如果当前节点匹配到给定ID，将其子节点ID添加到结果数组中
            if (nodes[i].children && nodes[i].children.length > 0) {
              childIds = childIds.concat(
                _this.getAllChildIdsByIdHelper(nodes[i].children)
              );
            }
          } else if (nodes[i].children && nodes[i].children.length > 0) {
            // 如果当前节点不匹配给定ID，继续向下遍历子节点
            traverseChildren(nodes[i].children);
          }
        }
      }

      traverseChildren([data]); // 调用辅助函数从根节点开始遍历

      return childIds;
    },

    getAllChildIdsByIdHelper(nodes) {
      let childIds = [];
      let _this = this;
      for (let i = 0; i < nodes.length; i++) {
        childIds.push(nodes[i].id);

        if (nodes[i].children && nodes[i].children.length > 0) {
          childIds = childIds.concat(
            _this.getAllChildIdsByIdHelper(nodes[i].children)
          );
        }
      }

      return childIds;
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleExport() {
      let downloadUrl = `/api/blade-act/lecture/export-lecture?${
        this.website.tokenHeader
      }=${getToken()}`;
      let values = {};
      this.$confirm("是否导出数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then((res) => {
          downloadXls(res.data, `专业知识创作活动表${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/demoLecture_2/detail/${this.form.id}`,
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      //提交时间
      if (!this.validatenull(params.submitTime)) {
        this.query.submitTimeStart = params.submitTime[0];
        this.query.submitTimeEnd = params.submitTime[1];
        delete this.query.submitTime;
      }
      if (params.baseDepartment) {
        let baseDepartmentId = params.baseDepartment.join(",") + ",";
        params.baseDepartment.map((item) => {
          this.orgData.map((item2) => {
            let itemList = this.getAllChildIdsById(item, item2);
            baseDepartmentId += itemList.join(",");
          });
        });
        // 调用方法，传入给定ID和组织架构数据
        params.baseDepartmentIds = baseDepartmentId;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.type = 2;
      getListPage(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
