export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  searchLabelWidth: "130",
  dialogClickModal: false,
  addBtnIcon: " ",
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "项目方案名称",
      prop: "projectName",
      type: "input",
      search: true,
    },
    {
      label: "项目类型",
      prop: "projectType",
      type: "select",
      dicUrl: "/api/blade-act/activityProjectType/getClinicalCooperation",
      props: {
        label: "projectTypeName",
        value: "id",
      },

      search: true,
    },
    {
      label: "产品名称",
      prop: "productName",
      type: "input",
      search: true,
    },
    {
      label: "项目开始日期",
      prop: "startDate",
      type: "input",
    },
    {
      label: "项目截止日期",
      prop: "endDate",
      type: "input",
    },
    {
      label: "项目发起部门",
      prop: "orgDepartment",
      type: "input",
    },
    {
      label: "项目负责人员",
      prop: "orgPersonnel",
      type: "input",
    },
    {
      label: "项目验收部门",
      prop: "checkDepartment",
      type: "input",
    },
    {
      label: "项目验收人员",
      prop: "checkPersonnel",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态 ",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
