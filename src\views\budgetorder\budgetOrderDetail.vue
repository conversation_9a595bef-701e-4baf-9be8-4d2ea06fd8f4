<template>
  <div>
    <basic-container>
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
    </basic-container>
    <basic-container>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="1">
          <!--基本信息-->
          <avue-form
            v-if="form.entrustedCompanyName != ''"
            :option="option"
            v-model="form"
          >
          </avue-form>
          <template v-else>
            <el-skeleton :rows="10" animated />
          </template>
        </el-tab-pane>
        <el-tab-pane label="服务预算明细" name="2">
          <avue-crud
            :table-loading="activityLoading"
            :data="activityData"
            :option="activityOption"
            @refresh-change="refreshChangeActivity"
          >
          </avue-crud>
        </el-tab-pane>
        <el-tab-pane label="预算清单" name="3">
          <div v-if="form.budgetOrderUrl" style="margin-top: 15px">
            <iframe
              :src="form.budgetOrderUrl"
              width="100%"
              :height="iframeHeight"
              title="企业会员协议"
              frameBorder="no"
              border="0"
              marginWidth="0"
              marginHeight="0"
              scrolling="no"
              allowTransparency="yes"
            ></iframe>
          </div>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
  </div>
</template>

<script>
import {
  getDetail,
  getBudgetOrderActStatisticsList,
} from "@/api/budgetorder/budgetOrder";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      iframeHeight: window.innerHeight - 180,
      activeName: "1",
      form: {
        entrustedCompanyName: "",
      },
      option: {
        disabled: true,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: "150",
        column: [
          {
            label: "企业名称",
            prop: "entrustedCompanyName",
            type: "input",
          },

          {
            label: "总包方",
            prop: "generalContractor",
            type: "input",
          },

          {
            label: "业务期间",
            prop: "accountPeriod",
            type: "input",
          },
          {
            label: "开始日期",
            prop: "budgetStartTime",
            type: "date",
            format: "yyyy-MM-dd",
          },
          {
            label: "结束日期",
            prop: "budgetEndTime",
            type: "date",
            format: "yyyy-MM-dd",
          },
          {
            label: "预算总费用",
            prop: "budgetTotalFee",
            type: "input",
            hide: true,
          },
        ],
      },
      activityLoading: false,
      activityData: [],
      activityOption: {
        maxHeight: "600",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        menu: false,
        dialogClickModal: false,
        column: [
          {
            label: "服务项目编号",
            prop: "projectCode",
            type: "input",
          },
          {
            label: "服务大类",
            prop: "projectCategory",
            type: "input",
          },
          {
            label: "服务项目名称",
            prop: "actTypeName",
            type: "input",
          },
          {
            label: "服务计划数量",
            prop: "settleNum",
            type: "input",
          },
          {
            label: "服务预算金额",
            prop: "settleFee",
            type: "input",
          },

          {
            label: "服务预算占比",
            prop: "proportion",
            type: "input",
          },
        ],
      },
      id: "",
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
    this.getResultOrderActStatistics();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },

    handleClick() {},

    //服务预算明细
    refreshChangeActivity() {
      this.getResultOrderActStatistics();
    },
    getResultOrderActStatistics() {
      this.activityLoading = true;
      getBudgetOrderActStatisticsList(this.id).then((res) => {
        let data = res.data;
        let num = 0;
        let cost = 0;

        data.data.forEach((item) => {
          num += item.settleNum;
          cost += parseFloat(item.settleFee);
        });
        let obj = {
          actTypeName: "汇总",
          settleNum: num,
          settleFee: cost.toFixed(2),
        };
        data.data.push(obj);
        this.activityData = data.data;
        this.activityLoading = false;
      });
    },
    getDetail() {
      getDetail(this.id).then((res) => {
        this.viewDialogVisible = true;
        this.form = res.data.data;
      });
    },
    submit() {},
  },
};
</script>

<style scoped></style>
