<template>
  <div>
    <basic-container class="el-card__body">
      <el-page-header @back="goBack" :content="$route.name"></el-page-header>
    </basic-container>
    <basic-container>
      <avue-form
        ref="form"
        v-if="!validatenull(form)"
        :option="option"
        v-model="form"
        @submit="submit"
      >
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/doctorlibrary/doctorLibrary";
// import { getPersonalServiceProviders } from "@/api/csc/personalserviceprovider";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      id: "",
      form: {},
      option: {
        height: "auto",
        calcHeight: 30,
        submitBtn: false,
        emptyBtn: false,
        disabled: true,
        tip: false,
        labelWidth: "170",
        border: true,
        index: true,
        dialogClickModal: false,
        group: [
          {
            label: "基本信息",
            arrow: true,
            prop: "group1",
            column: [
              {
                label: "姓名",
                prop: "name",
                type: "input",
              },
              {
                label: "身份证号",
                prop: "idCardNumber",
                type: "input",
              },
              {
                label: "手机号",
                prop: "phone",
                type: "input",
              },
              {
                label: "性别",
                prop: "sex",
                type: "input",
              },
              {
                label: "民族",
                prop: "nationality",
                type: "input",
              },
              {
                label: "籍贯",
                prop: "nativePlace",
                type: "input",
              },
              {
                label: "医院名称",
                prop: "hospitalName",
                type: "input",
              },
              {
                label: "一级科室",
                prop: "departmentName",
                type: "input",
              },
              {
                label: "二级科室",
                prop: "departmentTwoName",
                type: "input",
              },
              {
                label: "职称",
                prop: "professional",
                type: "input",
              },
              {
                label: "职务",
                prop: "duty",
                type: "input",
              },
              {
                label: "讲者级别",
                prop: "speakerLevel",
                type: "input",
              },
              {
                  label: "身份证有效期开始",
                  prop: "idCardStartDate",
                  type: "input",
                },
                {
                  label: "身份证有效期结束",
                  prop: "idCardEndDate",
                  type: "input",
                },
                {
                  label: "身份证地址",
                  prop: "idCardAddress",
                  type: "input",
                },
              {
                label: "开户银行",
                prop: "bankName",
                type: "input",
              },
              {
                label: "银行账号",
                prop: "bankAccount",
                type: "input",
              },
              {
                label: "是否标准银联卡",
                prop: "uniFlg",
                type: "input",
              },
              {
                label: "是否招商银行",
                prop: "bnkFlg",
                type: "input",
             },
              {
                label: "身份证ocr认证",
                prop: "idCardOcrResult",
                type: "select",
                dicData: [
                  {
                    label: "识别成功",
                    value: 1,
                  },
                  {
                    label: "识别失败",
                    value: 2,
                  },
                ],
              },
              {
                label: "银行卡三要素",
                prop: "cardAuthResult",
                type: "select",
                dicData: [
                  {
                    label: "一致",
                    value: "01",
                  },
                  {
                    label: "不一致",
                    value: "02",
                  },
                  {
                    label: "不确定",
                    value: "03",
                  },
                  {
                    label: "失败",
                    value: "04",
                  },
                ],
              },
              {
                label: "运营商三要素",
                prop: "carriersAuthResult",
                type: "select",
                search: true,
                dicData: [
                  {
                    label: "一致",
                    value: "01",
                  },
                  {
                    label: "不一致",
                    value: "02",
                  },
                  {
                    label: "不确定",
                    value: "03",
                  },
                  {
                    label: "失败",
                    value: "04",
                  },
                ],
              },
              {
                label: "数据结果状态",
                prop: "dataStatus",
                type: "select",
                search: true,
                dicData: [
                  {
                    label: "异常",
                    value: 0,
                  },
                  {
                    label: "成功",
                    value: 1,
                  },
                ],
              },
              {
                label: "异常备注",
                prop: "remark",
                type: "input",
              },
            ],
          },
          {
            label: "图片资料",
            arrow: true,
            prop: "group2",
            column: [
              {
                label: "身份证正反图片",
                prop: "idCardFile",
                listType: "picture-card",
                type: "upload",
                span: 24,
              },
              {
                label: "医师认证资料",
                prop: "doctorCertificateData",
                listType: "picture-card",
                type: "upload",
                span: 24,
              },
              {
                label: "人脸识别图片",
                prop: "faceInfo",
                listType: "picture-card",
                type: "upload",
                span: 24,
              },
              {
                label: "银行卡图片",
                prop: "bankImg",
                listType: "picture-card",
                type: "upload",
                limit: 5,
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  methods: {
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },
    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },

    submit() {},
  },
};
</script>

<style scoped>
.el-card__body {
  position: relative;
}
.button {
  position: absolute;
  right: 40px;
  top: 15px;
  z-index: 99;
}
</style>
