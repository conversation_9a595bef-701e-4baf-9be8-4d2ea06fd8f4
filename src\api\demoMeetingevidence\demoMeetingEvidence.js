import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/demo/meetingEvidence/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/demo/meetingEvidence/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/demo/meetingEvidence/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/demo/meetingEvidence/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/demo/meetingEvidence/submit",
    method: "post",
    data: row,
  });
};

//批量提交凭证
export const submitList = (row) => {
  return request({
    url: "/api/blade-act/demo/meetingEvidence/submitList",
    method: "post",
    data: row,
  });
};
