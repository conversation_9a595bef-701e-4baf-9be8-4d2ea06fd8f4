<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="orderStatus" slot-scope="{ row }">
        <el-tag v-if="row.orderStatus == 1" type="success" size="small"
          >已接单</el-tag
        >
        <el-tag v-if="row.orderStatus == 2" type="success" size="small"
          >待验收</el-tag
        >
        <el-tag v-if="row.orderStatus == 3" type="success" size="small"
          >待结算</el-tag
        >
        <el-tag v-if="row.orderStatus == 4" type="info" size="small"
          >已完成</el-tag
        >
        <el-tag v-if="row.orderStatus == 5" type="warning" size="small"
          >已作废</el-tag
        >
      </template>
      <template slot="doctorName" slot-scope="{ row }">
        <div
          class="to-view"
          @click="toAuthenticationDoctor(row)"
          v-if="row.doctorName"
        >
          <a readonly>
            {{ row.doctorName }}
          </a>
        </div>
      </template>
      <template slot="planAmount" slot-scope="{ row }">
        {{ row.payType && row.payType == '1' ?  Math.floor(Number(row.planAmount)) :  row.planAmount }}
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, add, update, remove } from "@/api/orderdoctor/orderDoctor";
import option from "@/const/orderdoctor/orderDoctor";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.orderDoctor_add, false),
        viewBtn: this.vaildData(this.permission.orderDoctor_view, false),
        delBtn: this.vaildData(this.permission.orderDoctor_delete, false),
        editBtn: this.vaildData(this.permission.orderDoctor_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    toAuthenticationDoctor(row) {
      this.$router.push({
        path: `/authenticationDoctor/detail/${row.doctorId}/1`,
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        this.$router.push({
          path: `/orderdoctor/detail/${this.form.id}`,
          query: { payType: this.form.payType }
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;

      //接包时间
      if (!this.validatenull(params.receivingOrderData)) {
        this.query.receivingStartData = params.receivingOrderData[0];
        this.query.receivingEndData = params.receivingOrderData[1];
        delete this.query.receivingOrderData;
      }

      //订单计划开始时间
      if (!this.validatenull(params.invitationStartDate)) {
        this.query.invitationStartStart = params.invitationStartDate[0];
        this.query.invitationStartEnd = params.invitationStartDate[1];
        delete this.query.invitationStartDate;
      }
      //订单计划结束时间
      if (!this.validatenull(params.invitationEndDate)) {
        this.query.invitationEndStart = params.invitationEndDate[0];
        this.query.invitationEndEnd = params.invitationEndDate[1];
        delete this.query.invitationEndDate;
      }

      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
