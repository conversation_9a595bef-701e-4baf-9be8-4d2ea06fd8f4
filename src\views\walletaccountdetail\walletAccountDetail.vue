<template>
  <basic-container>
    <avue-crud
      :upload-exceed="uploadExceed"
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <!-- 企业结算单编号 -->
      <template slot="settlementOrderCode" slot-scope="{ row }">
        <div
          class="to-view"
          @click="toDetail(row.settlementOrderId)"
          v-if="row.settlementOrderCode"
        >
          <a readonly>
            {{ row.settlementOrderCode }}
          </a>
        </div>
      </template>
      <template slot-scope="{ type, disabled }" slot="settlementOrderCodeForm">
        <div
          class="to-view"
          @click="toDetail(form.settlementOrderId)"
          v-if="form.settlementOrderCode"
        >
          <a readonly>
            {{ form.settlementOrderCode }}
          </a>
        </div>
      </template>
      <!-- 个人结算单编号 -->
      <template slot="settlementOrderDoctorCode" slot-scope="{ row }">
        <div
          class="to-view"
          @click="toDetail2(row.settlementOrderDoctorId)"
          v-if="row.settlementOrderDoctorCode"
        >
          <a readonly>
            {{ row.settlementOrderDoctorCode }}
          </a>
        </div>
      </template>
      <template
        slot-scope="{ type, disabled }"
        slot="settlementOrderDoctorCodeForm"
      >
        <div
          class="to-view"
          @click="toDetail2(form.settlementOrderDoctorId)"
          v-if="form.settlementOrderDoctorCode"
        >
          <a readonly>
            {{ form.settlementOrderDoctorCode }}
          </a>
        </div>
      </template>
      <!-- 支付结果 -->
      <template slot="paymentResult" slot-scope="{ row }">
        <el-tag v-if="row.paymentResult == 1" type="warning" size="small"
          >未支付</el-tag
        >
        <el-tag v-if="row.paymentResult == 2" type="success" size="small"
          >支付成功</el-tag
        >
        <el-tag v-if="row.paymentResult == 3" type="danger" size="small"
          >支付失败</el-tag
        >
      </template>
      <!-- 会员姓名 -->
      <template slot-scope="{ disabled, size }" slot="doctorNameForm">
        <el-select
          v-model="form.doctorName"
          @change="doctorNameChange"
          @focus="doctorNameFocus"
          filterable
          remote
          reserve-keyword
          placeholder="请选择会员姓名"
          :remote-method="remoteMethodProduct"
          :loading="doctorNameSelectLoading"
        >
          <el-option
            v-for="item in doctorNameOptions"
            :key="item.value"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </template>
      <template slot="menuLeft">
        <el-button
          type="primary"
          v-if="permission.walletAccountDetail_import"
          @click="importShow = true"
          >导 入
        </el-button>
        <el-button
          type="primary"
          v-if="permission.walletAccountDetail_exportWalletAccountr"
          @click="exportWalletAccountr()"
          >导 出
        </el-button>
      </template>
      <template slot="search" slot-scope="{ row, size }">
        <avue-form ref="form" :option="searchOption" v-model="query">
          <template slot="menuForm">
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="searchChange2"
              >搜 索</el-button
            >
            <el-button icon="el-icon-delete" @click="searchReset"
              >清 空</el-button
            >
          </template>
        </avue-form>
      </template>
    </avue-crud>
    <el-dialog
      title="导入个人结算明细支付结果"
      append-to-body
      :visible.sync="importShow"
      width="555px"
    >
      <avue-form
        :option="excelOption"
        v-model="excelForm"
        :upload-after="uploadAfter"
      >
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
      title="查看"
      @close="form = {}"
      append-to-body
      :visible.sync="viewDialogVisible"
      width="60%"
    >
      <avue-form :option="viewDialogOption" v-model="viewDialogForm">
        <template slot-scope="{ type, disabled }" slot="settlementOrderCode">
          <div
            class="to-view"
            @click="toDetail(viewDialogForm.settlementOrderId)"
            v-if="viewDialogForm.settlementOrderCode"
          >
            <a readonly>
              {{ viewDialogForm.settlementOrderCode }}
            </a>
          </div>
        </template>
        <template
          slot-scope="{ type, disabled }"
          slot="settlementOrderDoctorCode"
        >
          <div
            class="to-view"
            @click="toDetail2(viewDialogForm.settlementOrderDoctorId)"
            v-if="viewDialogForm.settlementOrderDoctorCode"
          >
            <a readonly>
              {{ viewDialogForm.settlementOrderDoctorCode }}
            </a>
          </div>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  remove,
  getDoctorList,
} from "@/api/walletaccountdetail/walletAccountDetail";
// import option from "@/const/walletaccountdetail/walletAccountDetail";
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
export default {
  data() {
    return {
      //查看
      viewDialogVisible: false,
      viewDialogForm: {},
      viewDialogOption: {
        disabled: true,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: "130",
        column: [
          {
            label: "业务流水号",
            prop: "code",
            type: "input",
          },
          {
            label: "企业结算单编号",
            prop: "settlementOrderCode",
            type: "input",

          },
          {
            label: "自然人结算单编号",
            prop: "settlementOrderDoctorCode",
            type: "input",
          },
          {
            label: "会员姓名",
            prop: "doctorName",
          },
          {
            label: "证件类型",
            prop: "credentialType",
            type: "select",
            dicData: [
              {
                label: "身份证",
                value: 1,
              },
              {
                label: "港澳通行证或居住证",
                value: 2,
              },
              {
                label: "台湾通行证或居住证",
                value: 3,
              },
              {
                label: "护照",
                value: 4,
              },
            ],
            value: 0,
          },
          {
            label: "客户手机号",
            prop: "phone",
            type: "input",
          },

          {
            label: "证件号",
            prop: "idCardNumber",
            type: "input",
            span: 12,
            viewDisplay: true, //详情时是否显示
            rules: [
              {
                required: true,
                message: "请输入证件号码",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "支付金额",
            prop: "paymentAmount",
            type: "input",
          },
          {
            label: "支付时间",
            prop: "paymentTime",
            type: "date",
            width: "150",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            rules: [
              {
                required: true,
                message: "请输入退款时间",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "支付凭证",
            prop: "paymentVoucher",
            type: "input",
          },

          {
            label: "支付结果",
            prop: "paymentResult",
            type: "select",
            dicData: [
              {
                label: "未支付",
                value: 1,
              },
              {
                label: "支付成功",
                value: 2,
              },
              {
                label: "支付失败",
                value: 3,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择支付结果",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },
      //end
      //会员姓名
      doctorNameSelectOption: {}, //选中的对象
      doctorNameOptions: [],
      doctorNameSelectLoading: false,
      importShow: false,
      searchOption: {
        menuSpan: 6,
        submitText: "搜索",
        submitIcon: "",
        emptyBtn: false,
        submitBtn: false,
        labelWidth: 120,
        column: [
          {
            label: "企业结算单编号",
            prop: "settlementOrderCode",
            span: 6,
            type: "input",
          },
          {
            dataType: "String",
            label: "会员姓名",
            prop: "doctorName",
            type: "input",
            span: 6,
          },
          {
            label: "会员手机号",
            prop: "phone",
            type: "input",
            span: 6,
          },

          {
            label: "证件号",
            prop: "idCardNumber",
            type: "input",
            span: 6,
          },
          {
            label: "支付时间",
            prop: "startDate",
            type: "daterange",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            span: 6,
          },

          {
            label: "支付结果",
            prop: "paymentResult",
            type: "select",
            search: true,
            span: 6,
            dicData: [
              {
                label: "未支付",
                value: 1,
              },
              {
                label: "支付成功",
                value: 2,
              },
              {
                label: "支付失败",
                value: 3,
              },
            ],
          },
        ],
      },
      query: {
        customerType: "",
        doctorName: "",
        paymentTime: [],
      },
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "支付明细表",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "数据上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
            },
            data: {
              isCovered: 1,
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action:
              "/api/blade-pay/doctorPaymentDetails/import-walletAccount?isCovered=1",
          },
          // {
          //   label: "模板下载",
          //   prop: "excelTemplate",
          //   formslot: true,
          //   span: 24,
          // },
        ],
      },
      excelForm: {},
      option: {
        searchBtn: false,
        emptyBtn: false,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        labelWidth: 150,
        searchMenuSpan: 6,
        border: true,
        index: true,
        addBtnIcon: " ",
        viewBtn: true,
        cancelBtnIcon: " ",
        saveBtnIcon: " ",
        delBtnIcon: " ",
        editBtnIcon: " ",
        viewBtnIcon: " ",
        updateBtnIcon: " ",
        selection: true,
        dialogClickModal: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "业务流水号",
            prop: "code",
            type: "input",
          },
          {
            label: "企业结算单编号",
            prop: "settlementOrderCode",
            type: "input",
            width: "150",
          },
          {
            label: "自然人结算单编号",
            prop: "settlementOrderDoctorCode",
            type: "input",
            width: "150",
          },
          {
            label: "会员姓名",
            prop: "doctorName",
          },
          {
            label: "证件类型",
            prop: "credentialType",
            type: "select",
            dicData: [
              {
                label: "身份证",
                value: 1,
              },
              {
                label: "港澳通行证或居住证",
                value: 2,
              },
              {
                label: "台湾通行证或居住证",
                value: 3,
              },
              {
                label: "护照",
                value: 4,
              },
            ],
            value: 0,
          },
          {
            label: "客户手机号",
            prop: "phone",
            type: "input",
          },

          {
            label: "证件号",
            prop: "idCardNumber",
            type: "input",
            span: 12,
            viewDisplay: true, //详情时是否显示
            rules: [
              {
                required: true,
                message: "请输入证件号码",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "支付金额",
            prop: "paymentAmount",
            type: "input",
          },
          {
            label: "支付时间",
            prop: "paymentTime",
            type: "date",
            width: "150",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            rules: [
              {
                required: true,
                message: "请输入退款时间",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "支付凭证",
            prop: "paymentVoucher",
            type: "input",
          },

          {
            label: "支付结果",
            prop: "paymentResult",
            type: "select",
            dicData: [
              {
                label: "未支付",
                value: 1,
              },
              {
                label: "支付成功",
                value: 2,
              },
              {
                label: "支付失败",
                value: 3,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择支付结果",
                trigger: ["blur", "change"],
              },
            ],
          },
        ],
      },

      form: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      // option: option,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.walletAccountDetail_add, false),
        viewBtn: this.vaildData(
          this.permission.walletAccountDetail_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.walletAccountDetail_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.walletAccountDetail_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  methods: {
    // 去详情
    toDetail(settlementOrderId) {
      this.$router.push({
        path: `/settlementOrderDoctor/detail/${settlementOrderId}`,
      });
    },
    toDetail2(id) {
      this.$router.push({
        path: `/settlementOrderDoctorDetailOut/detail/${id}`,
      });
    },
    uploadExceed() {
      this.$message.error("超出上传限制凭证数量");
    },
    //初始化数据
    initOptions() {
      this.doctorNameSelectLoading = true;
      getDoctorList().then((res) => {
        const data = res.data;
        this.doctorNameOptions = data.data;
        this.doctorNameSelectLoading = false;
      });
    },
    //涉及产品名称搜索
    remoteMethodProduct(query) {
      if (query !== "") {
        this.doctorNameSelectLoading = true;
        getDoctorList(query).then((res) => {
          const data = res.data;
          this.doctorNameOptions = data.data;
          this.doctorNameSelectLoading = false;
        });
      } else {
        this.initOptions();
      }
    },
    //获取焦点
    doctorNameFocus() {
      if (this.doctorNameOptions.length == 0) {
        this.initOptions();
      }
    },
    //会员姓名更改
    doctorNameChange(value) {
      let obj = this.doctorNameOptions.filter((item) => {
        return item.id == value;
      });
      this.form.idCode = obj[0].idCardNumber;
      this.doctorNameSelectOption = obj[0];
    },
    //会员姓名end
    uploadAfter(res, done, loading, column) {
      window.console.log(column);
      this.importShow = false;
      this.onLoad(this.page);
      done();
    },
    handleTemplate() {
      exportBlob(
        `/api/blade-pay/doctorPaymentDetails/export-template?${
          this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, `个人费用支付明细流水模板.xlsx`);
      });
    },

    exportWalletAccountr() {
      let _this = this;
      exportBlob(
        `/api/blade-pay/doctorPaymentDetails/export-walletAccountr?${
          this.website.tokenHeader
        }=${getToken()}&doctorName=${
          this.query.doctorName ? this.query.doctorName : ""
        }&startDate=${
          this.query.paymentTime.length > 0 ? this.query.paymentTime[0] : ""
        }&endDate=${
          this.query.paymentTime > 0 ? this.query.paymentTime[1] : ""
        }&settlementOrderCode=${
          this.query.settlementOrderCode ? this.query.settlementOrderCode : ""
        }&paymentResult=${
          this.query.paymentResult ? this.query.paymentResult : ""
        }`
      ).then((res) => {
        if (res.data.type === "application/json") {
          const reader = new FileReader();
          reader.readAsText(res.data, "utf-8");
          reader.onload = function () {
            const _res = JSON.parse(reader.result);
            _this.$message({
              type: "error",
              message: _res.msg,
            });
          };
        } else {
          downloadXls(res.data, `个人费用支付明细流水数据表.xlsx`);
        }
      });
    },

    rowSave(row, done, loading) {
      row.doctorId = this.doctorNameSelectOption.id;
      row.doctorName = this.doctorNameSelectOption.name;
      if (row.voucherUrl instanceof Array) {
        row.voucherUrl = row.voucherUrl.join(",");
      }
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          window.console.log(error);
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      if (!this.validatenull(this.doctorNameSelectOption)) {
        row.doctorId = this.doctorNameSelectOption.id;
        row.doctorName = this.doctorNameSelectOption.name;
      }
      if (row.voucherUrl instanceof Array) {
        row.voucherUrl = row.voucherUrl.join(",");
      }
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          loading();
          console.log(error);
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (type == "view") {
        getDetail(this.form.id).then((res) => {
          this.viewDialogVisible = true;
          this.viewDialogForm = res.data.data;
        });
      } else if ("edit" == type) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          done();
        });
      } else {
        done();
      }
    },
    searchReset() {
      this.$refs.form.resetForm();
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;

      //支付时间
      if (!this.validatenull(params.paymentTime)) {
        this.query.startDate = params.paymentTime[0];
        this.query.endDate = params.paymentTime[1];
        delete this.query.paymentTime;
      }
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    searchChange2() {
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
