export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "康缘会议id",
      prop: "meetingId",
      type: "input",
    },
    {
      label: "腾讯会议id",
      prop: "tencentMeetingId",
      type: "input",
    },
    {
      label: "会议code",
      prop: "meetingCode",
      type: "input",
    },
    {
      label: "录制文件 ID",
      prop: "meetingRecordId",
      type: "input",
    },
    {
      label: "主持人用户id",
      prop: "hostUserId",
      type: "input",
    },
    {
      label: "会议开始时间，UNIX 时间戳（单位毫秒）",
      prop: "mediaStartTime",
      type: "input",
    },
    {
      label: "会议主题",
      prop: "subject",
      type: "input",
    },
    {
      label: "录制状态：1：录制中3：转码完成当状态为转码完成才会返回录制文件列表",
      prop: "state",
      type: "input",
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
