export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchLabelWidth: "150",
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: false,
  editBtn: false,
  addBtn: false,
  viewBtnIcon: " ",
  addBtnIcon: " ",
  delBtn: false,
  dialogClickModal: false,
  menuWidth: 150,
  column: [
    {
      label: "客户成果单编号",
      prop: "resultOrderCode",
      type: "input",
      width: "140",
    },
    {
      label: "自然人成果编号",
      prop: "code",
      search: true,
      type: "input",
      width: "160",
    },
    {
      label: "客户名称",
      prop: "entrustedCompanyName",
      type: "input",
      search: true,
    },
    {
      label: "统一社会信用代码",
      prop: "socialCreditCode",
      type: "input",
      search: true,
      width: "165",
    },
    {
      label: "平台方",
      prop: "generalContractor",
      type: "input",
      span: 24,
      width: "140",
    },

    {
      label: "会员名称",
      prop: "doctorName",
      type: "input",
      search: true,
      width: "90",
    },

    {
      label: "证件号码",
      prop: "idCardNumber",
      type: "input",
      search: true,
    },
    {
      label: "业务期间",
      prop: "accountPeriod",
    },
    {
      label: "开始日期",
      prop: "actStartTime",
      type: "datetime",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      search: true,
      searchRange: true,
    },
    {
      label: "结束日期",
      prop: "actEndTime",
      type: "datetime",
      format: "yyyy-MM-dd",
      valueFormat: "yyyy-MM-dd",
      search: true,
      searchRange: true,
    },

    {
      label: "服务项目数量",
      prop: "checkNumber",
      type: "input",
      width: "110",
    },
    {
      label: "服务总金额",
      prop: "serviceResultAmount",
      type: "input",
    },
  ],
};
