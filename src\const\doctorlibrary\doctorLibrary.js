export default {
  addBtn: false,
  editBtn: false,
  viewBtnIcon: " ",
  delBtnIcon: " ",
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "姓名",
      prop: "name",
      type: "input",
      search: true,
      fixed: true,
    },
    {
      label: "手机号",
      prop: "phone",
      width: "110px",
      type: "input",
      search: true,
    },
    {
      label: "身份证号",
      prop: "idCardNumber",
      width: "160px",
      type: "input",
      search: true,
    },

    {
      label: "医院名称",
      prop: "hospitalName",
      width: "200px",
      type: "input",
    },
    {
      label: "一级科室",
      prop: "departmentName",
      width: "100px",
      type: "input",
    },
    {
      label: "职称",
      prop: "professional",
      width: "100px",
      type: "input",
    },
    {
      label: "职务",
      prop: "duty",
      width: "100px",
      type: "input",
    },
    {
      label: "性别",
      prop: "sex",
      type: "input",
    },
    {
      label: "民族",
      prop: "nationality",
      type: "input",
    },
    {
      label: "籍贯",
      prop: "nativePlace",
      type: "input",
    },
    {
      label: "讲者级别",
      prop: "speakerLevel",
      type: "input",
    },

    // {
    //   label: "身份证正反图片-sfe",
    //   prop: "idCardFileSfe",
    //   type: "input",
    // },
    // {
    //   label: "身份证正面",
    //   prop: "idCardPositive",
    //   type: "input",
    // },
    // {
    //   label: "身份证反面",
    //   prop: "idCardNegative",
    //   type: "input",
    // },
    // {
    //   label: "身份证有效期开始日期",
    //   prop: "idCardStartDate",
    //   type: "input",
    // },
    // {
    //   label: "身份证有效期结束日期",
    //   prop: "idCardEndDate",
    //   type: "input",
    // },
    // {
    //   label: "身份证地址",
    //   prop: "idCardAddress",
    //   type: "input",
    // },

    // {
    //   label: "医师认证资料sfe-多张",
    //   prop: "doctorCertificateSfe",
    //   type: "input",
    // },
    // {
    //   label: "医师认证资料元圈存储-多张",
    //   prop: "doctorCertificateData",
    //   type: "input",
    // },
    {
      label: "开户银行",
      prop: "bankName",
      width: "180px",
      type: "input",
    },
    {
      label: "银行账号",
      prop: "bankAccount",
      width: "180px",
      type: "input",
    },
    // {
    //   label: "银行卡图片-sfe",
    //   prop: "bankImgSfe",
    //   type: "input",
    // },
    // {
    //   label: "银行卡图片",
    //   prop: "bankImg",
    //   type: "input",
    // },
    // {
    //   label: "是否为标准银联卡Y/N",
    //   prop: "uniFlg",
    //   type: "input",
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   hide: true,
    // },
    // {
    //   label: "Y:招商银行N:非招商银行",
    //   prop: "bnkFlg",
    //   type: "input",
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   hide: true,
    // },
    {
      label: "ocr识别",
      prop: "idCardOcrResult",
      width: "100px",
      type: "select",
      search: true,
      fixed: "right",
      dicData: [
        {
          label: "成功",
          value: 1,
        },
        {
          label: "失败",
          value: 2,
        },
      ],
    },
    {
      label: "运营商三要素",
      prop: "carriersAuthResult",
      type: "select",
      search: true,
      fixed: "right",
      width: 110,
      dicData: [
        {
          label: "一致",
          value: "01",
        },
        {
          label: "不一致",
          value: "02",
        },
        {
          label: "不确定",
          value: "03",
        },
        {
          label: "失败",
          value: "04",
        },
      ],
    },
    {
      label: "银行卡三要素",
      prop: "cardAuthResult",
      type: "select",
      search: true,
      fixed: "right",
      width: 110,
      dicData: [
        {
          label: "一致",
          value: "01",
        },
        {
          label: "不一致",
          value: "02",
        },
        {
          label: "不确定",
          value: "03",
        },
        {
          label: "失败",
          value: "04",
        },
      ],
    },
    {
      label: "数据结果",
      prop: "dataStatus",
      type: "select",
      search: true,
      fixed: "right",
      dicData: [
        {
          label: "异常",
          value: 0,
        },
        {
          label: "成功",
          value: 1,
        },
      ],
    },
    {
      label: "异常备注",
      width: "220px",
      prop: "remark",
      type: "input",
      fixed: "right",
    },
  ],
};
