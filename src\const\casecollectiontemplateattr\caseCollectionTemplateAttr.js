export default {
  searchIndex: 3,
  searchIcon: true,
  searchShowBtn: false,
  columnBtn: false,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  delBtn: false,
  delBtnIcon: " ",
  editBtnIcon: " ",
  viewBtnIcon: " ",
  addBtnIcon: " ",
  cancelBtnIcon: " ",
  saveBtnIcon: " ",
  updateBtnIcon: " ",
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键id",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "属性编码",
      prop: "attrCode",
      type: "input",
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: "属性名称",
      prop: "attrName",
      type: "input",
      search: true,
      rules: [
        {
          required: true,
          message: "请输入属性名称",
          trigger: ["blur", "change"],
        },
      ],
    },
    {
      label: "属性类型",
      prop: "attrType",
      type: "select",
      rules: [
        {
          required: true,
          message: "请选择属性类型",
          trigger: ["blur", "change"],
        },
      ],
      dicData: [
        {
          label: "单选",
          value: 1,
        },
        {
          label: "多选",
          value: 2,
        },
        {
          label: "文本",
          value: 3,
        },
        {
          label: "图片",
          value: 4,
        },
      ],
    },
    {
      label: "属性值选项",
      prop: "attrValue",
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入属性值选项",
          trigger: ["blur"],
        },
      ],
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态 ",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "版本控制",
      prop: "version",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ],
};
