export default {
  rowKey: 'id',
  menuWidth: 100,
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  selection: true,
  dialogClickModal: false,
  lazy: true,
  column: [
    {
      label: "评论id 主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "评论内容",
      prop: "content",
      type: "input",
      search: true,
    },
    {
      label: "父主键",
      prop: "parentId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "内容id",
      prop: "contentId",
      type: "input",
      search: true,
    },
    {
      label: "所属内容",
      prop: "contentTitle",
      type: "input",
      search: true,
    },
    {
      label: "点赞量",
      prop: "likeNum",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "评论量",
      prop: "commentNum",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "发布人",
      prop: "createUserName",
      type: "input",
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "发布时间",
      prop: "createTime",
      type: "input",
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: "互动数据",
      prop: "interaction",
      type: "input",
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
