<template>
  <div>
    <basic-container>
      <el-row :gutter="20" style="display: flex; align-items: center">
        <el-col :span="10">
          <el-page-header @back="goBack" :content="$route.name">
          </el-page-header>
        </el-col>
        <el-col
          :span="14"
          v-if="$route.query.type != 'view'"
          style="text-align: right"
        >
        </el-col>
      </el-row>
    </basic-container>
    <basic-container>
      <avue-form
        v-if="!validatenull(form)"
        ref="form"
        :option="option"
        v-model="form"
        @submit="submit"
      >
        <template slot-scope="{}" slot="orderDoctorCode">
          <div class="to-view" @click="toOrderDoctor()">
            <a readonly>
              {{ form.orderDoctorCode }}
            </a>
          </div>
        </template>
      </avue-form>
      <template v-else>
        <el-skeleton :rows="10" animated />
      </template>
    </basic-container>
  </div>
</template>

<script>
import { getDetail } from "@/api/orderdoctosettlementpoint/orderDoctorSettlementPoint";

import { mapGetters } from "vuex";
export default {
  data() {
    return {
      //弹窗

      id: "",
      form: {},
      option: {
        disabled: true,
        submitBtn: false,
        emptyBtn: false,
        labelWidth: "170",
        group: [
          {
            label: "基本信息",
            prop: "baseInfo",
            column: [
              {
                label: "结算单编号",
                prop: "code",
                type: "input",
              },
              {
                label: "会员订单编号",
                prop: "orderDoctorCode",
                type: "input",
              },
              {
                label: "服务收入性质",
                prop: "natureOfIncome",
                type: "select",
                search: true,
                dicData: [
                  {
                    label: "经营所得",
                    value: 1,
                  },
                  {
                    label: "劳务报酬",
                    value: 2,
                  },
                ],
              },
              {
                label: "服务活动类型",
                prop: "activityPlanType",
                type: "select",
                search: true,
                dicData: [
                  {
                    label: "学术会议",
                    value: 1,
                  },
                  {
                    label: "知识创作",
                    value: 2,
                  },
                  {
                    label: "知识传播",
                    value: 3,
                  },
                  {
                    label: "临床调研",
                    value: 4,
                  },
                  {
                    label: "用药反馈",
                    value: 5,
                  },
                  {
                    label: "专业评审",
                    value: 6,
                  },
                  {
                    label: "病例征集",
                    value: 8,
                  },
                ],
              },

              {
                label: "会员姓名",
                prop: "doctorName",
                type: "input",
                search: true,
              },
              {
                label: "会员手机号",
                prop: "doctorPhone",
                type: "input",
                search: true,
              },
              {
                label: "身份证号",
                prop: "doctorIdCard",
                type: "input",
              },
              {
                label: "结算金额",
                prop: "paymentAmount",
                type: "input",
              },
              {
                label: "是否申请打款",
                prop: "isApplyPayment",
                type: "select",
                dicData: [
                  {
                    label: "未申请",
                    value: 0,
                  },
                  {
                    label: "已申请待经办",
                    value: 1,
                  },
                  {
                    label: "支付成功",
                    value: 2,
                  },
                  {
                    label: "支付失败",
                    value: 3,
                  },
                  {
                    label: "已作废",
                    value: 4,
                  },
                ],
              },
              {
                label: "是否纳税申报",
                prop: "isTaxDeclaration",
                type: "select",
                dicData: [
                  {
                    label: "未申报",
                    value: 0,
                  },
                  {
                    label: "已申报",
                    value: 1,
                  },
                ],
              },
              {
                label: "支付时间",
                prop: "paymentTime",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
              },
              {
                label: "纳税申报单编号",
                prop: "taxReturnCode",
                type: "input",
              },
            ],
          },
        ],
      },

      query: {},
      loading: false,

      data: [],
    };
  },
  created() {
    this.id = this.$route.params.id;
    this.getDetail();
  },
  computed: {
    ...mapGetters(["permission", "createEvidenceFileStatus"]),
  },
  methods: {
    toOrderDoctor() {
      this.$router.push({
        path: `/orderdoctor/detail/${this.form.orderDoctorId}`,
      });
    },
    // 预览
    preview(url) {
      if (url) {
        this.$router.push({
          path: `/preview/preview`,
          query: {
            pdfUrl: url,
          },
        });
      } else {
        this.$message.error("文件为空无法预览");
      }
    },
    goBack() {
      this.$router.$avueRouter.closeTag();
      this.$router.go(-1);
    },

    getDetail() {
      let _this = this;
      getDetail(this.id).then((res) => {
        if (res.data.success) {
          _this.form = res.data.data;
        } else {
          _this.$message.error(res.data.msg);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.avue-crud > h2 {
  font-size: 16px !important;
  font-weight: 500 !important;
}
</style>
