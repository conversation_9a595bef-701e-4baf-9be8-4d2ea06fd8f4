<template>
  <basic-container>
    <el-tabs v-model="customerType" @tab-click="handleClick">
      <el-tab-pane
        v-if="permission.customerCertificationRecord_audit_yishi"
        label="自然人会员认证审核记录"
        name="3"
      >
        <avue-crud
          :option="option3"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :search.sync="search"
          :permission="permissionList"
          :before-open="beforeOpen"
          v-model="form"
          ref="crud"
          @row-del="rowDel"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoad"
        >
          <template slot="auditStatus" slot-scope="{ row }">
            <el-tag v-if="row.auditStatus == 1" type="warning" size="small"
              >待审</el-tag
            >
            <el-tag v-if="row.auditStatus == 2" type="success" size="small"
              >通过</el-tag
            >
            <el-tag v-if="row.auditStatus == 3" type="danger" size="small"
              >驳回</el-tag
            >
          </template>
          <template slot="isFaceVerify" slot-scope="{ row }">
            <el-tag v-if="row.isFaceVerify == 0" type="warning" size="small"
              >未提交</el-tag
            >
            <el-tag v-if="row.isFaceVerify == 1" type="success" size="small"
              >已提交</el-tag
            >
          </template>
          <!-- 操作栏模块 -->
          <template slot-scope="{ row }" slot="menu">
            <el-button
              v-if="permission.customerCertificationRecord_view"
              type="text"
              @click="toView(row)"
              >查看</el-button
            >
          </template>
        </avue-crud>
      </el-tab-pane>
      <el-tab-pane
        v-if="permission.customerCertificationRecord_audit_xuqiufang"
        label="企业会员认证审核记录"
        name="2"
      >
        <avue-crud
          :option="option2"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :search.sync="search"
          :permission="permissionList"
          :before-open="beforeOpen"
          v-model="form"
          ref="crud"
          @row-del="rowDel"
          @search-change="searchChange"
          @search-reset="searchReset"
          @selection-change="selectionChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoad"
        >
          <template slot="menuLeft">
            <!-- <el-button type="danger"  icon="el-icon-delete" plain v-if="permission.customerCertificationRecord_delete" @click="handleDelete">删 除</el-button> -->
          </template>
          <template slot="auditStatus" slot-scope="{ row }">
            <el-tag v-if="row.auditStatus == 1" type="warning" size="small"
              >待审</el-tag
            >
            <el-tag v-if="row.auditStatus == 2" type="success" size="small"
              >通过</el-tag
            >
            <el-tag v-if="row.auditStatus == 3" type="danger" size="small"
              >驳回</el-tag
            >
          </template>
          <!-- 操作栏模块 -->
          <template slot-scope="{ row }" slot="menu">
            <el-button
              v-if="permission.customerCertificationRecord_view"
              type="text"
              @click="toView(row)"
              >查看</el-button
            >
          </template>
        </avue-crud>
      </el-tab-pane>
    </el-tabs>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  remove,
} from "@/api/customercertificationrecord/customerCertificationRecord";
import {
  option2,
  option3,
} from "@/const/customercertificationrecord/customerCertificationRecord";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      customerType: "3",
      form: {},
      query: {},
      loading: true,
      search: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option2: option2,
      option3: option3,
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permission.customerCertificationRecord_add,
          false
        ),
        viewBtn: this.vaildData(
          this.permission.customerCertificationRecord_view,
          false
        ),
        delBtn: this.vaildData(
          this.permission.customerCertificationRecord_delete,
          false
        ),
        editBtn: this.vaildData(
          this.permission.customerCertificationRecord_edit,
          false
        ),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    if (!this.validatenull(window.sessionStorage.getItem("customerType"))) {
      this.customerType = window.sessionStorage.getItem("customerType");
      window.sessionStorage.removeItem("customerType");
    }
    if (
      !this.validatenull(
        window.sessionStorage.getItem("customercertificationrecordSearch")
      )
    ) {
      this.search = JSON.parse(
        window.sessionStorage.getItem("customercertificationrecordSearch")
      );
      this.query = JSON.parse(
        window.sessionStorage.getItem("customercertificationrecordSearch")
      );
      window.sessionStorage.removeItem("customercertificationrecordSearch");
    }
  },
  //组件销毁
  beforeDestroy() {
    if (!this.validatenull(this.query)) {
      window.sessionStorage.setItem(
        "customercertificationrecordSearch",
        JSON.stringify(this.query)
      );
    }
  },
  methods: {
    toView(row) {
      if (this.customerType == 3) {
        this.$router.push({
          path: `/customercertificationrecord/authenticationDoctorDetail/${row.id}`,
        });
      } else if (this.customerType == 2) {
        this.$router.push({
          path: `/customercertificationrecord/detail/${row.id}`,
        });
      }
    },

    handleClick() {
      this.query = {};
      this.data = [];
      this.page.currentPage = 1;
      this.$refs.crud.searchReset();
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then((res) => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.customerType = this.customerType;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  },
};
</script>

<style></style>
