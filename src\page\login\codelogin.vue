<template>
  <el-form
    class="login-form"
    :rules="loginRules"
    ref="loginForm"
    :model="loginForm"
    label-width="0"
  >
    <el-form-item prop="phone">
      <el-input
        @keyup.enter.native="handleLogin"
        v-model="loginForm.phone"
        auto-complete="off"
        :placeholder="$t('login.phone')"
      >
      </el-input>
    </el-form-item>

    <el-form-item prop="code">
      <el-input
        @keyup.enter.native="handleLogin"
        v-model="loginForm.code"
        auto-complete="off"
        prefix-icon="el-icon-lock"
        :placeholder="$t('login.code')"
      >
        <template slot="suffix">
          <span
            @click="handleSend"
            class="msg-text"
            :class="[{ display: msgKey }]"
            >{{ msgText }}</span
          >
        </template>
      </el-input>
    </el-form-item>
    <!-- <div style="height: 10px"></div> -->
    <el-form-item label="">
      <slider-check
        ref="refSlider"
        :key="menuKey"
        @changeState="changeState"
      ></slider-check>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        @click.native.prevent="handleLogin"
        class="login-submit"
        >{{ $t("login.submit") }}</el-button
      >
    </el-form-item>
    <div class="links">
      <!-- <span style="color: #999">登录即表示已阅读并同意</span>
      <span @click="openAgreement" style="color: rgb(29, 144, 161)">《平台协议》</span>
      <span style="color: #999">和</span>
      <span @click="openPrivacy" style="color: rgb(29, 144, 161)">《隐私政策》</span>
      <a
        target="_blank"
        href="https://xyt.xcc.cn/getpcInfo?sn=1402072400684933120&certType=8&url=*.fengchaozb.cn&language=CN"
        ><img
          src="https://program.xinchacha.com/xyt/xcc_small_ssl.png"
          style="width: 15px; highet: 15px; position: absolute; top: 0px"
      /></a> -->
      <el-checkbox v-model="checked" style="color: #999">
        登录即表示您已阅读并同意
        <span @click="openAgreement" style="color: #6ba2d6">《平台协议》</span>
        和
        <span @click="openPrivacy" style="color: #6ba2d6">《隐私政策》</span>
        <a
          target="_blank"
          href="https://xyt.xcc.cn/getpcInfo?sn=1402072400684933120&certType=8&url=*.fengchaozb.cn&language=CN"
          ><img
            src="https://program.xinchacha.com/xyt/xcc_small_ssl.png"
            style="width: 15px; highet: 15px; position: absolute; top: 0px"
        /></a>
      </el-checkbox>
    </div>
  </el-form>
</template>

<script>
import { isvalidatemobile } from "@/util/validate";
import { mapGetters } from "vuex";
import { sendSms } from "@/api/user";
import sliderCheck from "./slidingBlock.vue";
export default {
  components: {
    sliderCheck,
  },
  name: "codelogin",
  data() {
    const validatePhone = (rule, value, callback) => {
      if (isvalidatemobile(value)[0]) {
        callback(new Error(isvalidatemobile(value)[1]));
      } else {
        callback();
      }
    };
    const validateCode = (rule, value, callback) => {
      if (value.length !== 6) {
        callback(new Error("请输入6位数的验证码"));
      } else {
        callback();
      }
    };
    return {
      randCode: "",
      menuKey: 1,
      login_model_tel: false,
      checked: true,
      msgText: "",
      msgTime: "",
      msgKey: false,
      loginForm: {
        phone: "",
        code: "",
      },
      loginRules: {
        phone: [{ required: true, trigger: "blur", validator: validatePhone }],
        code: [{ required: true, trigger: "blur", validator: validateCode }],
      },
    };
  },
  created() {
    this.msgText = this.config.MSGINIT;
    this.msgTime = this.config.MSGTIME;
  },
  mounted() {},
  computed: {
    ...mapGetters(["tagWel"]),
    config() {
      return {
        MSGINIT: this.$t("login.msgText"),
        MSGSCUCCESS: this.$t("login.msgSuccess"),
        MSGTIME: 60,
      };
    },
  },
  props: [],
  methods: {
    changeState(tag) {
      if (tag) {
        this.login_model_tel = true;
      } else {
        this.login_model_tel = false;
      }
    },
    openAgreement() {
      window.open(`https://www.yuancycle.com/agreement.html`);
    },
    openPrivacy() {
      window.open(`https://www.yuancycle.com/privacy.html`);
    },

    handleSend() {
      //需要单独验证的字段名phone
      this.$refs.loginForm.validateField("phone", (val) => {
        if (!val) {
          if (!this.login_model_tel) {
            return this.$message({
              type: "warning",
              message: "请先滑块验证",
            });
          }
          if (this.msgKey) return;
          this.msgText = this.msgTime + this.config.MSGSCUCCESS;
          this.msgKey = true;
          this.randCode = Math.floor(Math.random() * (9999 - 1000)) + 1000;
          // console.log(this.randCode);
          sendSms(this.randCode, this.loginForm.phone).then((res) => {
            if (res.data.success) {
              this.$message({
                type: "success",
                message: "验证码已发送,请注意查收！",
              });
            } else {
              this.$message({
                type: "error",
                message: res.data.msg,
              });
            }
          });
          const time = setInterval(() => {
            this.msgTime--;
            this.msgText = this.msgTime + this.config.MSGSCUCCESS;
            if (this.msgTime === 0) {
              this.msgTime = this.config.MSGTIME;
              this.msgText = this.config.MSGINIT;
              this.msgKey = false;
              clearInterval(time);
            }
          }, 1000);
        } else {
          console.log("不通过");
          return false;
        }
      });
    },
    // handleLogin() {
    //   this.$refs.loginForm.validate(valid => {
    //     if (valid) {
    //       this.$store.dispatch("LoginByPhone", this.loginForm).then(() => {
    //         this.$router.push({ path: this.tagWel.value });
    //       });
    //     }
    //   });
    // }
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          if (!this.checked) {
            this.$message({
              type: "error",
              duration: "1500",
              message: "请先阅读平台协议和隐私政策再勾选同意!",
            });
            return;
          } else {
            for (let i in this.loginForm) {
              this.loginForm[i] = this.loginForm[i].trim();
            }
            this.loginForm.randCode = this.randCode;
            this.loginForm.tenantId = "000000";
            this.loginForm.mobile = this.loginForm.phone;
            // console.log(this.loginForm)
            this.$store.dispatch("LoginByPhone", this.loginForm).then(() => {
              this.$router.push({ path: this.tagWel.value });
            });
          }
        }
      });
    },
  },
};
</script>

<style scoped>
.msg-text {
  display: block;
  min-width: 60px;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
}
.msg-text.display {
  color: #ccc;
}
</style>
