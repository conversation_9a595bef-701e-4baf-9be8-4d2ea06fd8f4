import request from "@/router/axios";

export const getList = (current, size, params) => {
  return request({
    url: "/api/blade-act/activityServiceProject/list",
    method: "get",
    params: {
      ...params,
      current,
      size,
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: "/api/blade-act/activityServiceProject/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const remove = (ids) => {
  return request({
    url: "/api/blade-act/activityServiceProject/remove",
    method: "post",
    params: {
      ids,
    },
  });
};

export const add = (row) => {
  return request({
    url: "/api/blade-act/activityServiceProject/submit",
    method: "post",
    data: row,
  });
};

export const update = (row) => {
  return request({
    url: "/api/blade-act/activityServiceProject/submit",
    method: "post",
    data: row,
  });
};

//获取涉及产品列表
export const getProductList = (productName) => {
  return request({
    url: "/api/blade-sys/baseProduct/getList",
    method: "get",
    params: {
      productName,
    },
  });
};

//通过产品获取问卷调研模板
export const listByProductId = (productId) => {
  return request({
    url: "/api/blade-act/clinicalResearchTemplate/listByProductId",
    method: "get",
    params: {
      productId,
    },
  });
};

export const getClinicalCooperation = (id) => {
  return request({
    url: "/api/blade-act/activityProjectType/getClinicalCooperation",
    method: "get",
    params: {
      id,
    },
  });
};
