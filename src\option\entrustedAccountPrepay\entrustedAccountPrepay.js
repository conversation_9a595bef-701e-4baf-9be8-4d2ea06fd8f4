export default {
  searchIndex: 3,
  height:'auto',
  calcHeight: 30,
  labelWidth: 150,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  searchLabelWidth: "120",
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    // {
    //   label: "企业id",
    //   prop: "entrustedCompanyId",
    //   type: "input",
    // },
    // {
    //   label: "企业资金账户id",
    //   prop: "entrustedAccountId",
    //   type: "input",
    // },
    {
      label: "预付单编号",
      prop: "code",
      type: "input",
      search: true,
    },
    {
      label: "客户名称",
      prop: "entrustedCompanyName",
      type: "input",
      search: true,
    },
    {
      label: "支付方式",
      prop: "paymentType",
      type: "select",
      dicData: [
        {
          label: "线下",
          value: 1,
        },
        {
          label: "线上",
          value: 2,
        },
      ],
    },
    {
      label: "记账类型",
      prop: "chargeType",
      type: "select",
      dicData: [
        {
          label: "收入",
          value: 1,
        },
        {
          label: "支出",
          value: 2,
        },
      ],
    },
    {
      label: "记账金额",
      prop: "recordAmount",
      type: "input",
    },
    {
      label: "预付时间",
      prop: "prepayTime",
      type: "input",
    },
    {
      label: "记账凭证",
      prop: "recordVoucher",
      type: "input",
    },
    {
      label: "状态",
      prop: "confirmStatus",
      type: "select",
      dicData: [
        {
          label: "待确认",
          value: 0,
        },
        {
          label: "已确认",
          value: 1,
        },
        {
          label: "已拒绝",
          value: 2,
        },
      ],
    },
    // {
    //   label: "记账业务描述：充值/结算单支付",
    //   prop: "recordDesc",
    //   type: "input",
    // },
    // {
    //   label: "付款银行",
    //   prop: "payerBankName",
    //   type: "input",
    // },
    // {
    //   label: "付款账号",
    //   prop: "payerBankAccount",
    //   type: "input",
    // },
    // {
    //   label: "收款银行",
    //   prop: "payeeBankName",
    //   type: "input",
    // },
    // {
    //   label: "收款账号",
    //   prop: "payeeBankAccount",
    //   type: "input",
    // },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    // {
    //   label: "版本控制",
    //   prop: "version",
    //   type: "input",
    // },
  ]
}
